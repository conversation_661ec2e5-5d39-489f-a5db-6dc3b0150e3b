from datetime import datetime, timedelta
from django.shortcuts import render
from rest_framework import status

# Create your views here.
from rest_framework.viewsets import GenericViewSet
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from helpers.custom_permissions import IsAdmin

from user_profiles.models import Company
from accounts.models import AccountDetail, TransactionDetail
from helpers.custom_pagination import PaginationClass
from helpers.custom_responses import Response
from accounts import models, serializers
from helpers import enums

class TransactionDetailsViewsAPI(GenericViewSet):
    
    pagination_class = PaginationClass
    permission_classes = [IsAuthenticated, IsAdmin]
    
    def get_queryset(self):
        transactions = TransactionDetail.objects.all().order_by("-created_at")
        
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        company = self.request.query_params.get('company', None)
        status = self.request.query_params.get('status', None)
        
        if company:
            transactions = transactions.filter(company__name=company)
            
        if start_date and end_date:
            try:
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            except ValueError:
                raise ValueError("Invalid date format. Must be in 'YYYY-MM-DD' or 'DD-MM-YYYY' format.")
            
            transactions = transactions.filter(created_at__range=[start_date, end_date])
            
        if status:
            transactions = transactions.filter(transaction_status=status)
            
        return transactions
    
    
    def list(self, request):
        try:
            queryset = self.get_queryset()
        except Exception as err:
            return Response(
                errors=str(err),
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
            
        paginate_queryset = self.paginate_queryset(queryset)
        
        transaction_serializer = serializers.TransactionSerializer(paginate_queryset, many=True)
        paginated_resp = self.get_paginated_response(data=transaction_serializer.data)
        
        return Response(
                data={
                    "transaction": paginated_resp.data,
                },
                status_code=200,
                status=status.HTTP_200_OK,
            )
        
        
class CompanyAccountDetails(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]
    
    def get(self, request):
        
        companies = Company.objects.all()
        transactions = TransactionDetail.objects.all().order_by("-created_at")
        company = self.request.query_params.get('company', None)
        
        if company is not None:
            account = AccountDetail.objects.filter(company__name=company)
            if not account:
                return Response(
                    errors="Company does not exist",
                    status_code=404,
                    status=status.HTTP_404_NOT_FOUND
                )
            
            transactions = transactions.filter(company__name=company)
            
            provider_balances = []
            inflows = [item.amount for item in transactions if item.transaction_type == "CREDIT" and item.transaction_status == "SUCCESSFUL"]
            outflows = [item.amount for item in transactions if item.transaction_type == "DEBIT" and item.transaction_status == "SUCCESSFUL"]
    
            balance = [bal.cash_balance for bal in account]
            company_data = {
                "company_name": account[0].company.name,
                "balance": sum(balance),
                "cash_flow": {
                    "total_inflow": sum(inflows),
                    "total_outflow": sum(outflows)
                } 
            }
            
            for provider in enums.ServiceProvider:
                try:
                    account_balances = account.get(provider=provider.value)
                    data = {"provider": provider.value, "balances": account_balances.cash_balance}
                except AccountDetail.DoesNotExist:
                    data = {"provider": provider.value, "balances": "N/A"}
                provider_balances.append(data)
                
            return Response(
                data={
                    "company": company_data,
                    "provider_balances": provider_balances
                },
                status_code=200,
                status=status.HTTP_200_OK,
            )
            
        else:
            company_details = []
            company_data = {}
            companies = Company.objects.all()
            
            for company in companies:
                account_details = AccountDetail.objects.filter(company=company)
                account_balances = [bal.cash_balance for bal in account_details]
                
                inflows = [
                    item.amount for item in transactions if item.transaction_type == "CREDIT" 
                    and item.transaction_status == "SUCCESSFUL" and item.company == company
                ]
                outflows = [
                    item.amount for item in transactions if item.transaction_type == "DEBIT" 
                    and item.transaction_status == "SUCCESSFUL" and item.company == company
                ]
                
                if company.name not in company_data:
                    company_data[company.name] = { # Uses the company name as key
                        "company_name": company.name,
                        "balance": sum(account_balances),
                        "cash_flow": {
                            "total_inflow": sum(inflows),
                            "total_outflow": sum(outflows)
                        }
                    }
                else:
                    company_data[company.name]["balance"] += sum(account_balances)
                    company_data[company.name]["cash_flow"]["total_inflow"] += sum(inflows)
                    company_data[company.name]["cash_flow"]["total_outflow"] += sum(outflows)

            company_details = list(company_data.values()) # Extracts the company name and allows only the dicts as values of the list.
            
            inflows = [item.amount for item in transactions if item.transaction_type == "CREDIT" and item.transaction_status == "SUCCESSFUL"]
            outflows = [item.amount for item in transactions if item.transaction_type == "DEBIT" and item.transaction_status == "SUCCESSFUL"]
            balance = [bal.cash_balance for bal in account_details]
            
            overall = {
                "cash_flow": {
                     "total_inflow": sum(inflows),
                    "total_outflow": sum(outflows)
                },
                "balance": sum(balance)
            }
            
        return Response(
            data={
                "companies": company_details,
                "overall": overall
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )

