# [LibertyTechX - Core Banking Service](https://github.com/LibertytechX/corebanking)

An API Service for collection Account and NIP transfers.

## Technologies

* [Python 3.9](https://python.org) : Base programming language for development
* [PostgreSQL](https://www.postgresql.org/) : Application relational databases for development, staging, and production environments
* [Django Framework](https://www.djangoproject.com/) : Development framework used for the application
* [Django Rest Framework](https://www.django-rest-framework.org/) : Provides API development tools for easy API development
* [Github Actions](https://docs.github.com/en/free-pro-team@latest/actions) : Continuous Integration
* [Redis](https://redis.io/docs/getting-started/) : An in-memory data structure store, cache, and message broker
* [Celery](https://docs.celeryq.dev/en/stable/) : Task queues used as a mechanism to distribute work across threads or machines
* [Celery Beat](https://docs.celeryq.dev/en/stable/userguide/periodic-tasks.html) : A scheduler for periodic tasks

## Description

Your one-stop banking solution to receive and send money!

### Getting Started

Getting started with this project is very simple, all you need is to have Python, Git, and a Code Editor installed on your machine. Then open up your terminal and run this command `<NAME_EMAIL>:LibertytechX/corebanking.git` to clone the project repository.

Change directory into the project folder `cd corebanking` (if not in the directory already), after which you can choose to create a virtual environment first before installing the project requirements using the command `pip install -r requirements.txt`.

### Basic Project Commands

* Start Celery worker `celery -A config.celery worker --loglevel=INFO --concurrency 1 -P solo`

* Start Celery beat `celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler`

#### Additional Information

* Useful database constraints:

  * accounts
    *
  * wema_bank
    *
  * zenith_bank


### Developing With Docker
- Copy the `sample.env.txt` to `.env`
  ```sh
    cp sample.env.txt .env
  ```
- Update the database credentials with the following
  ```txt
      - POSTGRES_DB=devdb
      - POSTGRES_USER=devuser
      - POSTGRES_PASSWORD=changeme
      - POSTGRES_HOST=corebanking_db
  ```
- Update the `ENVIRONMENT` value to `container`, like so 
  ```
  ENVIRONMENT=container
  ```
- Spin up your docker container
  - Build the main app
  ```sh
  docker-compose build app
  ```
  - Run up to start all other services
  ```sh
  docker-compose up
  ```
- Steps to debug if encounter any issue while spinning up the container
  - verify if `celery` and `celery-beat` services in the `docker-compose.yml` has the correct image
  - run `docker images` to check the corebanking image name like so
    ```
    (corebvenv) λ MACs-MBP corebanking → λ git ayo/dev* → docker images 
    REPOSITORY                                        TAG         IMAGE ID       CREATED             SIZE
    corebanking-app                                   latest      c0108be25d72   About an hour ago   654MB
    ```
  - if `corebanking-app` not same as the `image: corebanking-app` update accordingly and restart up command `docker-compose up`
  Note: This is just reusability here, reusing the main app image for the celery services so we won't have too many redundant image eating up space on our PC.


  #### Steps to create a superuser from your container
- Run `docker ps` to check your main app container name
    Mine is `corebanking-app-1` as will yours
    ```sh
        (corebvenv) λ MACs-MBP corebanking → λ git ayo/dev* → docker ps               
          CONTAINER ID   IMAGE                COMMAND                   CREATED        STATUS         PORTS                    NAMES
          ba79ef379d4d   corebanking-app      "sh -c '\n      pytho…"   22 hours ago   Up 4 seconds   0.0.0.0:8000->8000/tcp   corebanking-app-1
    ```
- Run the following command, to start an interactive shell in the container
```sh
docker exec -it corebanking-app-1 sh
```
```sh
(corebvenv) λ MACs-MBP corebanking → λ git ayo/dev* → docker exec -it corebanking-app-1 sh
/app # 
```
- Run the django create superuser command, like so
```sh
/app # python manage.py createsuperuser
Email: <EMAIL>
Password: 
Password (again): 
This password is too short. It must contain at least 8 characters.
Bypass password validation and create user anyway? [y/N]: y
Superuser created successfully.
```
Boom!, There you have it, you can now start developing with docker

If encounter any Issue and the steps to resolve not here, while setting up the docker environment on your PC, please create an Issue here with probably a tag `chore` and assign to `Ayobami6`. Thank you