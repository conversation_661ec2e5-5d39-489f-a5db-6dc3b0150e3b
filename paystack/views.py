from django.shortcuts import render

# Create your views here.
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView

from paystack.services import PayStackManager

class AccountInquiryView(APIView):

    """
    Paystack Integration to verify an account number
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        
        account = request.query_params.get("account_number")
        bank_code = request.query_params.get("bank_code")
        
        if account is None or bank_code is None:
            raise Exception("Account Number and Bank Code Required.")

        response = PayStackManager.resolve_account_number(
            account_number=account, bank_code=bank_code
        )
        if response.get("status"):
            account_number = response.get("data", {}).get("account_number", {})
            account_name = response.get("data", {}).get("account_name", {})
            account_details = {
                "account_number": account_number,
                "account_name": account_name
            }
            return Response(
                data={"status": True, "data": account_details},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                data={"status": False, "error": response.get("message")},
                status=status.HTTP_200_OK,
            )
