import json
import requests
from django.conf import settings


class PayStackManager:
    BASE_URL = settings.PAYSTACK_BASE_URL
    HEADERS = {
        "Authorization": f"Bearer {settings.PAYSTACK_SECRET_KEY}",
        'Content-Type': 'application/json'
    }

    
    @classmethod
    def resolve_account_number(cls, account_number, bank_code):
        url = f"{cls.BASE_URL}/bank/resolve?account_number={account_number}&bank_code={bank_code}"
        request = cls._handle_request("GET", url=url)
        response = cls._handle_response(request) 
        return response
    

    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {}
    ):
        """
        Helper method to handle API requests and responses
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=cls.HEADERS)

            data = {
                "response_body": response.json(),
                "status_code": response.status_code,
                "status": "success"
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "response_body": response.text,
                "status_code": response.status_code,
                "status": "failed",
                "error": str(err)
            }
            return data


    @classmethod
    def _handle_response(
        cls,
        response: dict
    ):
        status_code = response.get("status_code")
        status = response.get("status")
        if status == "success" and status_code == 200:
            resp = response.get("response_body", {}).get("data", {})
            data = {
                "status": True,
                "message": "Successful",
                "data": resp
            }
        elif status == "success":
            message = response.get("response_body", {}).get("message", {})
            data = {
                "status": False,
                "message": message,
            }
        else:
            message = response.get("response_body", {})
            data = {
                "status": False,
                "message": message,
            }  
        return data