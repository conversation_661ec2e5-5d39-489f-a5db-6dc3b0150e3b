# Create your reusable function(s) and object(s) here.
import ast
import json


def generate_company_account_number():
    """
    This function generates a unique account number for a company by
    counting the existing AccountDetail objects and adding 1 to the count.
    The generated account number is constructed by adding the count to a base number.
    Returns:
        str: A unique account number for the company.
    """
    from ..models import AccountDetail

    count = AccountDetail.objects.all().count()
    account_number = ********** + (count + 1)
    return str(account_number)


def parse_to_dict(data):
    """
    Parse input data to a Python dictionary.

    Args:
        data (str | dict): Input data which could be a JSON string or dictionary.

    Returns:
        dict: Parsed dictionary.

    Raises:
        ValueError: If the input is neither a dictionary nor a JSON string.
    """
    # print(type(data))
    if isinstance(data, dict):
        # Already a dictionary, return it as is.
        return data
    elif isinstance(data, str):
        try:
            # Attempt to convert the JSON string to a dictionary.
            return json.loads(data)
        except json.JSONDecodeError as e:
            try:
                return ast.literal_eval(data)
            except (ValueError, SyntaxError) as e:
                raise ValueError(f"Invalid JSON string: {e}")
    else:
        # Raise error for unsupported types.
        raise ValueError("Input data must be a dictionary or a JSON string.")
