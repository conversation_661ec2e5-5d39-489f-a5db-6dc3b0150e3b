import json

from django.db.models.signals import post_save
from django.dispatch import receiver

from accounts import models
from accounts.tasks import mqtt_inflow_event


# Register your signal(s) here.
@receiver(post_save, sender=models.TransactionDetail)
def send_inflow_event_transaction(sender, instance, created, **kwargs):
    if created:
        mqtt_inflow_event.delay(
            account_number=instance.beneficiary_account_number,
            title="MONEY IN",
            message=json.dumps({
                "amount": f"{instance.amount_payable}",
                "reference": f"{instance.session_id}",
                "narration": f"{instance.narration}",
                "payer_name": f"{instance.source_name}",
                "payer_bank": f"{instance.bank_name}",
            }),
        )
