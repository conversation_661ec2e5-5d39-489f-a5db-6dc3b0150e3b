from datetime import datetime
import uuid

from django.conf import settings
from django.contrib import admin
from django.db.models.query import QuerySet
from import_export.admin import ImportExportModelAdmin
import pytz

from accounts import models, resources
from accounts.tasks import (
    outward_transfer_verification_handler,
    outward_transfer_verification_handler_two,
)
from accounts.utils import wema_funds_transfer
from helpers.enums import (
    AccountType,
    ServiceProvider,
    TransactionStatus,
)
from wema_bank.models import VirtualAccount, ConstantVariable
from wema_bank.tasks import send_company_transaction_callbacks


# Register your model(s) here.
class CompanyAccountBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CompanyAccountBalanceResource
    autocomplete_fields = []
    search_fields = [
        "company__email",
        "company__name",
    ]
    list_filter = [
        "created_at",
        "company",
        "provider",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AccountDetailResource
    autocomplete_fields = [
        "company",
        "sub_company",
    ]
    search_fields = [
        "account_name",
        "account_number",
        "company__email",
        "sub_company__company_email",
    ]
    list_filter = [
        "created_at",
        "company",
        "account_type",
        "provider",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def create_fidelity_account_detail(self, request, queryset):
        from accounts.models import AccountDetail
        from fidelity.helpers.helpers import FidelityClass
        from fidelity.models import AccountSystem

        for instance in queryset:
            transaction_ref = uuid.uuid4()

            return_message = "No account created"

            if instance.account_type == AccountType.MAIN:
                existing_fidelity_main = AccountDetail.objects.filter(
                    company=instance.company,
                    provider=ServiceProvider.FIDELITY,
                    account_type=AccountType.MAIN,
                )

                if existing_fidelity_main:
                    return
                else:
                    data = {
                        "customer_firstname": instance.company.name.split()[0],
                        "customer_surname": instance.company.name.split()[-1],
                        "customer_email": instance.company.email,
                        "customer_mobile_no": instance.company.phone,
                        "name_on_account": instance.account_name,
                        "customer_middle_name": instance.account_name.split()[0],
                        "date_of_birth": "22-02-1994",
                        "gender": "M",
                        "title": "Mr.",
                        "address_line_1": instance.company.address,
                        "address_line_2": instance.company.address,
                        "city": "Lagos",
                        "state": "Lagos",
                        "country": "Nigeria",
                        "transaction_ref": str(transaction_ref),
                    }

                    account_system = AccountSystem.objects.create(
                        company=instance.company,
                        sub_company=instance.sub_company,
                        customer_bvn=(
                            instance.company.bvn
                            if instance.company and instance.company.bvn
                            else "************"
                        ),
                    )

                    data["customer_ref"] = account_system.customer_ref
                    data["transaction_desc"] = "create a virtual account"
                    data["amount"] = 0
                    data["request_ref"] = account_system.request_ref

                    create_account = FidelityClass.create_static_account(**data)

                    response_data = (
                        create_account.get("data", {})
                        .get("data", {})
                        .get("provider_response", {})
                    )
                    response_data = response_data or {}

                    account_system.request_data = data
                    account_system.payload = create_account.get("data")
                    account_system.reference = response_data.get("reference")
                    account_system.account_number = response_data.get("account_number")
                    account_system.account_reference = response_data.get(
                        "account_reference"
                    )
                    account_system.account_name = response_data.get("account_name")
                    account_system.customer_email = response_data.get("customer_email")
                    account_system.bank_name = response_data.get("bank_name")
                    account_system.bank_code = response_data.get("bank_code")
                    account_system.account_type = response_data.get("account_type")
                    account_system.status = response_data.get("status")
                    account_system.created_on = response_data.get("created_on")
                    account_system.transation_reference = data.get("transaction_ref")
                    account_system.save()

                    if create_account.get("status"):
                        AccountDetail.objects.create(
                            company=instance.company,
                            provider=ServiceProvider.FIDELITY,
                            account_type=AccountType.MAIN,
                            account_name=response_data.get("account_name"),
                            account_number=response_data.get("account_number"),
                            sub_company=instance.sub_company,
                        )
            elif instance.account_type == AccountType.SUB:
                existing_fidelity_sub = AccountDetail.objects.filter(
                    company=instance.company,
                    sub_company=instance.sub_company,
                    provider=ServiceProvider.FIDELITY,
                    account_type=AccountType.SUB,
                )

                if existing_fidelity_sub:
                    return
                else:
                    data = {
                        "customer_firstname": instance.sub_company.company_name.split()[
                            0
                        ],
                        "customer_surname": instance.sub_company.company_name.split()[
                            -1
                        ],
                        "customer_email": instance.sub_company.company_email,
                        "customer_mobile_no": instance.sub_company.company_phone,
                        "name_on_account": instance.account_name,
                        "customer_middle_name": instance.account_name.split()[0],
                        "date_of_birth": "22-02-1994",
                        "gender": "M",
                        "title": "Mr.",
                        "address_line_1": instance.sub_company.company_address,
                        "address_line_2": instance.sub_company.company_address,
                        "city": "Lagos",
                        "state": "Lagos",
                        "country": "Nigeria",
                        "transaction_ref": str(transaction_ref),
                    }

                    account_system = AccountSystem.objects.create(
                        company=instance.company,
                        sub_company=instance.sub_company,
                        customer_bvn=(
                            instance.company.bvn
                            if instance.sub_company and instance.company.bvn
                            else "************"
                        ),
                    )

                    data["customer_ref"] = account_system.customer_ref
                    data["transaction_desc"] = "create a virtual account"
                    data["amount"] = 0
                    data["request_ref"] = account_system.request_ref

                    create_account = FidelityClass.create_static_account(**data)

                    response_data = (
                        create_account.get("data", {})
                        .get("data", {})
                        .get("provider_response", {})
                    )
                    response_data = response_data or {}

                    account_system.request_data = data
                    account_system.payload = create_account.get("data")
                    account_system.reference = response_data.get("reference")
                    account_system.account_number = response_data.get("account_number")
                    account_system.account_reference = response_data.get(
                        "account_reference"
                    )
                    account_system.account_name = response_data.get("account_name")
                    account_system.customer_email = response_data.get("customer_email")
                    account_system.bank_name = response_data.get("bank_name")
                    account_system.bank_code = response_data.get("bank_code")
                    account_system.account_type = response_data.get("account_type")
                    account_system.status = response_data.get("status")
                    account_system.created_on = response_data.get("created_on")
                    account_system.transation_reference = data.get("transaction_ref")
                    account_system.save()

                    if create_account.get("status"):
                        AccountDetail.objects.create(
                            company=instance.company,
                            provider=ServiceProvider.FIDELITY,
                            account_type=AccountType.SUB,
                            account_name=response_data.get("account_name"),
                            account_number=response_data.get("account_number"),
                            sub_company=instance.sub_company,
                        )
            else:
                pass

        return_message = create_account
        self.message_user(request, return_message)

    create_fidelity_account_detail.allow_tags = True
    create_fidelity_account_detail.short_description = (
        "FIDELITY: Create Fidelity Account Details"
    )

    actions = [
        create_fidelity_account_detail,
    ]


class CommissionEarnedResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CommissionEarnedResource
    search_fields = [
        "earner__email",
    ]
    list_filter = [
        "created_at",
        "earner",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionDetailResource
    search_fields = [
        "id",
        "company__email",
        "sub_company__company_email",
        "beneficiary_account_number",
        "source_account",
        "reference",
        "session_id",
        "company_reference",
        "request_reference",
        "unique_code",
        "confirmation_code",
    ]
    list_filter = [
        "created_at",
        "repayment_treated",
        "transaction_status",
        "transaction_type",
        "company",
        "one_time",
        "is_verified",
        "service_provider",
        "transfer_mode",
        "currency",
        "mode",
        "settled",
        "event_sent",
        "checked",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    @admin.action(description="Credit Transactions: resend company event")
    def resend_company_event(
        self,
        request,
        queryset: QuerySet[models.TransactionDetail],
    ):
        for obj in queryset:
            send_company_transaction_callbacks.delay(obj.id)

    @admin.action(description="Wema Debit Transactions: run TSQ with session id")
    def nip_session_verification(
        self,
        request,
        queryset: QuerySet[models.TransactionDetail],
    ):
        for obj in queryset:
            outward_transfer_verification_handler(obj.session_id)

    @admin.action(description="Credit Transaction: give company inflow value")
    def resolve_inflows_completely(
        self,
        request,
        queryset: QuerySet[models.TransactionDetail],
    ):
        for obj in queryset.reverse():
            # Fetch virtual account details.
            account_details = VirtualAccount.get_account_details(
                account_number=obj.beneficiary_account_number
            )
            if account_details is None:
                pass
            # Identify the company's/sub-company's details.
            company = account_details.company
            if account_details.sub_company is not None:
                sub_company = account_details.sub_company
                charges = float(sub_company.service_fee)
                account_type = AccountType.SUB
            else:
                sub_company = None
                charges = float(company.service_fee)
                account_type = AccountType.MAIN
            # Update the associated wallet.
            wallet_transaction = models.AccountDetail.fund_account(
                company=company,
                account_type=account_type,
                amount=obj.amount,
                charges=charges,
                sub_company=sub_company,
            )
            if wallet_transaction.get("status"):
                obj.created_at = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
                obj.balance_before = wallet_transaction.get("previous_balance")
                obj.balance_after = wallet_transaction.get("account_balance")
                obj.save()

    @admin.action(
        description="Cashconnect Transfers: Resolve Pending Cashconnect Transfers"
    )
    def process_pending_cash_connect_transactions(self, request, queryset):
        from decouple import config
        from helpers import enums
        from cash_connect.helpers.cash_connect_base_file import (
            CASH_CONNECT_STATUS_CODES,
        )
        from cash_connect.helpers.manage_request import CashConnectRequestManagers
        from accounts.models import TransferMoneyRequest
        from fidelity.models import AccountSystem

        for query in queryset:
            if (
                query.transaction_status == enums.TransactionStatus.PENDING
                and query.service_provider == enums.ServiceProvider.CASH_CONNECT
                and query.transaction_type == enums.TransactionType.DEBIT
            ):
                pass
            else:
                continue

            SUCCESS_CODES = ["00"]
            FAILURE_CODES = [
                code for code in CASH_CONNECT_STATUS_CODES.keys() if code != "00"
            ]

            company_instance = query.company
            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            if company_instance is None:
                return "Company not found"

            verification_response = CashConnectRequestManagers().handle_disbursement_verification(
                transaction_reference=f'{config("CASH_CONNECT_PARTNER_CODE")}-{query.company_reference}'
            )
            if not isinstance(verification_response, dict):
                continue

            processor_status_code = verification_response.get("processorStatusCode")

            if processor_status_code in SUCCESS_CODES:
                session_id = verification_response.get("data", {}).get("sessionId")
                source_account = verification_response.get("data", {}).get(
                    "sourceAccountNumber"
                )
                bank_name = verification_response.get("data", {}).get(
                    "beneficiaryBankName"
                )
                account_number = verification_response.get("data", {}).get(
                    "beneficiaryAccountNumber"
                )
                transaction_amount = verification_response.get("data", {}).get(
                    "transactionAmount"
                )
                transaction_value_amount = verification_response.get("data", {}).get(
                    "transactionValueAmount"
                )
                fee = verification_response.get("data", {}).get("transactionFeeAmount")
                narration = verification_response.get("data", {}).get("narration")

                query.transaction_status = enums.TransactionStatus.SUCCESSFUL
                query.session_id = session_id
                query.verification_response = verification_response
                query.source_account = source_account
                query.bank_name = bank_name
                query.save()

                # get transfer request instance
                try:
                    transfer_db_instance = TransferMoneyRequest.objects.get(
                        request_reference=query.company_reference
                    )
                except TransferMoneyRequest.DoesNotExist:
                    continue

                transfer_db_instance.session_id = session_id
                transfer_db_instance.verified_at = TODAY
                transfer_db_instance.verification_response = verification_response
                transfer_db_instance.status = enums.TransactionStatus.SUCCESSFUL
                transfer_db_instance.save()

                company_instance = query.company

            elif processor_status_code in FAILURE_CODES:
                # Refund Company wallet
                # Perform cashconnect Reversal
                query.transaction_status = enums.TransactionStatus.FAILED
                query.verification_response = verification_response
                query.save()

                AccountSystem.perform_transfer_reversal(
                    query=query,
                    service_provider=enums.ServiceProvider.CASH_CONNECT,
                    shadow_reverse=True,
                )

    actions = [
        resend_company_event,
        nip_session_verification,
        resolve_inflows_completely,
        process_pending_cash_connect_transactions,
    ]


class TransferMoneyResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransferMoneyResource
    autocomplete_fields = [
        "company",
    ]
    search_fields = [
        "id",
        "company__email",
        "source_account",
        "account_name",
        "account_number",
        "request_reference",
        "session_id",
    ]
    list_filter = [
        "created_at",
        "invalid_request",
        "refunded",
        "company",
        "status",
        "mode",
        "service_provider",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    @admin.action(description="NIP: Send Money via Provider")
    def wema_send_money(
        self,
        request,
        queryset: QuerySet[models.TransferMoneyRequest],
    ):
        from cash_connect.models import (
            CashConnectDisbursementWalletFundingFromSettlementWalletLogs,
            CashConnectRequestLogs,
        )
        from fidelity.models import TransferRequest as FidelityTransferRequest

        message = {}
        for obj in queryset:
            if obj.status not in [
                TransactionStatus.FAILED,
                TransactionStatus.IN_PROGRESS,
                TransactionStatus.SUCCESSFUL,
            ]:
                if obj.service_provider == ServiceProvider.CASH_CONNECT_COLLECTIONS:
                    # Transfer from cashconnect collections account to cashconnect disbursement account
                    cashconnect_disbursement_account = (
                        ConstantVariable.objects.last().cashconnect_disbursement_account
                    )

                    if obj.account_number != cashconnect_disbursement_account:
                        message.update(
                            {
                                "error": "Invalid disbursement account number provided for cashconnect"
                            }
                        )
                    else:
                        message = CashConnectDisbursementWalletFundingFromSettlementWalletLogs.fund_disbursement_account_from_collections(
                            transfer_money_obj=obj
                        )
                elif obj.service_provider == ServiceProvider.CASH_CONNECT:
                    message = CashConnectRequestLogs.admin_transfer_to_external_account(
                        transfer_money_obj=obj
                    )
                elif obj.service_provider == ServiceProvider.FIDELITY:
                    request.user = obj.company

                    message = (
                        FidelityTransferRequest.admin_transfer_to_external_account(
                            transfer_money_obj=obj,
                            request=request,
                        )
                    )
                else:
                    wema_funds_transfer(obj.id)

        self.message_user(request=request, message=message)

    @admin.action(description="NIP STATUS: run TSQ with reference")
    def nip_reference_verification(
        self,
        request,
        queryset: QuerySet[models.TransferMoneyRequest],
    ):
        for obj in queryset:
            outward_transfer_verification_handler_two(obj.id)

    @admin.action(description="NIP STATUS: run TSQ with session id")
    def nip_session_verification(
        self,
        request,
        queryset: QuerySet[models.TransferMoneyRequest],
    ):
        for obj in queryset:
            outward_transfer_verification_handler(obj.session_id)

    actions = [
        wema_send_money,
        nip_reference_verification,
        nip_session_verification,
    ]


class ProviderFloatMonitoringResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ProviderFloatMonitoringResource
    autocomplete_fields = []
    search_fields = []
    list_filter = [
        "provider",
        "created_at",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.CompanyAccountBalance, CompanyAccountBalanceResourceAdmin)
admin.site.register(models.AccountDetail, AccountDetailResourceAdmin)
admin.site.register(models.CommissionEarned, CommissionEarnedResourceAdmin)
admin.site.register(models.TransactionDetail, TransactionDetailResourceAdmin)
admin.site.register(models.TransferMoneyRequest, TransferMoneyResourceAdmin)
admin.site.register(
    models.ProviderFloatMonitoring, ProviderFloatMonitoringResourceAdmin
)
