from datetime import date, datetime
import json

from celery import shared_task
from decouple import config
from django.conf import settings
from django.db import transaction
from django.db.models import F, Sum
import pytz

from accounts import models
from helpers import enums
from helpers.reusable import (
    is_valid_string,
    make_request,
    session_maker,
)
from user_profiles.models import Company


CORE_BANKING_EMAIL = settings.CORE_BANKING_EMAIL
PENDING_CODE = ["09", "20", "97"]
SMS_KEY = config("SMS_KEY")
SMS_TEMPLATE = config("SMS_TEMPLATE")
SMS_URL = "https://whispersms.xyz/transactional/send"
SUCCESS_CODE = "00"


# Create your task(s) here.
@shared_task
def handle_inflow_commission(transaction_id: str):
    """
    This function is used to handle commissions earned on inflow(s).
    Args:
        transaction_id (str): The inflow transaction ID.
    Returns:
        str: A message indicating the result of the commission processing.
    """
    core_banking_user = Company.objects.filter(email=CORE_BANKING_EMAIL).first()
    if core_banking_user is None:
        return f"CORE BANKING USER NOT FOUND."
    credit_transaction = models.TransactionDetail.objects.filter(
        id=transaction_id
    ).first()
    if credit_transaction is None:
        return f"CREDIT TRANSACTION NOT FOUND FOR: {transaction_id}."
    commission_transaction = models.CommissionEarned.objects.create(
        earner=core_banking_user,
        company=credit_transaction.company,
        sub_company=credit_transaction.sub_company,
        transaction=credit_transaction,
        amount=credit_transaction.fee,
    )
    models.AccountDetail.fund_account(
        company=core_banking_user,
        account_type=enums.AccountType.COMMISSION,
        amount=commission_transaction.amount,
    )
    return "SUCCESSFULLY HANDLED COMMISSION EARNED."


@shared_task
def handle_wema_company_collection(inflow_id: str):
    """
    Process a Wema Bank inflow transaction for updating
    a company's/sub-company's wallet balance.
    Args:
        inflow_id (str): the TransactionCallback instance ID.
    Returns:
        str: A message indicating the result of the transaction processing.
    """
    from wema_bank.helper.api_specifications import WemaBank
    from wema_bank.models import (
        ConstantVariable,
        TransactionCallback,
        VirtualAccount,
        WemaInflowDump,
    )
    from wema_bank.tasks import instant_payout, send_company_transaction_callbacks

    constant = ConstantVariable.objects.first()
    process_inflow = constant.process_inflow
    if not process_inflow:
        return "INFLOWS ARE CURRENTLY NOT BEING PROCESSED."

    inflow_transaction = TransactionCallback.objects.filter(id=inflow_id).last()
    session_id = inflow_transaction.session_id
    credit_account = inflow_transaction.recipient_account_number
    amount = float(inflow_transaction.amount)

    # NOTE: bypass TSQ (only allowed when necessary).
    allow_inflow = False
    bypass_tsq = constant.bypass_tsq
    if bypass_tsq and is_valid_string(session_id):
        allow_inflow = True
    else:
        # perform TSQ before updating the wallet(s).
        transaction_status = WemaBank.inflow_event_tsq(session_id=session_id)
        if not transaction_status.get("status"):
            inflow_transaction.failed_tsq_payload = json.dumps(transaction_status)
            inflow_transaction.tsq_count += 1
            inflow_transaction.save()
            return f"FAILED TSQ FOR SESSION ID: {session_id}."
        else:
            data = transaction_status.get("data")
            # validate wema's transaction status.
            if data.get("status") == SUCCESS_CODE:
                response_data = data.get("transactions")[0]
                response_session_id = response_data.get("sessionid")
                response_craccount = response_data.get("craccount")
                response_amount = float(response_data.get("amount"))
                nibss_response = response_data.get("nibssresponse")

                if (
                    nibss_response == SUCCESS_CODE and session_id == response_session_id
                ) and (
                    credit_account == response_craccount and amount == response_amount
                ):
                    inflow_transaction.step_two = True
                    inflow_transaction.status = enums.InflowStatus.CONFIRMED
                    inflow_transaction.tsq_count += 1
                    inflow_transaction.confirmed_at = datetime.now(
                        tz=pytz.timezone(settings.TIME_ZONE)
                    )
                    inflow_transaction.confirmation_payload = json.dumps(data)
                    inflow_transaction.save()
                    allow_inflow = True
                else:
                    inflow_transaction.status = enums.InflowStatus.INVALID
                    inflow_transaction.tsq_count += 1
                    inflow_transaction.confirmed_at = datetime.now(
                        tz=pytz.timezone(settings.TIME_ZONE)
                    )
                    inflow_transaction.confirmation_payload = json.dumps(data)
                    inflow_transaction.save()
                    return f"INVALID TSQ DATA FOR SESSION ID: {session_id}."
            else:
                inflow_transaction.failed_tsq_payload = json.dumps(transaction_status)
                inflow_transaction.tsq_count += 1
                inflow_transaction.save()
                return f"FAILED TSQ FOR SESSION ID: {session_id}."

    if allow_inflow:
        # Fetch virtual account details.
        account_details = VirtualAccount.get_account_details(
            account_number=credit_account
        )
        if account_details is None:
            return f"VIRTUAL ACCOUNT DETAILS NOT FOUND FOR: {credit_account}."
        # Identify the company's/sub-company's details.
        company = account_details.company
        if account_details.sub_company is not None:
            sub_company = account_details.sub_company
            charges = float(sub_company.service_fee)
            account_type = enums.AccountType.SUB
        else:
            sub_company = None
            charges = float(company.service_fee)
            account_type = enums.AccountType.MAIN
        # Check for duplicate processing using session_id to ensure idempotency
        existing_transaction = models.TransactionDetail.objects.filter(
            session_id=session_id,
            service_provider=enums.ServiceProvider.WEMA_BANK,
            transaction_status=enums.TransactionStatus.SUCCESSFUL,
        ).first()

        if existing_transaction is not None:
            return f"TRANSACTION ALREADY PROCESSED FOR SESSION ID: {session_id}."

        # Maintaining database integrity at this point.
        # Wrap the entire balance update process in atomic transaction
        with transaction.atomic():
            # Double-check for duplicate within the transaction to handle race conditions
            existing_transaction = (
                models.TransactionDetail.objects.select_for_update()
                .filter(
                    session_id=session_id,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                )
                .first()
            )

            if existing_transaction is not None:
                return f"TRANSACTION ALREADY PROCESSED FOR SESSION ID: {session_id}."

            # Instantiate the transaction record.
            credit_transaction = models.TransactionDetail.register_transaction(
                company=company,
                sub_company=sub_company,
                beneficiary_account_number=credit_account,
                beneficiary_account_name=inflow_transaction.recipient_account_name,
                amount=amount,
                fee=charges,
                amount_payable=amount - charges,
                transaction_type=enums.TransactionType.CREDIT,
                transaction_status=enums.TransactionStatus.SUCCESSFUL,
                narration=inflow_transaction.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=enums.APIMode.LIVE,
                session_id=session_id,
                bank_code=inflow_transaction.payer_bank_code,
                bank_name=inflow_transaction.payer_bank_name,
                source_account=inflow_transaction.payer_account_number,
                source_name=inflow_transaction.payer_account_name,
                is_verified=True,
                one_time=account_details.one_time,
                request_reference=account_details.request_reference,
                offline=account_details.offline,
                unique_code=account_details.unique_code,
                confirmation_code=account_details.confirmation_code,
            )
            if not isinstance(credit_transaction, bool):
                if account_details.one_time:
                    account_details.request_active = False
                    account_details.save()
                # update the wema data dump and transaction status(es).
                data_dump = WemaInflowDump.objects.filter(session_id=session_id).last()
                if data_dump != None:
                    data_dump.is_resolved = True
                    data_dump.save()
                inflow_transaction.is_resolved = True
                inflow_transaction.save()
                # Update the virtual account all time inflow balance.
                VirtualAccount.fund(
                    nuban=credit_account,
                    amount=amount,
                )
                # Update the associated wallet.
                # The fund_account method now handles its own locking internally
                wallet_transaction = models.AccountDetail.fund_account(
                    company=company,
                    account_type=account_type,
                    amount=amount,
                    charges=charges,
                    sub_company=sub_company,
                )
                if wallet_transaction.get("status"):
                    credit_transaction.balance_before = wallet_transaction.get(
                        "previous_balance"
                    )
                    credit_transaction.balance_after = wallet_transaction.get(
                        "account_balance"
                    )
                    credit_transaction.save()
                if charges > 0.0:
                    handle_inflow_commission.delay(transaction_id=credit_transaction.id)
                # virtual account instant payout if applicable.
                if (
                    account_details.instant_settlement
                    and account_details.settlement_bank_code != None
                ) and (
                    account_details.settlement_account_name != None
                    and account_details.settlement_account_number != None
                ):
                    instant_payout.apply_async(
                        queue="nip",
                        args=[credit_transaction.id],
                    )
                else:
                    send_company_transaction_callbacks.delay(
                        transaction_id=credit_transaction.id
                    )
                return f"SUCCESSFULLY UPDATED ACCOUNT DETAILS WITH SESSION ID: {session_id}."
            else:
                return f"DUPLICATE TRANSACTION FOR SESSION ID: {session_id}."


@shared_task
def outward_transfer_verification_handler(session_id: str):
    """
    Verification is performed using the session ID.
    """
    from wema_bank.helper.api_specifications import WemaBank

    nip_tsq = WemaBank.nip_outward_tsq(session_id=session_id)
    if nip_tsq.get("status"):
        data = nip_tsq.get("wema_response").split("|")
        response_code = data[0]
        if response_code == SUCCESS_CODE:
            status = enums.TransactionStatus.SUCCESSFUL
        if response_code in PENDING_CODE:
            status = enums.TransactionStatus.IN_PROGRESS
        if response_code != SUCCESS_CODE and response_code not in PENDING_CODE:
            status = enums.TransactionStatus.FAILED
        # Update the transfer money request record.
        transfer_request = models.TransferMoneyRequest.objects.filter(
            session_id=session_id
        ).last()
        if transfer_request != None:
            transfer_request.status = status
            transfer_request.verified_at = datetime.now(
                tz=pytz.timezone(settings.TIME_ZONE)
            )
            transfer_request.verification_response = json.dumps(nip_tsq)
            transfer_request.save()
        # Update the company's transaction record.
        associated_record = models.TransactionDetail.objects.filter(
            session_id=session_id
        ).last()
        if associated_record != None:
            associated_record.transaction_status = status
            associated_record.is_verified = True
            associated_record.verification_response = json.dumps(nip_tsq)
            associated_record.save()
        return nip_tsq.get("message")
    else:
        return nip_tsq.get("message")


@shared_task
def outward_transfer_verification_handler_two(reference: str):
    """
    Verification is performed using the transaction reference.
    """
    from wema_bank.helper.api_specifications import WemaBank

    nip_tsq = WemaBank.get_transaction_status(reference=reference)
    if nip_tsq.get("status"):
        if (
            isinstance(nip_tsq.get("wema_response"), str)
            and nip_tsq.get("wema_response") == "Transaction Reference not found"
        ):
            status = enums.TransactionStatus.FAILED
        else:
            data = json.loads(nip_tsq.get("wema_response"))
            response_code = data.get("Response").split("|")[0]
            if response_code == SUCCESS_CODE:
                status = enums.TransactionStatus.SUCCESSFUL
            if response_code in PENDING_CODE:
                status = enums.TransactionStatus.IN_PROGRESS
            if response_code != SUCCESS_CODE and response_code not in PENDING_CODE:
                status = enums.TransactionStatus.FAILED
        # Update the transfer money request record.
        transfer_request = models.TransferMoneyRequest.objects.filter(
            id=reference
        ).last()
        if transfer_request != None:
            transfer_request.status = status
            transfer_request.verified_at = datetime.now(
                tz=pytz.timezone(settings.TIME_ZONE)
            )
            transfer_request.verification_response = json.dumps(nip_tsq)
            transfer_request.save()
        # Update the company's transaction record.
        associated_record = models.TransactionDetail.objects.filter(
            company_reference=transfer_request.request_reference
        ).last()
        if associated_record != None:
            associated_record.transaction_status = status
            associated_record.is_verified = True
            associated_record.verification_response = json.dumps(nip_tsq)
            associated_record.save()
        return nip_tsq.get("message")
    else:
        return nip_tsq.get("message")


@shared_task
def outward_transfer_handler(request_id: str):
    from wema_bank.helper.api_specifications import (
        WemaBank,
        response_code_status,
    )

    transfer_request = models.TransferMoneyRequest.objects.filter(
        id=request_id,
        status=enums.TransactionStatus.PENDING,
    ).first()
    if transfer_request is None:
        return f"TRANSFER MONEY REQUEST NOT FOUND FOR: {request_id}."

    forward_request = WemaBank.nip_fund_transfer(
        account_name=transfer_request.account_name,
        account_number=transfer_request.account_number,
        bank_code=transfer_request.bank_code,
        narration=transfer_request.narration,
        reference_id=request_id,
        amount=transfer_request.amount,
    )
    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    associated_details = models.TransactionDetail.objects.filter(
        company_reference=transfer_request.request_reference
    ).last()

    if not forward_request.get("status"):
        # Update the transfer money request record.
        transfer_request.status = enums.TransactionStatus.FAILED
        transfer_request.requested_at = TODAY
        transfer_request.request_response = json.dumps(forward_request)
        transfer_request.save()
        # Update the company's transaction record.
        associated_details.transaction_status = enums.TransactionStatus.FAILED
        associated_details.provider_response = json.dumps(forward_request)
        associated_details.save()
        return forward_request.get("message")

    if forward_request.get("status"):
        if not isinstance(forward_request.get("wema_response"), dict):
            data = forward_request.get("wema_response").split("|")
            response_code = data[0]
            session_id = data[1]
            message = response_code_status.get(response_code)
            if response_code == SUCCESS_CODE:
                status = enums.TransactionStatus.SUCCESSFUL
            if response_code in PENDING_CODE:
                status = enums.TransactionStatus.IN_PROGRESS
            if response_code != SUCCESS_CODE and response_code not in PENDING_CODE:
                status = enums.TransactionStatus.FAILED
            # Update the transfer money request record.
            transfer_request.status = status
            transfer_request.session_id = session_id
            transfer_request.requested_at = TODAY
            transfer_request.request_response_code = response_code
            transfer_request.request_response_message = message
            transfer_request.request_response = json.dumps(forward_request)
            transfer_request.save()
            # Update the company's transaction record.
            associated_details.transaction_status = status
            associated_details.session_id = session_id
            associated_details.provider_response = json.dumps(forward_request)
            associated_details.save()
            # Session ID not returned on [2024-05-27] - initiate TSQ using reference now.
            # outward_transfer_verification_handler.delay(session_id=session_id)
            outward_transfer_verification_handler_two.apply_async(
                queue="nip",
                args=[
                    request_id,
                ],
            )
            return "MONEY TRANSFER REQUEST WAS PROCESSED SUCCESSFULLY."
        else:
            # Update the transfer money request record.
            transfer_request.status = enums.TransactionStatus.IN_PROGRESS
            transfer_request.requested_at = TODAY
            transfer_request.request_response = json.dumps(forward_request)
            transfer_request.save()
            # Update the company's transaction record.
            associated_details.transaction_status = enums.TransactionStatus.IN_PROGRESS
            associated_details.provider_response = json.dumps(forward_request)
            associated_details.save()
            return "MONEY TRANSFER REQUEST NOT COMPLETED."
    else:
        return forward_request.get("message")


@shared_task
def handle_transfer_request_reversal(reference: str):
    transfer_request = models.TransferMoneyRequest.objects.filter(id=reference).first()
    if transfer_request is not None:
        company = transfer_request.company
        amount = transfer_request.amount
        wallet_transaction = models.AccountDetail.fund_account(
            company=company,
            account_type=enums.AccountType.MAIN,
            amount=amount,
        )
        reversal_transaction = models.TransactionDetail.objects.create(
            company=company,
            beneficiary_account_number=transfer_request.account_number,
            beneficiary_account_name=transfer_request.account_name,
            amount=amount,
            fee=0.0,
            amount_payable=transfer_request.amount,
            transaction_type=enums.TransactionType.CREDIT,
            transaction_status=enums.TransactionStatus.REVERSED,
            narration=transfer_request.narration,
            service_provider=enums.ServiceProvider.WEMA_BANK,
            mode=transfer_request.mode,
            bank_code=transfer_request.bank_code,
            source_account=transfer_request.source_account,
            company_reference=transfer_request.request_reference,
        )
        reversal_transaction.balance_before = wallet_transaction.get("previous_balance")
        reversal_transaction.balance_after = wallet_transaction.get("account_balance")
        reversal_transaction.save()
        return "SUCCESSFULLY REVERSED COMPANY TRANSACTION AND REFUNDED THE WALLET."


@shared_task
def low_balance_reminder():
    wallets = models.AccountDetail.objects.filter(
        provider=enums.ServiceProvider.WEMA_BANK,
        account_type=enums.AccountType.MAIN,
        company__notify=True,
    )
    if not wallets.exists():
        return "NO REGISTERED COMPANY FOR LOW BALANCE REMINDER."
    for wallet in wallets:
        if wallet.cash_balance <= wallet.company.balance_benchmark:
            contacts = wallet.company.contact_person.split(",")
            for contact in contacts:
                make_request(
                    "POST",
                    dict(
                        url=SMS_URL,
                        headers={
                            "Authorization": f"Api_key {SMS_KEY}",
                            "Content-Type": "application/json",
                        },
                        data=json.dumps(
                            {
                                "receiver": str(contact),
                                "template": SMS_TEMPLATE,
                                "place_holders": {"amount": str(wallet.cash_balance)},
                            }
                        ),
                    ),
                )
    return "LOW BALANCE REMINDER RAN SUCCESSFULLY."


@shared_task
def inward_transfer_handler(request_id: str):
    from wema_bank.models import VirtualAccount
    from wema_bank.tasks import send_company_transaction_callbacks

    transfer_request = models.TransferMoneyRequest.objects.filter(
        id=request_id,
        status=enums.TransactionStatus.PENDING,
    ).last()
    if transfer_request is None:
        return f"TRANSFER MONEY REQUEST NOT FOUND FOR: {request_id}."

    associated_details = models.TransactionDetail.objects.filter(
        company_reference=transfer_request.request_reference
    ).last()
    # Fetch virtual account details.
    account_details = VirtualAccount.get_account_details(
        account_number=transfer_request.account_number
    )
    if account_details is None:
        return (
            f"VIRTUAL ACCOUNT DETAILS NOT FOUND FOR: {transfer_request.account_number}."
        )
    # Identify the company's/sub-company's details.
    company = account_details.company
    if account_details.sub_company is not None:
        sub_company = account_details.sub_company
        charges = float(sub_company.service_fee)
        account_type = enums.AccountType.SUB
    else:
        sub_company = None
        charges = float(company.service_fee)
        account_type = enums.AccountType.MAIN
    # Instantiate the transaction record.
    credit_transaction = models.TransactionDetail.register_transaction(
        company=company,
        beneficiary_account_number=transfer_request.account_number,
        beneficiary_account_name=transfer_request.account_name,
        amount=transfer_request.amount,
        fee=charges,
        amount_payable=float(transfer_request.amount) - charges,
        transaction_type=enums.TransactionType.CREDIT,
        transaction_status=enums.TransactionStatus.SUCCESSFUL,
        narration=transfer_request.narration,
        service_provider=transfer_request.service_provider,
        mode=enums.APIMode.LIVE,
        session_id=f"local-{session_maker()}",
        bank_code=transfer_request.bank_code,
        bank_name=transfer_request.company.name,
        source_account=transfer_request.source_account,
        source_name=transfer_request.company.name,
        is_verified=True,
        sub_company=sub_company,
        one_time=account_details.one_time,
        request_reference=account_details.request_reference,
        offline=account_details.offline,
        unique_code=account_details.unique_code,
        confirmation_code=account_details.confirmation_code,
    )
    if not isinstance(credit_transaction, bool):
        transfer_request.status = enums.TransactionStatus.SUCCESSFUL
        transfer_request.save()
        associated_details.transaction_status = enums.TransactionStatus.SUCCESSFUL
        associated_details.save()
        # Update the virtual account all time inflow balance.
        VirtualAccount.fund(
            nuban=transfer_request.account_number,
            amount=transfer_request.amount,
        )
        # Update the associated wallet.
        wallet_transaction = models.AccountDetail.fund_account(
            company=company,
            account_type=account_type,
            amount=transfer_request.amount,
            charges=charges,
            sub_company=sub_company,
        )
        if wallet_transaction.get("status"):
            credit_transaction.balance_before = wallet_transaction.get(
                "previous_balance"
            )
            credit_transaction.balance_after = wallet_transaction.get("account_balance")
            credit_transaction.save()
        if charges > 0.0:
            handle_inflow_commission.delay(transaction_id=credit_transaction.id)
        send_company_transaction_callbacks.delay(transaction_id=credit_transaction.id)
        return f"SUCCESSFULLY UPDATED ACCOUNT DETAILS WITH REQUEST REFERENCE: {transfer_request.request_reference}."
    else:
        return f"DUPLICATE TRANSACTION FOR REQUEST REFERENCE: {transfer_request.request_reference}."


@shared_task
def mqtt_inflow_event(
    account_number: str,
    title: str,
    message: str,
):
    from helpers.emqx_mqtt import EMQXHandler

    logger = settings.LOGGER

    try:
        handler = EMQXHandler()

        with handler.session():
            payload = {
                "subject": title,
                "body": message,
            }
            success = handler.publish(
                topic=f"account/events/{account_number}",
                message=json.dumps(payload),
                qos=1,
            )
            if success:
                return (
                    f"Successfully published message to account/events/{account_number}"
                )
            else:
                return f"Failed to publish message to account/events/{account_number}"
    except Exception as e:
        logger.error(f"Error in mqtt_inflow_event: {e}")
        return f"Error in mqtt_inflow_event: {e}"


@shared_task
def wema_float_monitoring():
    from accounts.models import (
        ProviderFloatMonitoring,
        TransactionDetail,
    )
    from wema_bank.helper.api_specifications import WemaBank

    check_wema = WemaBank.get_balance()
    if check_wema.get("status"):
        wema_float_balance = float(check_wema.get("wema_response"))
        wema_float_monitoring = ProviderFloatMonitoring.objects.filter(
            created_at__date=date.today(),
            provider=enums.ServiceProvider.WEMA_BANK,
        ).first()
        local_company_balances = (
            models.AccountDetail.objects.filter(
                account_type=enums.AccountType.MAIN,
                provider=enums.ServiceProvider.WEMA_BANK,
            ).aggregate(
                available_balance=Sum(F("cash_balance") + F("overdraft_balance"))
            )[
                "available_balance"
            ]
            or 0.0
        )
        local_events = TransactionDetail.objects.filter(
            created_at__date=date.today(),
            service_provider=enums.ServiceProvider.WEMA_BANK,
            transaction_status=enums.TransactionStatus.SUCCESSFUL,
        )
        credit_value = (
            local_events.filter(
                transaction_type=enums.TransactionType.CREDIT
            ).aggregate(Sum("amount"))["amount__sum"]
            or 0.0
        )
        debit_value = (
            local_events.filter(transaction_type=enums.TransactionType.DEBIT).aggregate(
                Sum("amount")
            )["amount__sum"]
            or 0.0
        )
        if wema_float_monitoring is None:
            ProviderFloatMonitoring.objects.create(
                provider=enums.ServiceProvider.WEMA_BANK,
                bank_opening_balance=wema_float_balance,
                local_opening_balance=local_company_balances,
                total_credit_amount=credit_value,
                total_debit_amount=debit_value,
                local_closing_balance=local_company_balances,
                bank_closing_balance=wema_float_balance,
                difference=float(local_company_balances) - float(wema_float_balance),
            )
        else:
            wema_float_monitoring.total_credit_amount = credit_value
            wema_float_monitoring.total_debit_amount = debit_value
            wema_float_monitoring.local_closing_balance = float(local_company_balances)
            wema_float_monitoring.bank_closing_balance = float(wema_float_balance)
            wema_float_monitoring.difference = (
                float(local_company_balances)
                - float(wema_float_monitoring.known_deficit)
                - float(wema_float_balance)
            )
            wema_float_monitoring.save()
            return "SUCCESSFULLY UPDATED WEMA FLOAT MONITORING."
    else:
        return check_wema.get("message")
