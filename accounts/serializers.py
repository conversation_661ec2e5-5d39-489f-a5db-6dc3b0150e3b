from django.db.models import Sum

from rest_framework import serializers

from accounts import models
from fidelity.models import TransferRequest

from helpers.reusable import format_currency
from helpers.enums import TransactionStatus, TransactionType, ServiceProvider


# Create your serializer(s) here.
class AccountDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AccountDetail
        exclude = [
            "id",
            "created_at",
            "updated_at",
            "previous_balance",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.name
        representation["sub_company"] = (
            instance.sub_company.company_name
            if instance.sub_company is not None
            else None
        )
        representation["cash_balance"] = format_currency(amount=instance.cash_balance)

        total_wema_inflow = (
            models.TransactionDetail.objects.filter(
                company=instance.company,
                sub_company=instance.sub_company,
                transaction_type=TransactionType.CREDIT,
                transaction_status=TransactionStatus.SUCCESSFUL,
                service_provider=ServiceProvider.WEMA_BANK,
            ).aggregate(total=Sum("amount"))["total"]
            or 0.00
        )

        total_fidelity_inflow = (
            TransferRequest.objects.filter(
                company=instance.company,
                sub_company=instance.sub_company,
                transaction_type=TransactionType.CREDIT,
                status=TransactionStatus.SUCCESSFUL,
            ).aggregate(total=Sum("amount"))["total"]
            or 0.00
        )

        representation["total_wema_inflow"] = total_wema_inflow
        representation["total_fidelity_inflow"] = total_fidelity_inflow
        return representation


class TransactionDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.TransactionDetail
        fields = "__all__"
        read_only_fields = [
            "company",
            "transaction_status",
            "transaction_type",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.name
        representation["sub_company"] = (
            instance.sub_company.company_name
            if instance.sub_company is not None
            else None
        )
        return representation


class TransferMoneySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.TransferMoneyRequest
        fields = [
            "company",
            "source_account",
            "mode",
            "account_name",
            "account_number",
            "bank_code",
            "request_reference",
            "amount",
            "narration",
            "status",
            "session_id",
        ]
        read_only_fields = [
            "company",
            "status",
            "session_id",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.name
        return representation


class VerifyTransferSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.TransactionDetail
        exclude = [
            "repayment_treated",
            "settled",
            "provider_response",
            "verification_response",
            "event_sent",
            "event_send_count",
            "event_updated_at",
            "company_event_response",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.name
        representation["sub_company"] = (
            instance.sub_company.company_name
            if instance.sub_company is not None
            else None
        )
        return representation


class TransactionSerializer(serializers.Serializer):

    trnx_id = serializers.SerializerMethodField("get_trnx_id")
    trnx_date = serializers.SerializerMethodField("get_trnx_date")
    company_name = serializers.SerializerMethodField("get_company_name")
    reference = serializers.SerializerMethodField("get_reference")
    balance_before = serializers.SerializerMethodField("get_balance_before")
    balance_after = serializers.SerializerMethodField("get_balance_after")
    amount = serializers.SerializerMethodField("get_amount")
    trnx_type = serializers.SerializerMethodField("get_trnx_type")
    trnx_status = serializers.SerializerMethodField("get_trnx_status")

    class Meta:
        fields = (
            "trnx_id",
            "trnx_date",
            "company_name",
            "reference",
            "balance_before",
            "balance_after",
            "amount",
            "trnx_type",
            "trnx_status",
        )

    def get_trnx_id(self, obj):
        return obj.id

    def get_trnx_date(self, obj):
        return obj.created_at

    def get_company_name(self, obj):
        return obj.company.name

    def get_reference(self, obj):
        return obj.reference

    def get_amount(self, obj):
        return obj.amount

    def get_balance_before(self, obj):
        return obj.balance_before

    def get_balance_after(self, obj):
        return obj.balance_after

    def get_trnx_type(self, obj):
        return obj.transaction_type

    def get_trnx_status(self, obj):
        return obj.transaction_status


class ExcelFileUploadSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        """Validate that the uploaded file is an Excel file"""
        if not value.name.endswith((".xlsx", ".xls")):
            raise serializers.ValidationError(
                "File must be an Excel file (.xlsx or .xls)"
            )

        # Check file size (limit to 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 10MB")

        return value


class WemaReconciliationSerializer(serializers.Serializer):
    file = serializers.FileField()
    month = serializers.IntegerField(min_value=1, max_value=12)
    year = serializers.IntegerField(min_value=2000)

    def validate_file(self, value):
        """Validate that the uploaded file is an Excel file"""
        if not value.name.endswith((".xlsx", ".xls")):
            raise serializers.ValidationError(
                "File must be an Excel file (.xlsx or .xls)"
            )
        # Check file size (limit to 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 10MB")
        return value


class TransactionCheckerSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        """Validate that the uploaded file is an Excel file"""
        if not value.name.endswith((".xlsx", ".xls")):
            raise serializers.ValidationError(
                "File must be an Excel file (.xlsx or .xls)"
            )
        # Check file size (limit to 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 10MB")
        return value