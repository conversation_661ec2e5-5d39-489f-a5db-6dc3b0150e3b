from django.conf import settings
from django.db.models import Q
from django.http import HttpResponse, Http404
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
import os

from accounts import models, serializers
from accounts.utils import (
    process_wema_account_statement,
    wema_reconciliation,
    generate_detailed_monthly_transaction_summary,
    generate_monthly_transaction_summary_excel,
    check_monthly_summary_dependencies,
    process_transaction_checker,
)
from helpers import enums
from helpers.custom_permissions import (
    CompanyCanSendMoney,
    IsCompanyVerified,
    IsIPWhitelisted,
)
from helpers.custom_responses import Response


# Create your view(s) here.
class AccountDetailAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.AccountDetailSerializer

    def get(self, request, *args, **kwargs):
        provider = request.query_params.get("provider")
        if not provider:
            provider = enums.ServiceProvider.WEMA_BANK
        account_detail = models.AccountDetail.objects.filter(
            company=request.user,
            provider=provider,
        ).last()
        if account_detail is not None:
            serializer = self.serializer_class(instance=account_detail)
            data = {
                "message": "successfully fetched company account details.",
                "acount_details": serializer.data,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        else:
            data = {
                "message": "company has no account details yet, contact support.",
                "account_details": {},
            }
            return Response(
                data=data, status_code=404, status=status.HTTP_404_NOT_FOUND
            )


class TransferMoneyAPIView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        IsIPWhitelisted,
        CompanyCanSendMoney,
    ]
    serializer_class = serializers.TransferMoneySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": request.data,
                "account_balance": 0.0,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        else:
            transfer_request = models.TransferMoneyRequest.register_transfer_request(
                company=request.user,
                **serializer.validated_data,
            )
            if not transfer_request.get("status"):
                data = {
                    "status": transfer_request.get("status"),
                    "message": transfer_request.get("message"),
                    "transaction": transfer_request.get("transaction"),
                    "account_balance": transfer_request.get("account_balance"),
                }
                return Response(
                    data=data, status_code=402, status=status.HTTP_402_PAYMENT_REQUIRED
                )
            else:
                serializer = self.serializer_class(
                    instance=transfer_request.get("transaction")
                )
                data = {
                    "status": transfer_request.get("status"),
                    "message": transfer_request.get("message"),
                    "transaction": serializer.data,
                    "account_balance": transfer_request.get("account_balance"),
                }
                return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class VerifyTransferAPIView(APIView):
    permission_classes = [IsAuthenticated, IsCompanyVerified]

    def get(self, request, *args, **kwargs):
        search = request.GET.get("search")
        if search is None or search == "":
            return Response(
                errors={"message": "provide a search parameter."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        transaction = models.TransactionDetail.objects.filter(
            company=request.user
        ).filter(
            Q(company_reference=search) | Q(reference=search) | Q(session_id=search)
        )
        serializer = serializers.VerifyTransferSerializer(
            instance=transaction, many=True
        )
        return Response(
            data={"transactions": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class AccountTransactionsAPIView(APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        account_number = request.query_params.get("account_number")
        if not account_number.startswith("842"):
            return Response(
                errors={"message": "provide a valid account number."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        transactions = models.TransactionDetail.objects.filter(
            beneficiary_account_number=account_number
        )
        serializer = serializers.TransactionDetailSerializer(
            instance=transactions, many=True
        )
        return Response(
            data={"transactions": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class ProcessWemaAccountStatementAPIView(APIView):
    """
    API endpoint for processing Wema Account Statement to query TransactionDetail records
    """

    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.ExcelFileUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        uploaded_file = serializer.validated_data["file"]
        result = process_wema_account_statement(uploaded_file)
        if not result["success"]:
            return Response(
                errors={"message": result["error"]},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            data={
                "message": "Account statement processed successfully",
                "total_count": result["total_count"],
                "found_count": result["found_count"],
                "missing_count": result["missing_count"],
                "download_url": result["download_url"],
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class DownloadProcessedFileView(APIView):
    """
    Custom view to serve processed Excel files
    """

    def get(self, request, file_path):
        try:
            full_path = os.path.join(settings.MEDIA_ROOT, file_path)
            if not os.path.exists(full_path):
                raise Http404("File not found")
            with open(full_path, "rb") as f:
                file_data = f.read()
            response = HttpResponse(
                file_data,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = (
                f'attachment; filename="{os.path.basename(full_path)}"'
            )
            return response
        except Exception as e:
            raise Http404(f"Error serving file: {str(e)}")


class WemaReconciliationAPIView(APIView):
    """
    API view to handle Excel file reconciliation with local TransactionDetail records.
    Accepts an Excel file, month, and year for reconciliation.
    """

    permission_classes = [IsAuthenticated]
    serializer_class = serializers.WemaReconciliationSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        uploaded_file = serializer.validated_data["file"]
        month = serializer.validated_data["month"]
        year = serializer.validated_data["year"]
        result = wema_reconciliation(uploaded_file, month, year)

        if not result["success"]:
            return Response(
                errors={"message": result["error"]},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            data={
                "message": "Wema reconciliation completed successfully",
                "reconciliation_summary": result["reconciliation_summary"],
                "download_links": result["download_links"],
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class TransactionCheckerAPIView(APIView):
    """
    API endpoint to process Excel files and add transaction validation checker column.

    This endpoint accepts an Excel file containing transaction data and adds a 'checker' column
    that validates whether the balance calculations are correct based on transaction type:
    - For CREDIT transactions: balance_after - amount should equal balance_before
    - For DEBIT transactions: balance_after + amount should equal balance_before
    """
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.TransactionCheckerSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        uploaded_file = serializer.validated_data["file"]
        result = process_transaction_checker(uploaded_file)

        if not result["success"]:
            return Response(
                errors={"message": result["error"]},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            data={
                "message": "Transaction checker processing completed successfully",
                "total_transactions": result["total_transactions"],
                "valid_transactions": result["valid_transactions"],
                "invalid_transactions": result["invalid_transactions"],
                "validation_accuracy": result["validation_accuracy"],
                "transaction_type_counts": result["transaction_type_counts"],
                "download_url": result["download_url"],
                "filename": result["filename"],
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class MonthlyTransactionSummaryAPIView(APIView):
    """
    API view to generate and download Excel file with monthly transaction summaries per company.
    Only includes successful transactions and calculates total debit and credit amounts.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Generate monthly transaction summary Excel file and return download link.
        """
        result = generate_monthly_transaction_summary_excel()

        if not result["success"]:
            return Response(
                errors={"message": result["error"]},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            data={
                "message": "Monthly transaction summary generated successfully",
                "download_url": result["download_url"],
                "file_name": result["file_name"],
                "total_records": result["total_records"],
                "file_size": result["file_size"],
                "report_generated_at": result["report_generated_at"],
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class DetailedMonthlyTransactionSummaryAPIView(APIView):
    """
    API view to generate and download Excel file with monthly transaction summaries without company grouping.
    Only includes successful transactions and calculates total debit and credit amounts.
    Includes a checker column to validate transaction balance calculations.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Generate monthly transaction summary Excel file without company grouping and return download link.
        """
        result = generate_detailed_monthly_transaction_summary()

        if not result["success"]:
            return Response(
                errors={"message": result["error"]},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            data={
                "message": "Monthly transaction summary without company grouping generated successfully",
                "download_url": result["download_url"],
                "file_name": result["file_name"],
                "total_summary_records": result["total_summary_records"],
                "total_detailed_records": result["total_detailed_records"],
                "file_size": result["file_size"],
                "report_generated_at": result["report_generated_at"],
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class MonthlyTransactionSummaryDiagnosticsAPIView(APIView):
    """
    API view to check dependencies and diagnose issues with monthly transaction summary generation.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Check dependencies and return diagnostic information.
        """
        result = check_monthly_summary_dependencies()

        return Response(
            data={
                "message": "Dependency check completed",
                "diagnostics": result,
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )
