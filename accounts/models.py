from typing import Optional

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models, transaction
from django.db.models import F, Q

from cash_connect.models import CashConnectRequestLogs
from core.models import BaseModel
from helpers import enums
from helpers.reusable import generate_reference_code
from user_profiles.models import SubCompany


Company = settings.AUTH_USER_MODEL


# Create your model(s) here.
class CompanyAccountBalance(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    opening_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    closing_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
        default=enums.ServiceProvider.WEMA_BANK,
    )

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY DAILY ACCOUNT BALANCE"
        verbose_name_plural = "COMPANY DAILY ACCOUNT BALANCES"


class AccountDetail(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    account_name = models.CharField(max_length=255)
    account_number = models.CharField(max_length=10, blank=True, null=True)
    overdraft_balance = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        default=0.0,
        editable=False,
    )
    cash_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    previous_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        editable=False,
    )
    account_type = models.CharField(
        max_length=255,
        choices=enums.AccountType.choices,
    )
    provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
        default=enums.ServiceProvider.WEMA_BANK,
    )

    def __str__(self) -> str:
        return f"{self.account_name} = {self.cash_balance}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY ACCOUNT DETAIL"
        verbose_name_plural = "COMPANY ACCOUNT DETAILS"

    @classmethod
    @transaction.atomic
    def create_company_account(
        cls,
        company: Company,
        company_name: str,
        account_type: str,
        sub_company: Optional[SubCompany] = None,
    ):
        from wema_bank.models import VirtualAccount

        if sub_company is not None:
            virtual_account = VirtualAccount.create_account(
                company=company,
                sub_company=sub_company,
                first_name=company_name,
                email=sub_company.company_email,
                phone=sub_company.company_phone,
            )
        else:
            virtual_account = VirtualAccount.create_account(
                company=company,
                first_name=company_name,
                email=company.email,
                phone=company.phone,
            )
        account = cls.objects.create(
            company=company,
            account_name=company_name,
            account_type=account_type,
            account_number=virtual_account.account_number,
            sub_company=sub_company,
        )
        return account

    @classmethod
    @transaction.atomic
    def fund_account(
        cls,
        company: Company,
        account_type: str,
        amount: float,
        charges: Optional[float] = 0.00,
        sub_company: Optional[SubCompany] = None,
        provider=enums.ServiceProvider.WEMA_BANK,
    ):
        """
        This class method is used to add funds to a wallet's account balance. It first checks if the wallet
        exists, and if so, it updates the wallet's cash balance by adding the specified amount while
        deducting fees/charges. If the wallet is not found, it returns an error message.

        This method now uses database row locking to prevent race conditions when multiple transactions
        attempt to update the same account balance simultaneously.

        Args:
            company (Company): The company for which the wallet should be funded.
            account_type (str): The type of account to be act upon.
            amount (float): The amount to be added to the wallet's account balance.
            charges (float): The fees/charges to deduct from the amount.
            sub_company (SubCompany, Optional): The sub company for which the wallet should be funded.
        Returns:
            dict: A dictionary containing the status of the funding operation and a message.
        """

        # Use atomic transaction with row locking to prevent race conditions
        # First attempt for a sub-company (defaults to a company).
        # Use select_for_update() to lock the row and prevent concurrent modifications
        if sub_company is not None:
            wallet = (
                cls.objects.select_for_update()
                .filter(
                    company=company,
                    sub_company=sub_company,
                    account_type=account_type,
                    provider=provider,
                )
                .first()
            )
        else:
            wallet = (
                cls.objects.select_for_update()
                .filter(
                    company=company,
                    account_type=account_type,
                    provider=provider,
                )
                .first()
            )

        if wallet is not None:
            # company overdraft used.
            company_deficit = float(wallet.overdraft_balance)
            amount_payable = float(amount) - float(charges)
            available_balance = float(wallet.cash_balance)

            # Calculate the amount payable which is to be added to the wallet's cash balance.
            if amount_payable >= company_deficit:
                new_balance = available_balance + (
                    amount_payable - abs(company_deficit)
                )
                wallet.overdraft_balance = 0
                wallet.previous_balance = available_balance + company_deficit
                wallet.cash_balance = F("cash_balance") + (
                    amount_payable - abs(company_deficit)
                )
            else:
                new_balance = available_balance + amount_payable
                wallet.overdraft_balance = F("overdraft_balance") + amount_payable
                wallet.previous_balance = available_balance + company_deficit
                wallet.cash_balance = F("cash_balance") + amount_payable

            # Save the wallet with the updated balance
            wallet.save(
                update_fields=["cash_balance", "overdraft_balance", "previous_balance"]
            )
            return {
                "status": True,
                "message": "account top-up was successful.",
                "previous_balance": available_balance + company_deficit,
                "account_balance": new_balance,
            }
        else:
            return {
                "status": False,
                "message": "account details not found, contact support.",
                "previous_balance": 0,
                "account_balance": 0,
            }

    @classmethod
    def charge_account(
        cls,
        company: Company,
        source_account: str,
        amount: float,
        charges: Optional[float] = 0.0,
    ):
        """
        This class method is used to charge/debit funds from a wallet's account balance.

        This method now uses database row locking to prevent race conditions when multiple transactions
        attempt to update the same account balance simultaneously.

        Args:
            company (Company): The company whose account should be charged.
            source_account (str): The account number to be charged.
            amount (float): The amount to be charged from the account.
            charges (float): Additional charges/fees to be applied.
        Returns:
            dict: A dictionary containing the status of the charge operation and balance information.
        """
        from django.db import transaction

        charge_amount = float(amount) + float(charges)

        # Use atomic transaction with row locking to prevent race conditions
        with transaction.atomic():
            # Use select_for_update() to lock the row and prevent concurrent modifications
            company_wallet = (
                cls.objects.select_for_update()
                .filter(
                    company=company,
                    account_number=source_account,
                )
                .first()
            )
            if company_wallet is None:
                return {
                    "status": False,
                    "message": "account details not found, contact support.",
                    "previous_balance": 0.0,
                    "account_balance": 0.0,
                }

            available_balance = float(company_wallet.cash_balance)
            overdraft_balance = float(company_wallet.overdraft_balance)

            if available_balance >= charge_amount:
                company_wallet.previous_balance = available_balance
                company_wallet.cash_balance = F("cash_balance") - charge_amount
                company_wallet.save(update_fields=["cash_balance", "previous_balance"])
                return {
                    "status": True,
                    "message": "account was charged successfully.",
                    "previous_balance": available_balance,
                    "account_balance": available_balance - charge_amount,
                }
            else:
                if company.overdraft_allowed:
                    overdraft_value = charge_amount - available_balance
                    company_wallet.overdraft_balance = (
                        F("overdraft_balance") - overdraft_value
                    )
                    company_wallet.previous_balance = overdraft_balance
                    company_wallet.cash_balance = 0
                    company_wallet.save(
                        update_fields=[
                            "cash_balance",
                            "overdraft_balance",
                            "previous_balance",
                        ]
                    )
                    company_wallet.refresh_from_db()
                    return {
                        "status": True,
                        "message": "account overdraft was successful.",
                        "previous_balance": (available_balance + overdraft_balance),
                        "account_balance": float(company_wallet.overdraft_balance),
                    }
                else:
                    return {
                        "status": False,
                        "message": "can not charge company account, top-up required.",
                        "previous_balance": available_balance,
                        "account_balance": available_balance,
                    }

    @classmethod
    def charge_cash_connect_account(
        cls,
        company: Company,
        amount: float,
        charges: Optional[float] = 0.0,
    ):
        """
        This class method is used to deduct funds from a company's account balance. It first checks if the company's
        account exists, verifies that the available balance is sufficient to cover the total amount (including charges),
        and then deducts the specified amount while adding optional charges. If the company account is not found or
        the balance is insufficient, it returns an error message.
        Args:
            company (Company): The company from which the account should be charged.
            source_account (str): The account number associated with the Company.
            amount (float): The amount to be deducted from the company's account balance.
            charges (float, optional): Optional charges to add to the deducted amount (default is 0.0).
        Returns:
            dict: A dictionary containing the status of the charge operation and a message.
        """
        charge_amount = float(amount) + float(charges)
        company_wallet = cls.objects.filter(
            company=company,
            provider=enums.ServiceProvider.CASH_CONNECT,
        ).first()
        if company_wallet is None:
            return {
                "status": False,
                "message": "account details not found, contact support.",
            }

        print("company_wallet.cash_balance", company_wallet.cash_balance)
        available_balance = float(company_wallet.cash_balance)
        if available_balance >= charge_amount:
            company_wallet.previous_balance = available_balance
            company_wallet.cash_balance = F("cash_balance") - charge_amount
            company_wallet.save()
            company_wallet.refresh_from_db()
            return {
                "status": True,
                "message": "account was charged successfully.",
                "previous_balance": available_balance,
                "account_balance": float(company_wallet.cash_balance),
            }
        else:
            return {
                "status": False,
                "message": "can not charge company account, top-up required.",
                "previous_balance": None,
                "account_balance": None,
            }

    @classmethod
    def get_or_create_cash_connect_account_detail_instance(cls, company: Company):
        """
        Retrieves an existing cash connect account or creates a new one for a given company.

        This method attempts to fetch an existing Wema Bank main account for the company.
        If none exists, it creates a new virtual account using the company's details.

        Args:
            company (Company): A Company model instance containing the company's information
                            including name, email, BVN, and address.

        Returns:
            AccountDetail|None: Returns the account detail instance if successful, or None if
                            multiple accounts are found (indicating data inconsistency).

        Raises:
            No explicit exceptions are raised as they are handled internally.

        Example:
            >>> company = Company.objects.get(id=1)
            >>> account = AccountDetail.get_or_create_cash_connect_account_detail_instance(company)
            >>> if account:
            >>>     print(f"Account number: {account.account_number}")

        Notes:
            - Uses CASH_CONNECT Bank as the provider and sets account type as MAIN
            - When creating a new account:
                - Splits company name into first and last name
                - Sets default values for title (MR), gender (MALE), and marital status (SINGLE)
            - Handles potential multiple account scenarios by returning None
        """

        from cash_connect.helpers.manage_request import CashConnectRequestManagers

        provider = enums.ServiceProvider.CASH_CONNECT
        account_type = enums.AccountType.MAIN

        try:
            account = cls.objects.get(
                company=company, account_type=account_type, provider=provider
            )
        except cls.DoesNotExist:
            account = cls.objects.create(
                company=company,
                account_name=company.name,
                account_type=account_type,
                provider=provider,
            )
        return account

    @classmethod
    def get_account_detail_instance(
        cls,
        company: Company,
        provider=enums.ServiceProvider.WEMA_BANK,
        account_type=enums.AccountType.MAIN,
    ):
        """
        Retrieves a specific bank account detail instance for a company.

        This method attempts to fetch a single account matching the provided company,
        bank provider, and account type criteria. It handles cases where the account
        might not exist or where multiple accounts are found.

        Args:
            company (Company): A Company model instance for which to fetch the account
            provider (BankProvider, optional): The bank provider enum. Defaults to WEMA
            account_type (AccountType, optional): The type of account. Defaults to MAIN

        Returns:
            tuple: A tuple containing:
                - bool: True if account was found successfully, False otherwise
                - Union[AccountDetail, str]: Either:
                    - AccountDetail instance if successful
                    - Error message string if unsuccessful

        Example:
            >>> company = Company.objects.get(id=1)
            >>> success, result = AccountDetail.get_account_detail_instance(company)
            >>> if success:
            >>>     print(f"Found account: {result.account_number}")
            >>> else:
            >>>     print(f"Error: {result}")

        Notes:
            - Returns (False, "Account not found") if no matching account exists
            - Returns (False, "Multiple accounts found") if multiple accounts match criteria
            - Returns (True, account_instance) if exactly one account is found
        """

        try:
            account = cls.objects.get(
                company=company, account_type=account_type, provider=provider
            )
            return True, account
        except cls.DoesNotExist:
            return False, "Company account detail not found"
        except cls.MultipleObjectsReturned:
            return False, "Multiple company account details found"


class TransactionDetail(BaseModel):
    beneficiary_account_name = models.CharField(
        max_length=255,
        editable=False,
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    repayment_treated = models.BooleanField(default=False)
    company = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        editable=False,
    )
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        editable=False,
    )
    beneficiary_account_number = models.CharField(
        max_length=10,
        editable=False,
        null=True,
        blank=True,
    )
    reference = models.CharField(
        max_length=1200,
        default=generate_reference_code,
        unique=True,
        editable=False,
    )
    fee = models.FloatField(
        default=0.0,
        editable=False,
    )
    balance_before = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The account/wallet balance before the transaction.",
    )
    amount_payable = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The amount applicable after the deduction or addition of charges",
    )
    balance_after = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The account/wallet balance after the transaction.",
    )
    transaction_type = models.CharField(
        max_length=10,
        choices=enums.TransactionType.choices,
        editable=False,
    )
    transaction_status = models.CharField(
        max_length=255,
        choices=enums.TransactionStatus.choices,
        editable=False,
    )
    session_id = models.CharField(
        max_length=225,
        null=True,
        blank=True,
        editable=False,
        unique=True,
    )
    one_time = models.BooleanField(
        default=False,
        help_text="represents accounts used for one-time transaction(s).",
        editable=False,
    )
    request_reference = models.CharField(
        max_length=225,
        null=True,
        blank=True,
        editable=False,
        help_text="instant account request tracker.",
    )
    offline = models.BooleanField(
        default=False,
        editable=False,
        help_text="represents accounts used for offline transaction(s).",
    )
    unique_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        editable=False,
        help_text="offline ussd unique code.",
    )
    confirmation_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        editable=False,
        help_text="offline ussd confirmation code.",
    )
    bank_code = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        editable=False,
    )
    bank_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        editable=False,
    )
    source_account = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        editable=False,
    )
    source_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        editable=False,
    )
    service_provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
        editable=False,
    )
    narration = models.CharField(
        max_length=255,
        editable=False,
    )
    settled = models.BooleanField(
        default=False,
        editable=False,
    )
    is_verified = models.BooleanField(
        default=False,
        editable=False,
    )
    mode = models.CharField(
        max_length=10,
        choices=enums.APIMode.choices,
        editable=False,
    )
    transfer_mode = models.CharField(
        max_length=25,
        choices=enums.TransferMode.choices,
        null=True,
        blank=True,
    )
    provider_response = models.TextField(
        null=True,
        blank=True,
        editable=False,
    )
    verification_response = models.TextField(
        null=True,
        blank=True,
        editable=False,
    )
    company_reference = models.CharField(
        max_length=225,
        null=True,
        blank=True,
        editable=False,
        help_text="Transfer money request tracker.",
    )
    currency = models.CharField(
        max_length=3,
        choices=enums.Currency.choices,
        default=enums.Currency.NGN,
        editable=False,
    )
    event_sent = models.BooleanField(default=False)
    event_send_count = models.IntegerField(default=0)
    event_updated_at = models.DateTimeField(
        null=True,
        blank=True,
        editable=False,
    )
    company_event_response = models.TextField(
        null=True,
        blank=True,
        editable=False,
    )
    wallet_charge_response = models.TextField(
        null=True,
        blank=True,
        editable=False,
    )
    checked = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.company} | {self.transaction_type} | {self.amount} | {self.transaction_status}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY TRANSACTION DETAIL"
        verbose_name_plural = "COMPANY TRANSACTION DETAILS"

    @classmethod
    def register_transaction(
        cls,
        company: Company,
        beneficiary_account_number: str,
        beneficiary_account_name: str,
        amount: float,
        fee: float,
        amount_payable: float,
        transaction_type: str,
        transaction_status: str,
        narration: str,
        service_provider: str,
        mode: str,
        transfer_mode: Optional[str] = None,
        session_id: Optional[str] = None,
        bank_code: Optional[str] = None,
        bank_name: Optional[str] = None,
        source_account: Optional[str] = None,
        source_name: Optional[str] = None,
        is_verified: Optional[bool] = False,
        company_reference: Optional[str] = None,
        sub_company: Optional[SubCompany] = None,
        one_time: Optional[bool] = False,
        request_reference: Optional[str] = None,
        offline: Optional[bool] = False,
        unique_code: Optional[str] = None,
        confirmation_code: Optional[str] = None,
    ):
        """
        Register a transaction for a company's/sub-company's account.
        It creates a new transaction object and returns it for further
        processing or reference.

        This method now includes improved duplicate checking to prevent race conditions
        when multiple transactions with the same session_id are processed concurrently.

        Returns:
            Transaction: The registered transaction object, or False if duplicate.
        """
        from django.db import transaction, IntegrityError

        # Enhanced duplicate checking for session_id
        if session_id is not None:
            # Check for existing transaction with same session_id and service_provider
            existing_transaction = cls.objects.filter(
                session_id=session_id, service_provider=service_provider
            ).first()
            if existing_transaction is not None:
                return False

        # Use atomic transaction to handle potential race conditions
        try:
            with transaction.atomic():
                new_transaction = cls.objects.create(
                    company=company,
                    beneficiary_account_number=beneficiary_account_number,
                    beneficiary_account_name=beneficiary_account_name,
                    amount=amount,
                    fee=fee,
                    amount_payable=amount_payable,
                    transaction_type=transaction_type,
                    transaction_status=transaction_status,
                    narration=narration,
                    service_provider=service_provider,
                    mode=mode,
                    transfer_mode=transfer_mode,
                    session_id=session_id,
                    bank_code=bank_code,
                    bank_name=bank_name,
                    source_account=source_account,
                    source_name=source_name,
                    is_verified=is_verified,
                    company_reference=company_reference,
                    sub_company=sub_company,
                    one_time=one_time,
                    request_reference=request_reference,
                    offline=offline,
                    unique_code=unique_code,
                    confirmation_code=confirmation_code,
                )
                return new_transaction
        except IntegrityError:
            # Handle case where unique constraint is violated (duplicate session_id)
            return False

    @classmethod
    def verify_event(
        cls,
        reference: Optional[str] = None,
        session_id: Optional[str] = None,
        sub_company: Optional[SubCompany] = None,
    ):
        if reference:
            event = cls.objects.filter(id=reference).last()
        if session_id:
            event = cls.objects.filter(session_id=session_id).last()
        if event is None:
            return None
        if sub_company is not None:
            data = {
                "company": (
                    event.sub_company.company_name
                    if event.sub_company is not None
                    else None
                ),
                "recipient_account_name": event.beneficiary_account_name,
                "recipient_account_number": event.beneficiary_account_number,
                "amount": event.amount,
                "reference": event.id,
                "paid_at": event.created_at,
                "details": event.narration,
                "session_id": event.session_id,
                "fee": float(event.fee),
                "status": event.transaction_status,
                "type": event.transaction_type,
            }
        else:
            data = {
                "company": event.company.name if event.company is not None else None,
                "sub_company": (
                    event.sub_company.company_name
                    if event.sub_company is not None
                    else None
                ),
                "sub_company_email": (
                    event.sub_company.company_email
                    if event.sub_company is not None
                    else None
                ),
                "sub_company_unique_id": (
                    event.sub_company.unique_id
                    if event.sub_company is not None
                    else None
                ),
                "recipient_account_name": event.beneficiary_account_name,
                "recipient_account_number": event.beneficiary_account_number,
                "amount": event.amount,
                "reference": event.id,
                "paid_at": event.created_at,
                "details": event.narration,
                "session_id": event.session_id,
                "fee": float(event.fee),
                "status": event.transaction_status,
                "type": event.transaction_type,
            }
        return data


class CommissionEarned(BaseModel):
    earner = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        related_name="commissioner_earner",
    )
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    transaction = models.ForeignKey(TransactionDetail, on_delete=models.PROTECT)
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0.00)],
    )
    currency = models.CharField(
        max_length=3,
        choices=enums.Currency.choices,
        default=enums.Currency.NGN,
    )

    def __str__(self) -> str:
        return self.earner.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMMISSION EARNED"
        verbose_name_plural = "COMMISSIONS EARNED"


class TransferMoneyRequest(BaseModel):
    """
    The transfer money request transaction ID is used as the payment reference for
    further tracking and transaction status/verification.
    NOTE [LOGIC]:
    - LIVE mode: it creates a money transfer request; company transaction details; and sends
    it for further processing to the indicated service provider if the request is approved.
    """

    invalid_request = models.BooleanField(default=False)
    refunded = models.BooleanField(default=False)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    source_account = models.CharField(
        max_length=25,
        help_text="account number assigned to companies is now a real/virtual account.",
    )
    mode = models.CharField(max_length=25, choices=enums.APIMode.choices)
    transfer_mode = models.CharField(
        max_length=25,
        choices=enums.TransferMode.choices,
        default=enums.TransferMode.EXTERNAL,
    )
    account_name = models.CharField(
        max_length=225,
        verbose_name="destination account name",
    )
    account_number = models.CharField(
        max_length=25,
        verbose_name="destination account number",
    )
    bank_code = models.CharField(max_length=25)
    request_reference = models.CharField(
        max_length=255,
        unique=True,
        verbose_name="company request reference",
    )
    balance_before = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    balance_after = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    narration = models.CharField(max_length=255)
    status = models.CharField(
        max_length=25,
        choices=enums.TransactionStatus.choices,
        default=enums.TransactionStatus.PENDING,
        editable=False,
    )
    session_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        unique=True,
    )
    requested_at = models.DateTimeField(null=True, blank=True)
    request_response_code = models.CharField(max_length=25, null=True, blank=True)
    request_response_message = models.CharField(max_length=255, null=True, blank=True)
    request_response = models.TextField(
        null=True,
        blank=True,
        verbose_name="provider request response",
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    verification_response = models.TextField(
        null=True,
        blank=True,
        verbose_name="provider verification response",
    )
    wallet_charge_response = models.TextField(
        null=True,
        blank=True,
        editable=False,
    )
    service_provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
        default=enums.ServiceProvider.WEMA_BANK,
    )

    def __str__(self) -> str:
        return f"{self.company.__str__()} ==> {self.amount} ==> {self.account_name} ==> {self.status}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TRANSFER MONEY REQUEST"
        verbose_name_plural = "TRANSFER MONEY REQUESTS"

    @classmethod
    def register_transfer_request(
        cls,
        company: Company,
        source_account: str,
        mode: str,
        account_name: str,
        account_number: str,
        bank_code: str,
        request_reference: str,
        amount: float,
        narration: str,
        transfer_mode: Optional[str] = enums.TransferMode.EXTERNAL,
    ):
        from accounts.utils import wema_funds_transfer

        get_transaction = cls.objects.filter(
            request_reference=request_reference
        ).first()
        if get_transaction is not None:
            return {
                "status": False,
                "message": "duplicate transaction",
                "transaction": None,
                "account_balance": None,
            }
        if account_number.startswith("842") and bank_code == "000017":
            transfer_mode = enums.TransferMode.INTERNAL
        transaction = cls.objects.create(
            company=company,
            source_account=source_account,
            bank_code=bank_code,
            account_name=account_name,
            account_number=account_number,
            amount=amount,
            request_reference=request_reference,
            narration=narration,
            mode=mode,
            transfer_mode=transfer_mode,
        )
        return wema_funds_transfer(request_id=transaction.id)

    @classmethod
    def register_cash_connect_transfer_request(
        cls,
        company: Company,
        mode: str,
        beneficiary_account_name: str,
        beneficiary_account_number: str,
        transaction_reference: str,
        amount: float,
        narration: str,
        type_of_request=enums.CashConnectRquestTypes.INTER_DISBURSEMENT,
        beneficiary_bank_code="090360",
        **kwargs,
    ):
        service_partner = kwargs.get("service_partner")

        get_transaction = cls.objects.filter(
            request_reference=transaction_reference
        ).first()
        if get_transaction is not None:
            return {
                "status": False,
                "message": "duplicate transaction",
                "transaction": None,
                "account_balance": None,
            }

        if mode == enums.APIMode.LIVE:
            charge_user = AccountDetail.charge_cash_connect_account(
                company=company,
                amount=amount,
            )

            if not charge_user.get("status"):
                return {
                    "status": False,
                    "message": charge_user.get("message"),
                    "transaction": None,
                    "account_balance": None,
                }

            if charge_user.get("status"):
                transaction = cls.objects.create(
                    company=company,
                    mode=mode,
                    account_name=beneficiary_account_name,
                    account_number=beneficiary_account_number,
                    bank_code=beneficiary_bank_code,
                    request_reference=transaction_reference,
                    balance_before=float(charge_user.get("previous_balance")),
                    amount=amount,
                    balance_after=float(charge_user.get("account_balance")),
                    narration=narration,
                    status=enums.TransactionStatus.PENDING,
                    service_provider=enums.ServiceProvider.CASH_CONNECT,
                )
                debit_transaction = TransactionDetail.register_transaction(
                    company=company,
                    beneficiary_account_number=beneficiary_account_number,
                    beneficiary_account_name=beneficiary_account_name,
                    amount=amount,
                    fee=0.0,  # currently hard-coded.
                    amount_payable=amount,
                    transaction_type=enums.TransactionType.DEBIT,
                    transaction_status=enums.TransactionStatus.PENDING,
                    narration=narration,
                    service_provider=enums.ServiceProvider.CASH_CONNECT,
                    mode=mode,
                    bank_code=beneficiary_bank_code,
                    company_reference=transaction_reference,
                )
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.save()

                # create cash connect payload
                if type_of_request == enums.CashConnectRquestTypes.INTER_DISBURSEMENT:
                    cash_connect_inter_bank_payload = {
                        "disbursementReference": transaction_reference,
                        "disbursementAmount": amount,
                        "beneficiaryAccount": beneficiary_account_number,
                        "beneficiaryAccountName": beneficiary_account_name,
                        "beneficiaryBankCode": beneficiary_bank_code,
                        "narration": narration,
                    }

                    if service_partner == "LOANS":
                        res = CashConnectRequestLogs.make_loan_request(
                            type_of_request, **cash_connect_inter_bank_payload
                        )
                    else:
                        CashConnectRequestLogs.make_request(
                            type_of_request, **cash_connect_inter_bank_payload
                        )
                else:

                    cash_connect_inter_bank_payload = {
                        "disbursementReference": transaction_reference,
                        "disbursementAmount": amount,
                        "beneficiaryAccount": beneficiary_account_number,
                        "beneficiaryAccountName": beneficiary_account_name,
                        "narration": narration,
                    }

                    if service_partner == "LOANS":
                        CashConnectRequestLogs.make_loan_request(
                            type_of_request, **cash_connect_inter_bank_payload
                        )
                    else:
                        CashConnectRequestLogs.make_request(
                            type_of_request, **cash_connect_inter_bank_payload
                        )

                return {
                    "status": True,
                    "message": "request completed successfully.",
                    "transaction": transaction,
                    "account_balance": float(charge_user.get("account_balance")),
                }


class ProviderFloatMonitoring(BaseModel):
    provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
        editable=False,
    )
    bank_opening_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    local_opening_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    total_credit_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="local credit amount for the day.",
    )
    total_debit_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="local debit amount for the day.",
    )
    local_closing_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    bank_closing_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    known_deficit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    difference = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PROVIDER FLOAT MONITORING"
        verbose_name_plural = "PROVIDER FLOAT MONITORING"
