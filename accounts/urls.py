from django.urls import include, path

from accounts import views
from wema_bank.views import WemaCallbackAPIView


# Create your urls pattern(s) here.
account_urls = [
    path("details", views.AccountDetailAPIView.as_view()),
    path("transactions", views.AccountTransactionsAPIView.as_view()),
    path(
        "process_wema_account_statement/",
        views.ProcessWemaAccountStatementAPIView.as_view(),
    ),
    path(
        "download/<path:file_path>",
        views.DownloadProcessedFileView.as_view(),
    ),
    path(
        "wema_reconciliation/",
        views.WemaReconciliationAPIView.as_view(),
    ),
    path(
        "monthly_transaction_summary/",
        views.MonthlyTransactionSummaryAPIView.as_view(),
    ),
    path(
        "detailed_monthly_transaction_summary/",
        views.DetailedMonthlyTransactionSummaryAPIView.as_view(),
    ),
    path(
        "monthly_transaction_summary_diagnostics/",
        views.MonthlyTransactionSummaryDiagnosticsAPIView.as_view(),
    ),
    path(
        "transaction_checker/",
        views.TransactionCheckerAPIView.as_view(),
    ),
]

transfer_urls = [
    path(
        "transfer_money/",
        views.TransferMoneyAPIView.as_view(),
        name="default_transfer_money",
    ),
    path("verify_transfer", views.VerifyTransferAPIView.as_view()),
]

wema_urls = [
    path("user/wema/callback", WemaCallbackAPIView.as_view()),
]


urlpatterns = [
    path("", include(account_urls)),
    path("", include(transfer_urls)),
    path("", include(wema_urls)),
]
