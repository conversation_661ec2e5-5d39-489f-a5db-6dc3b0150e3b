from import_export import resources

from accounts import models


# Register your resource(s) here.
class CompanyAccountBalanceResource(resources.ModelResource):
    class Meta:
        model = models.CompanyAccountBalance


class AccountDetailResource(resources.ModelResource):
    class Meta:
        model = models.AccountDetail


class CommissionEarnedResource(resources.ModelResource):
    class Meta:
        model = models.CommissionEarned


class TransactionDetailResource(resources.ModelResource):
    class Meta:
        model = models.TransactionDetail

    def dehydrate_company(self, obj):
        return obj.company.name


class TransferMoneyResource(resources.ModelResource):
    class Meta:
        model = models.TransferMoneyRequest


class ProviderFloatMonitoringResource(resources.ModelResource):
    class Meta:
        model = models.ProviderFloatMonitoring
