import time
import uuid

from django.core.management.base import BaseCommand

from accounts.models import AccountDetail, TransferMoneyRequest
from helpers import enums


class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        payouts = AccountDetail.objects.filter(
            account_type=enums.AccountType.SUB,
            cash_balance__gte=1,
        )
        print(f"\n\n\nTOTAL PAYOUTS:        {payouts.count()}\n\n\n")
        time.sleep(5)
        for payout in payouts:
            self.stdout.write(
                self.style.SUCCESS(
                    "SYSTEM WILL TRY AND GENERATE A PAYOUT TRANSACTION!!!")
            )
            TransferMoneyRequest.register_transfer_request(
                company=payout.company,
                source_account=payout.account_number,
                mode=enums.APIMode.LIVE,
                account_name="Liberty/Paybox360",
                account_number="**********",
                bank_code="000017",
                request_reference=str(uuid.uuid4()),
                amount=float(payout.cash_balance),
                narration="Move funds to the main wallet",
            )
            self.stdout.write(
                self.style.SUCCESS(
                    "SYSTEM HAS COMPLETED THE GENERATION OF PAYOUT TRANSACTION!!!")
            )
