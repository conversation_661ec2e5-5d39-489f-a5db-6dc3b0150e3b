from datetime import datetime
import time

from django.core.management.base import BaseCommand, CommandParser
from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from accounts import models
from helpers import enums


class Command(BaseCommand):

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("session_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        session_id = kwargs["session_id"]
        transactions = (
            models.TransactionDetail.objects.filter(
                session_id=session_id,
            )
        )
        time.sleep(5)
        with transaction.atomic():
            for credit_transaction in transactions:
                self.stdout.write(
                    self.style.SUCCESS(
                        "SYSTEM WILL TRY AND DO AN UPDATE FOR EACH TRANSACTION !!!")
                )
                account_details = models.AccountDetail.objects.filter(
                    company=credit_transaction.company,
                    sub_company=credit_transaction.sub_company,
                ).last()
                fund_account = models.AccountDetail.fund_account(
                    company=credit_transaction.company,
                    account_type=account_details.account_type,
                    amount=credit_transaction.amount,
                    # source_account=credit_transaction.source_account,
                    sub_company=credit_transaction.sub_company,
                )
                print(f"\n\n\nFUND RESPONSE: {fund_account}\n\n\n")
                credit_transaction.balance_before = fund_account.get(
                    "previous_balance"
                )
                credit_transaction.balance_after = fund_account.get(
                    "account_balance"
                )
                credit_transaction.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        "SYSTEM HAS COMPLETED AN UPDATE ON THE TRANSACTION !!!")
                )
