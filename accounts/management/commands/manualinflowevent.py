from datetime import datetime, timedelta

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import transaction
import pytz

from accounts import models
from helpers import enums
from helpers.reusable import session_maker
from user_profiles.models import Company


class Command(BaseCommand):
    help = "MANUAL INFLOW EVENT UPDATER."

    @transaction.atomic()
    def handle(self, *args, **kwargs):
        # excel_workbook = openpyxl.load_workbook(
        #     "LibertyAssured-AccountStatement-February2025.xlsx",
        #     read_only=True,
        #     data_only=True,
        # )
        # excel_sheet = excel_workbook.active
        # column_names = [cell.value for cell in excel_sheet[1]]
        # data_rows = [row for row in excel_sheet.iter_rows(
        #     min_row=2, values_only=True)]
        # df = pd.DataFrame(data_rows, columns=column_names)
        # data = df.to_dict(orient="records")
        # for row in data:
        #     session_id = row.get("SESSION_ID")
        #     if pd.isna(session_id):
        #         session_id = None
        #     else:
        #         session_id = session_id.strip()
        #     response = row.get("RESPONSE")
        #     if pd.isna(response):
        #         response = None
        #     else:
        #         response = response.strip()
        #     amount = row.get("AMOUNT")
        #     if pd.isna(amount):
        #         amount = None
        #     else:
        #         amount = float(amount)
        #     origin_bank = row.get("ORIGINATOR_INSTITUTION")
        #     if pd.isna(origin_bank):
        #         origin_bank = None
        #     else:
        #         origin_bank = origin_bank
        #     origin_name = row.get("ORIGINATOR_NAME")
        #     if pd.isna(origin_name):
        #         origin_name = None
        #     else:
        #         origin_name = origin_name.strip()
        #     beneficiary_name = row.get("DESTINATION_ACCOUNT_NAME")
        #     if pd.isna(beneficiary_name):
        #         beneficiary_name = None
        #     else:
        #         beneficiary_name = beneficiary_name.strip()
        #     beneficiary_account = row.get("DESTINATION_ACCOUNT_NO")
        #     if pd.isna(beneficiary_account):
        #         beneficiary_account = None
        #     else:
        #         beneficiary_account = beneficiary_account.strip()
        #     narration = row.get("NARRATION")
        #     if pd.isna(narration):
        #         narration = None
        #     else:
        #         narration = narration.strip()

        #     print(f"\n\n\nSESSION_ID:   {session_id}\n\n\n")
        #     print(f"\n\n\nRESPONSE: {response}\n\n\n")
        #     print(f"\n\n\nAMOUNT:   {amount}\n\n\n")
        #     print(f"\n\n\nORIGIN_BANK:  {origin_bank}\n\n\n")
        #     print(f"\n\n\nORIGIN_NAME:  {origin_name}\n\n\n")
        #     print(f"\n\n\nBENEFICIARY_NAME: {beneficiary_name}\n\n\n")
        #     print(f"\n\n\nBENEFICIARY_ACCOUNT:  {beneficiary_account}\n\n\n")
        #     print(f"\n\n\nNARRATION:  {narration}\n\n\n")
        #     time.sleep(5)

        #     if (
        #         session_id is not None and
        #         beneficiary_account is not None
        #     ):
        #         # Fetch virtual account details.
        #         account_details = VirtualAccount.get_account_details(
        #             account_number=beneficiary_account
        #         )
        #         if account_details != None:
        #             # Identify the company's/sub-company's details.
        #             company = account_details.company
        #             if account_details.sub_company is not None:
        #                 sub_company = account_details.sub_company
        #                 charges = float(sub_company.service_fee)
        #                 account_type = enums.AccountType.SUB
        #             else:
        #                 sub_company = None
        #                 charges = float(company.service_fee)
        #                 account_type = enums.AccountType.MAIN
        #             duplicate_transaction = models.TransactionDetail.objects.filter(
        #                 session_id=session_id
        #             ).last()
        #             if duplicate_transaction == None:
        #                 # Instantiate the transaction record.
        company = Company.objects.get(
            id="6da3eb2b-f67c-4efb-be28-a1fe8bacf42b"
        )
        credit_transaction = models.TransactionDetail.objects.create(
            company=company,
            beneficiary_account_name="Cashconnect Loans",
            amount=********,
            amount_payable=********,
            balance_after=********,
            transaction_type=enums.TransactionType.CREDIT,
            transaction_status=enums.TransactionStatus.SUCCESSFUL,
            narration="wallet manual funding",
            service_provider=enums.ServiceProvider.CASH_CONNECT,
            mode=enums.APIMode.LIVE,
            session_id=f"local-{session_maker()}",
        )
        models.TransactionDetail.objects.filter(
            id=credit_transaction.id
        ).update(
            created_at=datetime.now(tz=pytz.timezone(
                settings.TIME_ZONE)) - timedelta(hours=5),
            updated_at=datetime.now(tz=pytz.timezone(
                settings.TIME_ZONE)) - timedelta(hours=5)
        )
        #         if not isinstance(credit_transaction, bool):
        #             if account_details.one_time:
        #                 account_details.request_active = False
        #                 account_details.save()
        #             # Update the virtual account all time inflow balance.
        #             VirtualAccount.fund(
        #                 nuban=beneficiary_account,
        #                 amount=amount,
        #             )
        #             # Update the associated wallet.
        #             wallet_transaction = models.AccountDetail.fund_account(
        #                 company=company,
        #                 account_type=account_type,
        #                 amount=amount,
        #                 charges=charges,
        #                 sub_company=sub_company,
        #             )
        #             if wallet_transaction.get("status"):
        #                 credit_transaction.balance_before = wallet_transaction.get(
        #                     "previous_balance"
        #                 )
        #                 credit_transaction.balance_after = wallet_transaction.get(
        #                     "account_balance"
        #                 )
        #                 credit_transaction.save()

        #             if charges > 0.0:
        #                 handle_inflow_commission.delay(
        #                     transaction_id=credit_transaction.id
        #                 )
        #             send_company_transaction_callbacks.delay(
        #                 transaction_id=credit_transaction.id
        #             )
        #             print(
        #                 f"SUCCESSFULLY UPDATED ACCOUNT DETAILS WITH SESSION ID: {session_id}."
        #             )
        #     else:
        #         print(
        #             f"DUPLICATE TRANSACTION: {session_id}."
        #         )
        # else:
        #     print(
        #         f"VIRTUAL ACCOUNT DETAILS NOT FOUND FOR: {beneficiary_account}."
        #     )
        #     time.sleep(2)
        #     pass
        self.stdout.write(
            self.style.SUCCESS(
                "SYSTEM HAS COMPLETED A INFLOW EVENT UPDATE")
        )
