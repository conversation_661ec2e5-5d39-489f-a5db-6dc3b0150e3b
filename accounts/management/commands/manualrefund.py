import time

from django.core.management.base import (
    BaseCommand,
    CommandParser,
)
from django.db import transaction

from accounts import models
from helpers import enums


class Command(BaseCommand):
    help = "MANUAL REFUND OF FAILED TRANSFER MONEY REQUEST / NIP TRANSFERS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("request_date")
        return super().add_arguments(parser)

    @transaction.atomic()
    def handle(self, *args, **kwargs):
        request_date = kwargs["request_date"]
        pending_refunds = (
            models.TransferMoneyRequest.objects.filter(
                invalid_request=False,
                refunded=False,
                status=enums.TransactionStatus.FAILED,
                created_at__date__gte=request_date,
                service_provider=enums.ServiceProvider.WEMA_BANK,
            )
        )
        print(f"NUMBER OF PENDING REFUNDS:      {pending_refunds.count()}")
        time.sleep(10)
        for refund in pending_refunds:
            if refund.status == enums.TransactionStatus.FAILED:
                time.sleep(5)
                self.stdout.write(
                    self.style.SUCCESS(
                        "SYSTEM WILL TRY AND DO A REFUND TO THE COMPANY")
                )
                account_details = models.AccountDetail.objects.filter(
                    account_number=refund.source_account
                ).last()
                wallet_funding = models.AccountDetail.fund_account(
                    company=refund.company,
                    account_type=account_details.account_type,
                    amount=refund.amount,
                    sub_company=account_details.sub_company,
                    provider=account_details.provider,
                )
                reversal = models.TransactionDetail.register_transaction(
                    company=refund.company,
                    beneficiary_account_number=refund.account_number,
                    beneficiary_account_name=refund.account_name,
                    amount=refund.amount,
                    fee=0.0,
                    amount_payable=refund.amount,
                    transaction_type=enums.TransactionType.CREDIT,
                    transaction_status=enums.TransactionStatus.REVERSED,
                    narration=refund.narration,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                    mode=refund.mode,
                    bank_code=refund.bank_code,
                    source_account=refund.source_account,
                    company_reference=refund.request_reference,
                )
                if not isinstance(reversal, bool):
                    reversal.balance_before = wallet_funding.get(
                        "previous_balance"
                    )
                    reversal.balance_after = wallet_funding.get(
                        "account_balance")
                    reversal.save()

                    refund.refunded = True
                    refund.save()

                    self.stdout.write(
                        self.style.SUCCESS(
                            "SYSTEM HAS COMPLETED A REFUND TO THE COMPANY")
                    )
