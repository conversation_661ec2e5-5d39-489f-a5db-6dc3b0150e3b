from django.core.management.base import (
    <PERSON><PERSON>om<PERSON>,
    <PERSON><PERSON><PERSON>r,
    Command<PERSON>arser,
)

from accounts import models
from helpers import enums
from user_profiles.models import Company


class Command(BaseCommand):
    help = "MANUAL VERIFICATION OF TRANSFER MONEY REQUEST/NIP TRANSFERS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("company_id")
        parser.add_argument("threshold")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        company_id = kwargs["company_id"]
        threshold = kwargs["threshold"]
        company = Company.objects.filter(id=company_id).last()
        if company is None:
            raise CommandError("INVALID COMPANY ID PROVIDED!")

        company_transactions = models.TransactionDetail.objects.filter(
            created_at__date=threshold,
            company=company
        )
        if company_transactions.exists():
            balance = models.CompanyAccountBalance.objects.create(
                company=company,
                opening_balance=company_transactions.last().balance_before,
                closing_balance=company_transactions.first().balance_after,
                provider=enums.ServiceProvider.WEMA_BANK,
            )
            models.CompanyAccountBalance.objects.filter(
                id=balance.id
            ).update(
                created_at=company_transactions.last().updated_at,
                updated_at=company_transactions.first().updated_at,
            )
        self.stdout.write(
            self.style.SUCCESS(
                "COMPANY ACCOUNT BALANCES HAVE BEEN CREATED SUCCESSFULLY.")
        )
