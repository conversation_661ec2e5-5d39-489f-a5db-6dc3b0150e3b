from django.core.management.base import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CommandParser,
)

from accounts import models
from helpers import enums


class Command(BaseCommand):
    help = "<PERSON><PERSON><PERSON> MISSING RECORD FOR FAILED TRANSFER MONEY REQUESTS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("company_reference")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        company_reference = kwargs["company_reference"]
        money_transfer_request = (
            models.TransferMoneyRequest.objects.filter(
                request_reference=company_reference,
            )
            .first()
        )
        if money_transfer_request is None:
            raise CommandError("INVALID COMPANY REFERENCE PROVIDED!")
        record = models.TransactionDetail.register_transaction(
            company=money_transfer_request.company,
            beneficiary_account_number=money_transfer_request.account_number,
            beneficiary_account_name=money_transfer_request.account_name,
            amount=money_transfer_request.amount,
            fee=0.0,
            amount_payable=money_transfer_request.amount,
            transaction_type=enums.TransactionType.DEBIT,
            transaction_status=enums.TransactionStatus.FAILED,
            narration=money_transfer_request.narration,
            service_provider=enums.ServiceProvider.WEMA_BANK,
            mode=money_transfer_request.mode,
            bank_code=money_transfer_request.bank_code,
            source_account=money_transfer_request.source_account,
            company_reference=money_transfer_request.request_reference,
        )
        record.created_at = money_transfer_request.created_at
        record.balance_before = money_transfer_request.balance_before
        record.balance_after = money_transfer_request.balance_after
        record.wallet_charge_response = money_transfer_request.wallet_charge_response
        record.save()
        self.stdout.write(
            self.style.SUCCESS(
                "SYSTEM HAS COMPLETED MANAGING RECORDs.")
        )
