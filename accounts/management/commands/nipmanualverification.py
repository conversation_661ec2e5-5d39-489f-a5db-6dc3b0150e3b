from datetime import datetime
import json
import time

from django.conf import settings
from django.core.management.base import (
    <PERSON><PERSON>ommand,
    CommandError,
    CommandParser,
)
import pytz

from accounts import models
from helpers import enums
from wema_bank.helper.api_specifications import WemaBank


class Command(BaseCommand):
    help = "MANUAL VERIFICATION OF TRANSFER MONEY REQUEST/NIP TRANSFERS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("request_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        request_id = kwargs["request_id"]
        money_transfer_request = models.TransferMoneyRequest.objects.filter(
            id=request_id,
            refunded=False,
        ).last()
        # transfer_requests = models.TransferMoneyRequest.objects.filter(
        #     status=enums.TransactionStatus.AWAITING_CONFIRMATION
        # )
        # for money_transfer_request in transfer_requests:
        if money_transfer_request is None:
            raise CommandError("INVALID REQUEST ID PROVIDED!")
        outward_tsq = WemaBank.get_transaction_status(
            reference=money_transfer_request.id
        )
        if (
            not outward_tsq.get("status")
            and outward_tsq.get("wema_response") == "Transaction Reference not found"
        ):
            nip_status = enums.TransactionStatus.FAILED
        if outward_tsq.get("status"):
            data = json.loads(outward_tsq.get("wema_response"))
            response_code = data.get("Response").split("|")[0]
            if response_code == "00":
                nip_status = enums.TransactionStatus.SUCCESSFUL
            if response_code in ["09", "20", "97"]:
                nip_status = enums.TransactionStatus.IN_PROGRESS
            if response_code != "00" and response_code not in ["09", "20", "97"]:
                nip_status = enums.TransactionStatus.FAILED
            if response_code == "XX":
                nip_status = enums.TransactionStatus.FAILED
        # Update the transfer money request record.
        money_transfer_request.status = nip_status
        money_transfer_request.verified_at = datetime.now(
            tz=pytz.timezone(settings.TIME_ZONE)
        )
        money_transfer_request.verification_response = json.dumps(
            outward_tsq
        )
        money_transfer_request.save()
        # Update the company's transaction record.
        associated_transaction = models.TransactionDetail.objects.filter(
            company_reference=money_transfer_request.request_reference
        ).last()
        associated_transaction.transaction_status = nip_status
        associated_transaction.is_verified = True
        associated_transaction.verification_response = json.dumps(
            outward_tsq
        )
        associated_transaction.save()
        time.sleep(2)
        self.stdout.write(
            self.style.SUCCESS(
                "TRANSFER MONEY REQUEST HAS BEEN VERIFIED SUCCESSFULLY.")
        )
