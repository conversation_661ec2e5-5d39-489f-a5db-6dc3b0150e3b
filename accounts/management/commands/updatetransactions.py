import time

from django.core.management.base import BaseCommand
from django.db import transaction

from accounts import models
from helpers import enums


class Command(BaseCommand):
    @transaction.atomic()
    def handle(self, *args, **kwargs):
        pending_transactions = (
            models.TransactionDetail.objects.filter(
                transaction_status=enums.TransactionStatus.PENDING,
                transfer_mode=enums.TransferMode.INTERNAL,
            )
        )
        print(
            f"NUMBER OF PENDING TRANSACTIONS:      {pending_transactions.count()}")
        time.sleep(5)
        for debit in pending_transactions:
            money_request = models.TransferMoneyRequest.objects.filter(
                request_reference=debit.company_reference
            ).last()
            if money_request is not None:
                debit.transaction_status = money_request.status
                debit.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        "SYSTEM HAS COMPLETED A TRANSACTION UPDATE.")
                )
