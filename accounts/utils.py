from datetime import datetime
import os
from typing import Dict, Any
import pandas as pd
from django.conf import settings

from accounts.models import (
    AccountDetail,
    TransactionDetail,
    TransferMoneyRequest,
)
from accounts.tasks import (
    inward_transfer_handler,
    outward_transfer_handler,
)
from helpers import enums
from helpers.reusable import format_currency


# Utility manager.
def wema_funds_transfer(request_id: str):
    transfer_request = TransferMoneyRequest.objects.filter(
        id=request_id,
        status=enums.TransactionStatus.PENDING,
    ).last()
    if transfer_request.mode == enums.APIMode.LIVE:
        charge_user = AccountDetail.charge_account(
            company=transfer_request.company,
            source_account=transfer_request.source_account,
            amount=transfer_request.amount,
        )
        if not charge_user.get("status"):
            transfer_request.invalid_request = True
            transfer_request.balance_before = float(charge_user.get("previous_balance"))
            transfer_request.balance_after = float(charge_user.get("account_balance"))
            transfer_request.status = enums.TransactionStatus.FAILED
            transfer_request.wallet_charge_response = str(charge_user)
            transfer_request.save()
            # company associated debit transaction.
            debit_transaction = TransactionDetail.register_transaction(
                company=transfer_request.company,
                beneficiary_account_number=transfer_request.account_number,
                beneficiary_account_name=transfer_request.account_name,
                amount=transfer_request.amount,
                fee=0.0,  # currently hard-coded.
                amount_payable=transfer_request.amount,
                transaction_type=enums.TransactionType.DEBIT,
                transaction_status=enums.TransactionStatus.FAILED,
                narration=transfer_request.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=transfer_request.mode,
                transfer_mode=transfer_request.transfer_mode,
                bank_code=transfer_request.bank_code,
                source_account=transfer_request.source_account,
                company_reference=transfer_request.request_reference,
            )
            if not isinstance(debit_transaction, bool):
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.wallet_charge_response = str(charge_user)
                debit_transaction.save()
                return {
                    "status": False,
                    "message": charge_user.get("message"),
                    "transaction": None,
                    "account_balance": None,
                }
            else:
                return {
                    "status": False,
                    "message": "duplicate transaction",
                    "transaction": None,
                    "account_balance": float(charge_user.get("account_balance")),
                }
        if charge_user.get("status"):
            transfer_request.balance_before = float(charge_user.get("previous_balance"))
            transfer_request.balance_after = float(charge_user.get("account_balance"))
            transfer_request.wallet_charge_response = str(charge_user)
            transfer_request.save()
            # company associated debit transaction.
            debit_transaction = TransactionDetail.register_transaction(
                company=transfer_request.company,
                beneficiary_account_number=transfer_request.account_number,
                beneficiary_account_name=transfer_request.account_name,
                amount=transfer_request.amount,
                fee=0.0,  # currently hard-coded.
                amount_payable=transfer_request.amount,
                transaction_type=enums.TransactionType.DEBIT,
                transaction_status=enums.TransactionStatus.PENDING,
                narration=transfer_request.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=transfer_request.mode,
                transfer_mode=transfer_request.transfer_mode,
                bank_code=transfer_request.bank_code,
                source_account=transfer_request.source_account,
                company_reference=transfer_request.request_reference,
            )
            if not isinstance(debit_transaction, bool):
                debit_transaction.balance_before = float(
                    charge_user.get("previous_balance")
                )
                debit_transaction.balance_after = float(
                    charge_user.get("account_balance")
                )
                debit_transaction.wallet_charge_response = str(charge_user)
                debit_transaction.save()
                if transfer_request.transfer_mode == enums.TransferMode.INTERNAL:
                    inward_transfer_handler.apply_async(
                        queue="nip",
                        args=[
                            transfer_request.id,
                        ],
                    )
                    return {
                        "status": True,
                        "message": "request completed successfully.",
                        "transaction": transfer_request,
                        "account_balance": float(charge_user.get("account_balance")),
                    }
                else:
                    outward_transfer_handler.apply_async(
                        queue="nip",
                        args=[
                            transfer_request.id,
                        ],
                    )
                    return {
                        "status": True,
                        "message": "request completed successfully.",
                        "transaction": transfer_request,
                        "account_balance": float(charge_user.get("account_balance")),
                    }
            else:
                return {
                    "status": False,
                    "message": "duplicate transaction",
                    "transaction": None,
                    "account_balance": float(charge_user.get("account_balance")),
                }


def process_wema_account_statement(uploaded_file) -> Dict[str, Any]:
    """
    Process Excel file to query TransactionDetail records and add company name and transaction date.
    Args:
        uploaded_file: The uploaded Excel file
    Returns:
        Dict containing statistics and download link for processed file
    """
    try:
        # Use engine='openpyxl' for Excel file processing
        df = pd.read_excel(uploaded_file, engine="openpyxl")
        if "TranRmks" not in df.columns:
            return {
                "success": False,
                "error": "'TranRmks' column not found in the Excel file.",
            }
        df["Local Owner"] = "NOT FOUND"
        df["Local Date"] = "NOT FOUND"

        session_ids = df["TranRmks"].dropna().unique()

        transactions = TransactionDetail.objects.filter(
            session_id__in=session_ids
        ).select_related("company")

        # Create a mapping of session_id to transaction data
        transaction_map = {}
        for transaction in transactions:
            transaction_map[transaction.session_id] = {
                "local_owner": transaction.company.name,
                "local_date": transaction.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            }

        found_count = 0
        total_count = len(df)

        for index, row in df.iterrows():
            session_id = row["TranRmks"]
            if pd.notna(session_id) and session_id in transaction_map:
                df.at[index, "Local Owner"] = transaction_map[session_id]["local_owner"]
                df.at[index, "Local Date"] = transaction_map[session_id]["local_date"]
                found_count += 1

        missing_count = total_count - found_count

        # Determine the month from the data (try to extract from transaction dates or use current month)
        report_month = "unknown"
        if found_count > 0:
            # Get the most common month from the transaction dates
            months = []
            for session_id in session_ids:
                if session_id in transaction_map:
                    transaction_date = transaction_map[session_id]["local_date"]
                    try:
                        date_obj = datetime.strptime(
                            transaction_date, "%Y-%m-%d %H:%M:%S"
                        )
                        # Full month name in lowercase
                        months.append(date_obj.strftime("%B").lower())
                    except:
                        continue

            if months:
                # Get the most frequent month
                report_month = max(set(months), key=months.count)
            else:
                report_month = datetime.now().strftime("%B").lower()
        else:
            report_month = datetime.now().strftime("%B").lower()

        # Generate file name using upload date
        upload_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"{upload_date}.xlsx"

        # Create the file path within MEDIA_ROOT to ensure proper serving
        media_file_path = os.path.join(
            settings.MEDIA_ROOT,
            "reports",
            "account_statement",
            f"{report_month}_report",
        )
        media_file_full_path = os.path.join(media_file_path, file_name)

        os.makedirs(media_file_path, exist_ok=True)
        df.to_excel(media_file_full_path, index=False)
        file_exists = os.path.exists(media_file_full_path)
        file_size = os.path.getsize(media_file_full_path) if file_exists else 0

        # Generate download URL using our custom download view
        relative_path = f"reports/account_statement/{report_month}_report/{file_name}"
        download_url = f"/accounts/download/{relative_path}"

        return {
            "success": True,
            "total_count": total_count,
            "found_count": found_count,
            "missing_count": missing_count,
            "download_url": download_url,
            "file_path": media_file_full_path,
            "report_category": "account_statement",
            "report_type": report_month,
            "file_name": file_name,
            "file_exists": file_exists,
            "file_size": file_size,
        }
    except Exception as e:
        return {"success": False, "error": f"Error processing file: {str(e)}"}


def generate_monthly_transaction_summary_excel() -> Dict[str, Any]:
    """
    Generate an Excel file with monthly transaction summaries per company.
    Only includes successful transactions and calculates total debit and credit amounts.

    Returns:
        Dict containing success status, download URL, and file information
    """
    try:
        from django.db.models import Sum, Count
        from django.db.models.functions import TruncMonth
        from datetime import datetime
        import pandas as pd
        import os
        from django.conf import settings
        from accounts import models
        from helpers import enums

        # Query successful transactions grouped by company and month
        successful_transactions = (
            models.TransactionDetail.objects.filter(
                transaction_status=enums.TransactionStatus.SUCCESSFUL,
                service_provider=enums.ServiceProvider.WEMA_BANK,
            )
            .annotate(month_year=TruncMonth("created_at"))
            .values("company__name", "month_year", "transaction_type")
            .annotate(total_amount=Sum("amount"), transaction_count=Count("id"))
            .order_by("company__name", "month_year", "transaction_type")
        )

        # Convert to list for easier processing
        transaction_data = list(successful_transactions)

        if not transaction_data:
            return {
                "success": False,
                "error": "No successful transactions found in the database.",
            }

        # Process data to create monthly summaries
        summary_data = []

        # Group by company and month
        company_month_data = {}
        for transaction in transaction_data:
            company_name = transaction["company__name"]
            month_year = transaction["month_year"]
            transaction_type = transaction["transaction_type"]
            total_amount = float(transaction["total_amount"])
            transaction_count = transaction["transaction_count"]

            # Create unique key for company-month combination
            key = f"{company_name}_{month_year.strftime('%Y-%m')}"

            if key not in company_month_data:
                company_month_data[key] = {
                    "company_name": company_name,
                    "month_year": month_year,
                    "total_debit": 0.0,
                    "total_credit": 0.0,
                    "debit_count": 0,
                    "credit_count": 0,
                }

            if transaction_type == enums.TransactionType.DEBIT:
                company_month_data[key]["total_debit"] += total_amount
                company_month_data[key]["debit_count"] += transaction_count
            elif transaction_type == enums.TransactionType.CREDIT:
                company_month_data[key]["total_credit"] += total_amount
                company_month_data[key]["credit_count"] += transaction_count

        # Calculate opening balances for each company-month combination
        def get_opening_balance(company_name, target_month_year):
            """
            Get opening balance for a company in a specific month.
            This is the balance_after from the last transaction of the previous month.
            """
            from dateutil.relativedelta import relativedelta

            # Calculate previous month
            previous_month = target_month_year - relativedelta(months=1)

            # Get the last transaction from the previous month for this company
            last_transaction = (
                models.TransactionDetail.objects.filter(
                    company__name=company_name,
                    transaction_status=enums.TransactionStatus.SUCCESSFUL,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                    created_at__year=previous_month.year,
                    created_at__month=previous_month.month,
                )
                .order_by("-created_at")
                .first()
            )

            if last_transaction:
                return float(last_transaction.balance_after)
            else:
                # If no previous transaction, try to get the last transaction before this month
                last_any_transaction = (
                    models.TransactionDetail.objects.filter(
                        company__name=company_name,
                        transaction_status=enums.TransactionStatus.SUCCESSFUL,
                        service_provider=enums.ServiceProvider.WEMA_BANK,
                        created_at__lt=target_month_year,
                    )
                    .order_by("-created_at")
                    .first()
                )

                if last_any_transaction:
                    return float(last_any_transaction.balance_after)
                else:
                    return 0.0

        # Add opening and closing balances to each month's data
        for key, data in company_month_data.items():
            opening_balance = get_opening_balance(
                data["company_name"], data["month_year"]
            )
            data["opening_balance"] = opening_balance

            # Get closing balance from the last transaction of the current month
            last_transaction_current_month = (
                models.TransactionDetail.objects.filter(
                    company__name=data["company_name"],
                    transaction_status=enums.TransactionStatus.SUCCESSFUL,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                    created_at__year=data["month_year"].year,
                    created_at__month=data["month_year"].month,
                )
                .order_by("-created_at")
                .first()
            )

            if last_transaction_current_month:
                closing_balance = float(last_transaction_current_month.balance_after)
            else:
                # Fallback to opening balance if no transactions in current month
                closing_balance = opening_balance

            data["closing_balance"] = closing_balance

        # Convert to list format for DataFrame
        for key, data in company_month_data.items():
            summary_data.append(
                {
                    "Company Name": data["company_name"],
                    "Month": data["month_year"].strftime("%B %Y"),
                    "Month Number": data["month_year"].month,
                    "Year": data["month_year"].year,
                    "Opening Balance": f"₦{data['opening_balance']:,.2f}",
                    "Total Debit Amount": f"₦{data['total_debit']:,.2f}",
                    "Total Credit Amount": f"₦{data['total_credit']:,.2f}",
                    "Closing Balance": f"₦{data['closing_balance']:,.2f}",
                    "Debit Transaction Count": data["debit_count"],
                    "Credit Transaction Count": data["credit_count"],
                    "Net Amount": f"₦{(data['total_credit'] - data['total_debit']):,.2f}",
                    "Total Transactions": data["debit_count"] + data["credit_count"],
                }
            )

        # Create DataFrame
        df = pd.DataFrame(summary_data)

        # Sort by company name, year, and month number for proper chronological order
        df = df.sort_values(["Company Name", "Year", "Month Number"])

        # Generate file name with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"monthly_transaction_summary_{timestamp}.xlsx"

        # Create directory structure
        report_dir = os.path.join(settings.MEDIA_ROOT, "reports", "transaction_summary")
        os.makedirs(report_dir, exist_ok=True)

        # Full file path
        file_path = os.path.join(report_dir, file_name)

        # Save to Excel with formatting
        with pd.ExcelWriter(file_path, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="Monthly Summary", index=False)

            # Get the worksheet for formatting
            worksheet = writer.sheets["Monthly Summary"]

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        # Generate download URL
        relative_path = f"reports/transaction_summary/{file_name}"
        download_url = f"/accounts/download/{relative_path}"

        # File statistics
        file_exists = os.path.exists(file_path)
        file_size = os.path.getsize(file_path) if file_exists else 0

        return {
            "success": True,
            "download_url": download_url,
            "file_path": file_path,
            "file_name": file_name,
            "file_exists": file_exists,
            "file_size": file_size,
            "total_records": len(summary_data),
            "report_generated_at": datetime.now().isoformat(),
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error generating monthly transaction summary: {str(e)}",
        }


def generate_detailed_monthly_transaction_summary() -> Dict[str, Any]:
    """
    Generate an Excel file with monthly transaction summaries without grouping by company.
    Only includes successful transactions and calculates total debit and credit amounts.
    Includes a checker column to validate transaction balance calculations.

    Optimized for large datasets with memory management and batch processing.

    Returns:
        Dict containing success status, download URL, and file information
    """
    try:
        # Check for production safe mode
        import os as env_os

        if (
            env_os.environ.get("TRANSACTION_SUMMARY_SAFE_MODE", "false").lower()
            == "true"
        ):
            return generate_ultra_lightweight_monthly_summary()

        # Import dependencies with error handling
        try:
            from django.db.models import Sum, Count
            from django.db.models.functions import TruncMonth
            from datetime import datetime
            import pandas as pd
            import os
            from django.conf import settings
            from accounts import models
            from helpers import enums
        except ImportError as e:
            return {
                "success": False,
                "error": f"Import error: {str(e)}. Please ensure all dependencies are installed.",
            }

        # Add logging for debugging
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Starting detailed monthly transaction summary generation")

        # Query successful transactions grouped by month only (no company grouping)
        try:
            successful_transactions = (
                models.TransactionDetail.objects.filter(
                    transaction_status=enums.TransactionStatus.SUCCESSFUL,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                )
                .annotate(month_year=TruncMonth("created_at"))
                .values("month_year", "transaction_type")
                .annotate(total_amount=Sum("amount"), transaction_count=Count("id"))
                .order_by("month_year", "transaction_type")
            )

            # Convert to list for easier processing
            transaction_data = list(successful_transactions)
            logger.info(f"Found {len(transaction_data)} transaction groups")

            if not transaction_data:
                return {
                    "success": False,
                    "error": "No successful transactions found in the database.",
                }
        except Exception as e:
            logger.error(f"Error querying transactions: {str(e)}")
            return {
                "success": False,
                "error": f"Database query error: {str(e)}",
            }

        # Process data to create monthly summaries
        summary_data = []

        # Group by month only (no company)
        month_data = {}
        for transaction in transaction_data:
            month_year = transaction["month_year"]
            transaction_type = transaction["transaction_type"]
            total_amount = float(transaction["total_amount"])
            transaction_count = transaction["transaction_count"]

            # Create unique key for month combination
            key = month_year.strftime("%Y-%m")

            if key not in month_data:
                month_data[key] = {
                    "month_year": month_year,
                    "total_debit": 0.0,
                    "total_credit": 0.0,
                    "debit_count": 0,
                    "credit_count": 0,
                }

            if transaction_type == enums.TransactionType.DEBIT:
                month_data[key]["total_debit"] += total_amount
                month_data[key]["debit_count"] += transaction_count
            elif transaction_type == enums.TransactionType.CREDIT:
                month_data[key]["total_credit"] += total_amount
                month_data[key]["credit_count"] += transaction_count

        # Calculate opening balances for each month
        def get_opening_balance_for_month(target_month_year):
            """
            Get opening balance for a specific month across all companies.
            This is the sum of balance_after from the last transaction of each company in the previous month.
            """
            from dateutil.relativedelta import relativedelta

            # Calculate previous month
            previous_month = target_month_year - relativedelta(months=1)

            # Get all companies that had transactions
            companies_with_transactions = (
                models.TransactionDetail.objects.filter(
                    transaction_status=enums.TransactionStatus.SUCCESSFUL,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                )
                .values_list("company__name", flat=True)
                .distinct()
            )

            total_opening_balance = 0.0

            for company_name in companies_with_transactions:
                # Get the last transaction from the previous month for this company
                last_transaction = (
                    models.TransactionDetail.objects.filter(
                        company__name=company_name,
                        transaction_status=enums.TransactionStatus.SUCCESSFUL,
                        service_provider=enums.ServiceProvider.WEMA_BANK,
                        created_at__year=previous_month.year,
                        created_at__month=previous_month.month,
                    )
                    .order_by("-created_at")
                    .first()
                )

                if last_transaction:
                    total_opening_balance += float(last_transaction.balance_after)
                else:
                    # If no previous transaction, try to get the last transaction before this month
                    last_any_transaction = (
                        models.TransactionDetail.objects.filter(
                            company__name=company_name,
                            transaction_status=enums.TransactionStatus.SUCCESSFUL,
                            service_provider=enums.ServiceProvider.WEMA_BANK,
                            created_at__lt=target_month_year,
                        )
                        .order_by("-created_at")
                        .first()
                    )

                    if last_any_transaction:
                        total_opening_balance += float(
                            last_any_transaction.balance_after
                        )

            return total_opening_balance

        # Add opening and closing balances to each month's data
        for key, data in month_data.items():
            opening_balance = get_opening_balance_for_month(data["month_year"])
            data["opening_balance"] = opening_balance

            # Get closing balance from the sum of last transactions of all companies in the current month
            companies_with_transactions = (
                models.TransactionDetail.objects.filter(
                    transaction_status=enums.TransactionStatus.SUCCESSFUL,
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                    created_at__year=data["month_year"].year,
                    created_at__month=data["month_year"].month,
                )
                .values_list("company__name", flat=True)
                .distinct()
            )

            total_closing_balance = 0.0
            for company_name in companies_with_transactions:
                last_transaction_current_month = (
                    models.TransactionDetail.objects.filter(
                        company__name=company_name,
                        transaction_status=enums.TransactionStatus.SUCCESSFUL,
                        service_provider=enums.ServiceProvider.WEMA_BANK,
                        created_at__year=data["month_year"].year,
                        created_at__month=data["month_year"].month,
                    )
                    .order_by("-created_at")
                    .first()
                )

                if last_transaction_current_month:
                    total_closing_balance += float(
                        last_transaction_current_month.balance_after
                    )

            if total_closing_balance == 0.0:
                # Fallback to opening balance if no transactions in current month
                total_closing_balance = opening_balance

            data["closing_balance"] = total_closing_balance

        # Process transactions in chunks to avoid memory issues with large datasets
        logger.info("Processing detailed transactions in chunks to avoid memory issues")

        # Limit the number of detailed transactions to prevent memory issues
        MAX_DETAILED_TRANSACTIONS = (
            5000  # Reduced to 5k transactions for production stability
        )

        all_transactions = (
            models.TransactionDetail.objects.filter(
                transaction_status=enums.TransactionStatus.SUCCESSFUL,
                service_provider=enums.ServiceProvider.WEMA_BANK,
            )
            .annotate(month_year=TruncMonth("created_at"))
            .order_by("-created_at")[:MAX_DETAILED_TRANSACTIONS]
        )

        # Create detailed transaction data with checker column
        detailed_data = []
        batch_size = 500  # Reduced batch size for production stability

        # Convert queryset to list to enable slicing
        transaction_list = list(all_transactions)
        logger.info(
            f"Processing {len(transaction_list)} transactions in batches of {batch_size}"
        )

        for i in range(0, len(transaction_list), batch_size):
            batch = transaction_list[i : i + batch_size]
            logger.info(
                f"Processing batch {i//batch_size + 1}, transactions {i+1} to {min(i+batch_size, len(transaction_list))}"
            )

            for transaction in batch:
                # Calculate checker value
                checker = False
            if transaction.transaction_type == enums.TransactionType.CREDIT:
                # For CREDIT: balance_after - amount should equal balance_before
                checker = (
                    abs(
                        (transaction.balance_after - transaction.amount)
                        - transaction.balance_before
                    )
                    < 0.01
                )
            elif transaction.transaction_type == enums.TransactionType.DEBIT:
                # For DEBIT: balance_after + amount should equal balance_before
                checker = (
                    abs(
                        (transaction.balance_after + transaction.amount)
                        - transaction.balance_before
                    )
                    < 0.01
                )

            detailed_data.append(
                {
                    "Transaction ID": transaction.id,
                    "Company Name": transaction.company.name,
                    "Month": transaction.month_year.strftime("%B %Y"),
                    "Transaction Date": transaction.created_at.strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "Transaction Type": transaction.transaction_type,
                    "Amount": f"₦{transaction.amount:,.2f}",
                    "Balance Before": f"₦{transaction.balance_before:,.2f}",
                    "Balance After": f"₦{transaction.balance_after:,.2f}",
                    "Beneficiary Account Name": transaction.beneficiary_account_name,
                    "Reference": transaction.reference,
                    "Narration": transaction.narration,
                    "Checker": checker,
                }
            )

            # Force garbage collection between batches to free memory
            if (i + batch_size) % (batch_size * 2) == 0:  # Every 2 batches
                import gc

                gc.collect()
                logger.info(
                    f"Garbage collection performed after batch {i//batch_size + 1}"
                )

        logger.info(f"Completed processing {len(detailed_data)} detailed transactions")

        # Convert to list format for DataFrame (monthly summary)
        for key, data in month_data.items():
            summary_data.append(
                {
                    "Month": data["month_year"].strftime("%B %Y"),
                    "Month Number": data["month_year"].month,
                    "Year": data["month_year"].year,
                    "Opening Balance": f"₦{data['opening_balance']:,.2f}",
                    "Total Debit Amount": f"₦{data['total_debit']:,.2f}",
                    "Total Credit Amount": f"₦{data['total_credit']:,.2f}",
                    "Closing Balance": f"₦{data['closing_balance']:,.2f}",
                    "Debit Transaction Count": data["debit_count"],
                    "Credit Transaction Count": data["credit_count"],
                    "Net Amount": f"₦{(data['total_credit'] - data['total_debit']):,.2f}",
                    "Total Transactions": data["debit_count"] + data["credit_count"],
                }
            )

        # Create DataFrames with error handling
        try:
            logger.info(
                f"Creating DataFrames with {len(summary_data)} summary records and {len(detailed_data)} detailed records"
            )
            summary_df = pd.DataFrame(summary_data)
            detailed_df = pd.DataFrame(detailed_data)

            # Sort by year and month number for proper chronological order
            summary_df = summary_df.sort_values(["Year", "Month Number"])
            detailed_df = detailed_df.sort_values(["Transaction Date"])

            # Group data by year for separate sheets
            summary_by_year = {}
            detailed_by_year = {}

            # Group summary data by year
            for year in summary_df["Year"].unique():
                year_summary = summary_df[summary_df["Year"] == year].copy()
                summary_by_year[year] = year_summary

            # Group detailed data by year (extract year from transaction date)
            detailed_df["Year"] = pd.to_datetime(
                detailed_df["Transaction Date"]
            ).dt.year
            for year in detailed_df["Year"].unique():
                year_detailed = detailed_df[detailed_df["Year"] == year].copy()
                # Remove the Year column from detailed data as it's not needed in the output
                year_detailed = year_detailed.drop("Year", axis=1)
                detailed_by_year[year] = year_detailed

            logger.info(f"Successfully grouped data by {len(summary_by_year)} years")
        except Exception as e:
            logger.error(f"Error processing DataFrames: {str(e)}")
            return {
                "success": False,
                "error": f"Data processing error: {str(e)}",
            }

        # Generate file name with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"monthly_transaction_summary_no_company_{timestamp}.xlsx"

        # Create directory structure
        try:
            report_dir = os.path.join(
                settings.MEDIA_ROOT, "reports", "transaction_summary"
            )
            os.makedirs(report_dir, exist_ok=True)

            # Full file path
            file_path = os.path.join(report_dir, file_name)
            logger.info(f"Generating Excel file: {file_path}")

            # Save to Excel with formatting (multiple sheets grouped by year)
            with pd.ExcelWriter(file_path, engine="openpyxl") as writer:
                # Create sheets for each year
                years = sorted(
                    set(summary_by_year.keys()) | set(detailed_by_year.keys())
                )
                logger.info(f"Creating sheets for years: {years}")

                for year in years:
                    # Summary sheet for this year
                    if year in summary_by_year:
                        summary_sheet_name = f"{year} Summary"
                        summary_by_year[year].to_excel(
                            writer, sheet_name=summary_sheet_name, index=False
                        )
                        logger.info(f"Created summary sheet for {year}")

                    # Detailed transactions sheet for this year
                    if year in detailed_by_year:
                        detailed_sheet_name = f"{year} Transactions"
                        detailed_by_year[year].to_excel(
                            writer, sheet_name=detailed_sheet_name, index=False
                        )
                        logger.info(f"Created transactions sheet for {year}")

                # Set standard column widths to avoid memory-intensive auto-adjustment
                try:
                    standard_widths = {
                        "A": 15,  # Transaction ID / Month
                        "B": 25,  # Company Name / Month Number
                        "C": 15,  # Month / Year
                        "D": 20,  # Transaction Date / Opening Balance
                        "E": 15,  # Transaction Type / Total Debit
                        "F": 15,  # Amount / Total Credit
                        "G": 15,  # Balance Before / Closing Balance
                        "H": 15,  # Balance After / Debit Count
                        "I": 30,  # Beneficiary / Credit Count
                        "J": 20,  # Reference / Net Amount
                        "K": 30,  # Narration / Total Transactions
                        "L": 10,  # Checker
                    }

                    for sheet_name in writer.sheets:
                        worksheet = writer.sheets[sheet_name]
                        for col_letter, width in standard_widths.items():
                            worksheet.column_dimensions[col_letter].width = width

                    logger.info("Applied standard column widths to all sheets")
                except Exception as e:
                    logger.warning(f"Error setting column widths: {str(e)}")
                    # Continue without column width adjustment

            logger.info("Excel file generated successfully")
        except Exception as e:
            logger.error(f"Error generating Excel file: {str(e)}")
            return {
                "success": False,
                "error": f"Excel generation error: {str(e)}",
            }

        # Generate download URL
        relative_path = f"reports/transaction_summary/{file_name}"
        download_url = f"/accounts/download/{relative_path}"

        # File statistics
        file_exists = os.path.exists(file_path)
        file_size = os.path.getsize(file_path) if file_exists else 0

        return {
            "success": True,
            "download_url": download_url,
            "file_path": file_path,
            "file_name": file_name,
            "file_exists": file_exists,
            "file_size": file_size,
            "total_summary_records": len(summary_data),
            "total_detailed_records": len(detailed_data),
            "report_generated_at": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(
            f"Critical error in generate_detailed_monthly_transaction_summary: {str(e)}"
        )
        # Try ultra-lightweight version first, then simple fallback
        try:
            logger.info("Attempting ultra-lightweight fallback")
            return generate_ultra_lightweight_monthly_summary()
        except Exception as lightweight_error:
            logger.error(f"Ultra-lightweight fallback failed: {str(lightweight_error)}")
            try:
                logger.info("Attempting simple fallback")
                return generate_simple_monthly_transaction_summary()
            except Exception as simple_error:
                logger.error(f"All fallbacks failed: {str(simple_error)}")
                return {
                    "success": False,
                    "error": f"Error generating monthly transaction summary: {str(e)}. All fallbacks failed.",
                }


def generate_simple_monthly_transaction_summary() -> Dict[str, Any]:
    """
    Simple fallback version of the monthly transaction summary without advanced features.
    This is used when the main function fails.
    """
    try:
        from django.db.models import Sum, Count
        from django.db.models.functions import TruncMonth
        from datetime import datetime
        import pandas as pd
        import os
        from django.conf import settings
        from accounts import models
        from helpers import enums

        # Simple query for successful transactions
        successful_transactions = (
            models.TransactionDetail.objects.filter(
                transaction_status=enums.TransactionStatus.SUCCESSFUL,
                service_provider=enums.ServiceProvider.WEMA_BANK,
            )
            .annotate(month_year=TruncMonth("created_at"))
            .values("month_year", "transaction_type")
            .annotate(total_amount=Sum("amount"), transaction_count=Count("id"))
            .order_by("month_year", "transaction_type")
        )

        transaction_data = list(successful_transactions)

        if not transaction_data:
            return {
                "success": False,
                "error": "No successful transactions found in the database.",
            }

        # Create simple summary data
        summary_data = []
        for transaction in transaction_data:
            summary_data.append(
                {
                    "Month": transaction["month_year"].strftime("%B %Y"),
                    "Transaction Type": transaction["transaction_type"],
                    "Total Amount": f"₦{transaction['total_amount']:,.2f}",
                    "Transaction Count": transaction["transaction_count"],
                }
            )

        # Create simple DataFrame
        df = pd.DataFrame(summary_data)

        # Generate file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"simple_monthly_summary_{timestamp}.xlsx"

        # Create directory
        report_dir = os.path.join(settings.MEDIA_ROOT, "reports", "transaction_summary")
        os.makedirs(report_dir, exist_ok=True)
        file_path = os.path.join(report_dir, file_name)

        # Save simple Excel file
        df.to_excel(file_path, index=False)

        # Generate download URL
        relative_path = f"reports/transaction_summary/{file_name}"
        download_url = f"/accounts/download/{relative_path}"

        # File statistics
        file_exists = os.path.exists(file_path)
        file_size = os.path.getsize(file_path) if file_exists else 0

        return {
            "success": True,
            "download_url": download_url,
            "file_path": file_path,
            "file_name": file_name,
            "file_exists": file_exists,
            "file_size": file_size,
            "total_summary_records": len(summary_data),
            "total_detailed_records": len(
                summary_data
            ),  # Same as summary for simple version
            "report_generated_at": datetime.now().isoformat(),
            "note": "Simple fallback version generated due to error in advanced version",
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error generating simple monthly transaction summary: {str(e)}",
        }


def generate_ultra_lightweight_monthly_summary() -> Dict[str, Any]:
    """
    Ultra-lightweight version for production with large datasets.
    Only generates summary data without detailed transactions.
    """
    try:
        from django.db.models import Sum, Count
        from django.db.models.functions import TruncMonth
        from datetime import datetime
        import pandas as pd
        import os
        from django.conf import settings
        from accounts import models
        from helpers import enums
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Starting ultra-lightweight monthly summary generation")

        # Only get aggregated data, no individual transactions
        successful_transactions = (
            models.TransactionDetail.objects.filter(
                transaction_status=enums.TransactionStatus.SUCCESSFUL,
                service_provider=enums.ServiceProvider.WEMA_BANK,
            )
            .annotate(month_year=TruncMonth("created_at"))
            .values("month_year", "transaction_type")
            .annotate(total_amount=Sum("amount"), transaction_count=Count("id"))
            .order_by("month_year", "transaction_type")
        )

        transaction_data = list(successful_transactions)
        logger.info(f"Found {len(transaction_data)} aggregated transaction groups")

        if not transaction_data:
            return {
                "success": False,
                "error": "No successful transactions found in the database.",
            }

        # Create only summary data (no detailed transactions)
        summary_data = []
        month_data = {}

        for transaction in transaction_data:
            month_year = transaction["month_year"]
            transaction_type = transaction["transaction_type"]
            total_amount = float(transaction["total_amount"])
            transaction_count = transaction["transaction_count"]

            key = month_year.strftime("%Y-%m")

            if key not in month_data:
                month_data[key] = {
                    "month_year": month_year,
                    "total_debit": 0.0,
                    "total_credit": 0.0,
                    "debit_count": 0,
                    "credit_count": 0,
                }

            if transaction_type == enums.TransactionType.DEBIT:
                month_data[key]["total_debit"] += total_amount
                month_data[key]["debit_count"] += transaction_count
            elif transaction_type == enums.TransactionType.CREDIT:
                month_data[key]["total_credit"] += total_amount
                month_data[key]["credit_count"] += transaction_count

        # Convert to summary format
        for key, data in month_data.items():
            summary_data.append(
                {
                    "Month": data["month_year"].strftime("%B %Y"),
                    "Year": data["month_year"].year,
                    "Total Debit Amount": f"₦{data['total_debit']:,.2f}",
                    "Total Credit Amount": f"₦{data['total_credit']:,.2f}",
                    "Debit Transaction Count": data["debit_count"],
                    "Credit Transaction Count": data["credit_count"],
                    "Net Amount": f"₦{(data['total_credit'] - data['total_debit']):,.2f}",
                    "Total Transactions": data["debit_count"] + data["credit_count"],
                }
            )

        # Create simple DataFrame
        df = pd.DataFrame(summary_data)
        df = df.sort_values(["Year"])

        # Generate file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"lightweight_monthly_summary_{timestamp}.xlsx"

        # Create directory
        report_dir = os.path.join(settings.MEDIA_ROOT, "reports", "transaction_summary")
        os.makedirs(report_dir, exist_ok=True)
        file_path = os.path.join(report_dir, file_name)

        # Save simple Excel file (single sheet only)
        df.to_excel(file_path, index=False, sheet_name="Monthly Summary")
        logger.info(f"Generated lightweight Excel file: {file_path}")

        # Generate download URL
        relative_path = f"reports/transaction_summary/{file_name}"
        download_url = f"/accounts/download/{relative_path}"

        # File statistics
        file_exists = os.path.exists(file_path)
        file_size = os.path.getsize(file_path) if file_exists else 0

        return {
            "success": True,
            "download_url": download_url,
            "file_path": file_path,
            "file_name": file_name,
            "file_exists": file_exists,
            "file_size": file_size,
            "total_summary_records": len(summary_data),
            "total_detailed_records": 0,  # No detailed records in lightweight version
            "report_generated_at": datetime.now().isoformat(),
            "note": "Lightweight version - summary data only, no detailed transactions or checker column",
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error generating ultra-lightweight monthly summary: {str(e)}",
        }


def check_monthly_summary_dependencies() -> Dict[str, Any]:
    """
    Check if all dependencies for monthly summary generation are available.
    This can be used for debugging production issues.
    """
    try:
        dependencies = {}

        # Check pandas
        try:
            import pandas as pd

            dependencies["pandas"] = {"available": True, "version": pd.__version__}
        except ImportError as e:
            dependencies["pandas"] = {"available": False, "error": str(e)}

        # Check openpyxl
        try:
            import openpyxl

            dependencies["openpyxl"] = {
                "available": True,
                "version": openpyxl.__version__,
            }
        except ImportError as e:
            dependencies["openpyxl"] = {"available": False, "error": str(e)}

        # Check Django models
        try:
            from accounts import models
            from helpers import enums

            dependencies["django_models"] = {"available": True}
        except ImportError as e:
            dependencies["django_models"] = {"available": False, "error": str(e)}

        # Check database connection
        try:
            from django.db import connection

            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            dependencies["database"] = {"available": True}
        except Exception as e:
            dependencies["database"] = {"available": False, "error": str(e)}

        # Check media directory
        try:
            from django.conf import settings
            import os

            media_root = settings.MEDIA_ROOT
            report_dir = os.path.join(media_root, "reports", "transaction_summary")
            os.makedirs(report_dir, exist_ok=True)
            dependencies["media_directory"] = {"available": True, "path": report_dir}
        except Exception as e:
            dependencies["media_directory"] = {"available": False, "error": str(e)}

        # Check transaction count
        try:
            from accounts.models import TransactionDetail
            from helpers.enums import TransactionStatus, ServiceProvider

            count = TransactionDetail.objects.filter(
                transaction_status=TransactionStatus.SUCCESSFUL,
                service_provider=ServiceProvider.WEMA_BANK,
            ).count()
            dependencies["transaction_data"] = {"available": True, "count": count}
        except Exception as e:
            dependencies["transaction_data"] = {"available": False, "error": str(e)}

        return {
            "success": True,
            "dependencies": dependencies,
            "all_available": all(
                dep.get("available", False) for dep in dependencies.values()
            ),
        }

    except Exception as e:
        return {"success": False, "error": f"Error checking dependencies: {str(e)}"}


def wema_reconciliation(uploaded_file, month: int, year: int) -> Dict[str, Any]:
    """
    Process Excel file for reconciliation with local TransactionDetail records.

    Args:
        uploaded_file: The uploaded Excel file
        month: The month to reconcile (1-12)
        year: The year to reconcile

    Returns:
        Dict containing reconciliation results and download links for generated files
    """
    try:
        # Add logging for debugging
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Starting wema reconciliation for {month}/{year}")
        # Read the Excel file with error handling
        try:
            df = pd.read_excel(uploaded_file, engine="openpyxl")
            logger.info(f"Successfully read Excel file with {len(df)} rows")
        except Exception as e:
            logger.error(f"Error reading Excel file: {str(e)}")
            return {
                "success": False,
                "error": f"Error reading Excel file: {str(e)}",
            }

        # Validate required columns
        required_columns = ["DrAmt", "CrAmt", "TranRmks"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {
                "success": False,
                "error": f"Missing required columns: {', '.join(missing_columns)}",
            }

        # Calculate totals from uploaded file where values are greater than zero
        dr_amt_total = df[df["DrAmt"] > 0]["DrAmt"].sum()
        cr_amt_total = df[df["CrAmt"] > 0]["CrAmt"].sum()
        dr_amt_count = (df["DrAmt"] > 0).sum()
        cr_amt_count = (df["CrAmt"] > 0).sum()

        # Create date range for the specified month and year
        from datetime import datetime, timedelta
        from django.utils import timezone

        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(seconds=1)

        # Make dates timezone aware
        start_date = timezone.make_aware(start_date)
        end_date = timezone.make_aware(end_date)

        # Query local TransactionDetail records for WEMA_BANK in the specified month/year with error handling
        try:
            # Limit the number of transactions to prevent memory issues
            MAX_TRANSACTIONS = 10000  # Limit to 10k transactions for reconciliation

            # Base queryset without slicing
            base_queryset = (
                TransactionDetail.objects.filter(
                    service_provider=enums.ServiceProvider.WEMA_BANK,
                    transaction_status=enums.TransactionStatus.SUCCESSFUL,
                    created_at__gte=start_date,
                    created_at__lte=end_date,
                )
                .select_related("company")
                .order_by("-created_at")
            )

            # Get the main transactions queryset with limit
            local_transactions = base_queryset[:MAX_TRANSACTIONS]

            # Calculate local transaction statistics using separate queries
            local_debit_transactions = base_queryset.filter(
                transaction_type=enums.TransactionType.DEBIT
            )[:MAX_TRANSACTIONS]

            local_credit_transactions = base_queryset.filter(
                transaction_type=enums.TransactionType.CREDIT
            )[:MAX_TRANSACTIONS]

            # Convert to list to get count since we can't call count() on sliced querysets
            local_transactions_list = list(local_transactions)
            logger.info(
                f"Found {len(local_transactions_list)} local transactions for {month}/{year}"
            )

        except Exception as e:
            logger.error(f"Error querying local transactions: {str(e)}")
            return {
                "success": False,
                "error": f"Error querying local transactions: {str(e)}",
            }

        local_debit_count = len(list(local_debit_transactions))
        local_credit_count = len(list(local_credit_transactions))
        local_debit_value = sum(float(t.amount) for t in local_debit_transactions)
        local_credit_value = sum(float(t.amount) for t in local_credit_transactions)

        return _process_reconciliation_files(
            df,
            local_transactions_list,
            month,
            year,
            dr_amt_total,
            cr_amt_total,
            dr_amt_count,
            cr_amt_count,
            local_debit_count,
            local_credit_count,
            local_debit_value,
            local_credit_value,
        )

    except Exception as e:
        logger.error(f"Critical error in wema_reconciliation: {str(e)}")
        # Try to generate a simple fallback reconciliation
        try:
            return wema_reconciliation_simple_fallback(uploaded_file, month, year)
        except Exception as fallback_error:
            logger.error(f"Fallback also failed: {str(fallback_error)}")
            return {
                "success": False,
                "error": f"Error processing reconciliation: {str(e)}. Fallback also failed: {str(fallback_error)}",
            }


def process_transaction_checker(uploaded_file) -> Dict[str, Any]:
    """
    Process Excel file to add a checker column that validates transaction balances.
    Returns only specified columns with company_name substituted from Company model.

    Args:
        uploaded_file: The uploaded Excel file

    Returns:
        Dict containing processing results and download link for processed file

    Output columns:
        - company_name (substituted from user_profiles.Company)
        - session_id, balance_before, amount, balance_after
        - transaction_type, transaction_status, id, created_at, updated_at
        - checker (True/False validation result)
        - checker_amount_difference (difference between expected and actual balance)
    """
    try:
        import pandas as pd
        import numpy as np
        import os
        from datetime import datetime

        # Read the Excel file
        df = pd.read_excel(uploaded_file, engine="openpyxl")

        # Check for required columns (including the ones we need for output)
        required_columns = [
            "company",
            "session_id",
            "balance_before",
            "amount",
            "balance_after",
            "transaction_type",
            "transaction_status",
            "id",
            "created_at",
            "updated_at",
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            return {
                "success": False,
                "error": f"Missing required columns: {', '.join(missing_columns)}. Required columns are: {', '.join(required_columns)}",
            }

        # Create the checker column and checker_amount_difference based on the validation logic
        def validate_transaction_with_difference(row):
            transaction_type = row["transaction_type"]
            amount = row["amount"]
            balance_before = row["balance_before"]
            balance_after = row["balance_after"]

            # Handle missing values
            if (
                pd.isna(amount)
                or pd.isna(balance_before)
                or pd.isna(balance_after)
                or pd.isna(transaction_type)
            ):
                return False, 0.0

            if transaction_type == "CREDIT":
                # For CREDIT: balance_after - amount = balance_before
                expected_balance = balance_before + amount
                difference = round(balance_after - expected_balance, 2)
                is_valid = (
                    abs(difference) < 0.01
                )  # Using small tolerance for floating point comparison
                return is_valid, difference
            elif transaction_type == "DEBIT":
                # For DEBIT: balance_after + amount = balance_before
                expected_balance = balance_before - amount
                difference = round(balance_after - expected_balance, 2)
                is_valid = (
                    abs(difference) < 0.01
                )  # Using small tolerance for floating point comparison
                return is_valid, difference
            else:
                return False, 0.0

        # Apply the validation function to create the checker column and difference
        validation_results = df.apply(
            validate_transaction_with_difference, axis=1, result_type="expand"
        )
        df["checker"] = validation_results[0]
        df["checker_amount_difference"] = validation_results[1]

        # Substitute company names from Company model
        from user_profiles.models import Company

        # Create a mapping of company IDs to company names
        company_ids = df["company"].dropna().unique()
        company_mapping = {}

        for company_id in company_ids:
            try:
                company = Company.objects.get(id=company_id)
                company_mapping[company_id] = company.name
            except Company.DoesNotExist:
                company_mapping[company_id] = f"Unknown Company ({company_id})"

        # Replace company IDs with company names
        df["company_name"] = (
            df["company"].map(company_mapping).fillna("Unknown Company")
        )

        # Select only the required columns in the specified order
        output_columns = [
            "id",
            "created_at",
            "updated_at",
            "company_name",
            "balance_before",
            "amount",
            "balance_after",
            "session_id",
            "transaction_type",
            "transaction_status",
            "checker",
            "checker_amount_difference",
        ]

        # Filter dataframe to only include required columns
        df_output = df[output_columns].copy()

        # Generate statistics
        total_transactions = len(df)
        valid_transactions = len(df[df["checker"] == True])
        invalid_transactions = len(df[df["checker"] == False])
        validation_accuracy = (
            (valid_transactions / total_transactions * 100)
            if total_transactions > 0
            else 0
        )

        # Transaction type breakdown
        transaction_type_counts = df["transaction_type"].value_counts().to_dict()

        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(settings.MEDIA_ROOT, "reports", "transaction_checker")
        os.makedirs(output_dir, exist_ok=True)

        # Save processed file with only the required columns
        output_filename = f"transaction_checker_processed_{timestamp}.xlsx"
        output_path = os.path.join(output_dir, output_filename)
        df_output.to_excel(output_path, index=False)

        # Generate download URL
        download_url = (
            f"/accounts/download/reports/transaction_checker/{output_filename}"
        )

        return {
            "success": True,
            "total_transactions": total_transactions,
            "valid_transactions": valid_transactions,
            "invalid_transactions": invalid_transactions,
            "validation_accuracy": round(validation_accuracy, 2),
            "transaction_type_counts": transaction_type_counts,
            "download_url": download_url,
            "filename": output_filename,
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error processing transaction checker: {str(e)}",
        }


def wema_reconciliation_simple_fallback(
    uploaded_file, month: int, year: int
) -> Dict[str, Any]:
    """
    Simple fallback version of wema_reconciliation without checker column.
    Used when the main function fails due to memory or processing issues.
    """
    try:
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Starting simple fallback reconciliation for {month}/{year}")

        # Read the Excel file
        df = pd.read_excel(uploaded_file, engine="openpyxl")

        # Basic validation
        if "TranRmks" not in df.columns:
            return {
                "success": False,
                "error": "'TranRmks' column not found in the Excel file.",
            }

        # Simple query without complex processing
        from datetime import datetime, timedelta
        from django.utils import timezone

        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(seconds=1)

        start_date = timezone.make_aware(start_date)
        end_date = timezone.make_aware(end_date)

        # Simple count query only
        local_transaction_count = TransactionDetail.objects.filter(
            service_provider=enums.ServiceProvider.WEMA_BANK,
            transaction_status=enums.TransactionStatus.SUCCESSFUL,
            created_at__gte=start_date,
            created_at__lte=end_date,
        ).count()

        # Generate simple response
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        return {
            "success": True,
            "reconciliation_summary": {
                "uploaded_file_stats": {
                    "total_records": len(df),
                    "note": "Simple fallback mode - limited processing",
                },
                "local_records_stats": {
                    "total_local_records": local_transaction_count,
                    "note": "Simple fallback mode - no detailed processing or checker column",
                },
            },
            "download_links": {"note": "Simple fallback mode - no files generated"},
            "file_paths": {},
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error in simple fallback reconciliation: {str(e)}",
        }


def _process_reconciliation_files(
    df,
    local_transactions,
    month: int,
    year: int,
    dr_amt_total,
    cr_amt_total,
    dr_amt_count,
    cr_amt_count,
    local_debit_count,
    local_credit_count,
    local_debit_value,
    local_credit_value,
) -> Dict[str, Any]:
    """
    Helper function to process reconciliation files and generate Excel outputs.
    """
    try:
        # Create a copy of the original dataframe for processing
        processed_df = df.copy()

        # Add new columns to the uploaded file
        processed_df["local owner"] = "NOT FOUND"
        processed_df["local date"] = "NOT FOUND"

        # Get all TranRmks values for global search (not just the specified month)
        tran_rmks_values = processed_df["TranRmks"].dropna().unique()

        # Query all TransactionDetail records globally using TranRmks
        global_transactions = TransactionDetail.objects.filter(
            session_id__in=tran_rmks_values
        ).select_related("company")

        # Create mapping for quick lookup
        transaction_map = {}
        for transaction in global_transactions:
            # Check multiple fields that might match TranRmks
            keys_to_check = [
                transaction.session_id,
                transaction.reference,
                transaction.narration,
            ]
            for key in keys_to_check:
                if key and key in tran_rmks_values:
                    transaction_map[key] = {
                        "local_owner": transaction.company.name,
                        "local_date": transaction.created_at.strftime(
                            "%Y-%m-%d %H:%M:%S"
                        ),
                    }

        # Update the processed dataframe with local information
        found_in_local = 0
        for index, row in processed_df.iterrows():
            tran_rmk = row["TranRmks"]
            if pd.notna(tran_rmk) and tran_rmk in transaction_map:
                processed_df.at[index, "local owner"] = transaction_map[tran_rmk][
                    "local_owner"
                ]
                processed_df.at[index, "local date"] = transaction_map[tran_rmk][
                    "local_date"
                ]
                found_in_local += 1

        return _generate_reconciliation_files(
            processed_df,
            local_transactions,
            month,
            year,
            dr_amt_total,
            cr_amt_total,
            dr_amt_count,
            cr_amt_count,
            local_debit_count,
            local_credit_count,
            local_debit_value,
            local_credit_value,
            found_in_local,
            tran_rmks_values,
        )

    except Exception as e:
        return {
            "success": False,
            "error": f"Error processing reconciliation files: {str(e)}",
        }


def _generate_reconciliation_files(
    processed_df,
    local_transactions,
    month: int,
    year: int,
    dr_amt_total,
    cr_amt_total,
    dr_amt_count,
    cr_amt_count,
    local_debit_count,
    local_credit_count,
    local_debit_value,
    local_credit_value,
    found_in_local,
    tran_rmks_values,
) -> Dict[str, Any]:
    """
    Generate the final reconciliation Excel files.
    """
    try:
        import logging

        logger = logging.getLogger(__name__)

        # Create local records dataframe with wema status and checker column
        try:
            local_records_data = []
            batch_size = 1000  # Process in batches to avoid memory issues
            transaction_list = list(local_transactions)

            logger.info(
                f"Processing {len(transaction_list)} transactions in batches of {batch_size}"
            )

            for i in range(0, len(transaction_list), batch_size):
                batch = transaction_list[i : i + batch_size]
                logger.info(
                    f"Processing batch {i//batch_size + 1}, transactions {i+1} to {min(i+batch_size, len(transaction_list))}"
                )

                for transaction in batch:
                    # Check if this local record exists in the uploaded file
                    wema_status = False
                    keys_to_check = [
                        transaction.session_id,
                        transaction.reference,
                        transaction.narration,
                    ]
                    for key in keys_to_check:
                        if key and key in tran_rmks_values:
                            wema_status = True
                            break

                    # Calculate checker value for transaction balance validation
                    checker = False
                    if transaction.transaction_type == enums.TransactionType.CREDIT:
                        # For CREDIT: balance_after - amount should equal balance_before
                        checker = (
                            abs(
                                (transaction.balance_after - transaction.amount)
                                - transaction.balance_before
                            )
                            < 0.01
                        )
                    elif transaction.transaction_type == enums.TransactionType.DEBIT:
                        # For DEBIT: balance_after + amount should equal balance_before
                        checker = (
                            abs(
                                (transaction.balance_after + transaction.amount)
                                - transaction.balance_before
                            )
                            < 0.01
                        )

                    local_records_data.append(
                        {
                            "transaction_id": str(transaction.id),
                            "company_name": transaction.company.name,
                            "beneficiary_account_name": transaction.beneficiary_account_name,
                            "beneficiary_account_number": transaction.beneficiary_account_number,
                            "amount": float(transaction.amount),
                            "balance_before": float(transaction.balance_before),
                            "balance_after": float(transaction.balance_after),
                            "transaction_type": transaction.transaction_type,
                            "transaction_status": transaction.transaction_status,
                            "reference": transaction.reference,
                            "session_id": transaction.session_id,
                            "narration": transaction.narration,
                            "created_at": transaction.created_at.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            ),
                            "wema_status": wema_status,
                            "checker": checker,
                        }
                    )

                # Force garbage collection between batches to free memory
                if ((i + batch_size) % (batch_size * 2)) == 0:
                    import gc

                    gc.collect()
                    logger.info(
                        f"Garbage collection performed after batch {i//batch_size + 1}"
                    )

            logger.info(f"Completed processing {len(local_records_data)} local records")

        except Exception as e:
            logger.error(f"Error processing local transactions: {str(e)}")
            return {
                "success": False,
                "error": f"Error processing local transactions: {str(e)}",
            }

        local_records_df = pd.DataFrame(local_records_data)

        # Generate file names with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        reconciliation_month = f"{year}_{month:02d}"

        # Create directories
        reconciliation_dir = os.path.join(
            settings.MEDIA_ROOT, "reports", "reconciliation", reconciliation_month
        )
        os.makedirs(reconciliation_dir, exist_ok=True)

        # Save processed uploaded file
        uploaded_file_name = f"wema_statement_processed_{timestamp}.xlsx"
        uploaded_file_path = os.path.join(reconciliation_dir, uploaded_file_name)
        processed_df.to_excel(uploaded_file_path, index=False)

        # Save local records file
        local_file_name = f"local_records_wema_status_{timestamp}.xlsx"
        local_file_path = os.path.join(reconciliation_dir, local_file_name)
        local_records_df.to_excel(local_file_path, index=False)

        # Generate download URLs
        uploaded_file_url = f"/accounts/download/reports/reconciliation/{reconciliation_month}/{uploaded_file_name}"
        local_file_url = f"/accounts/download/reports/reconciliation/{reconciliation_month}/{local_file_name}"

        return {
            "success": True,
            "reconciliation_summary": {
                "uploaded_file_stats": {
                    "dr_amt_count": int(dr_amt_count),
                    "dr_amt_total": format_currency(float(dr_amt_total)),
                    "cr_amt_count": int(cr_amt_count),
                    "cr_amt_total": format_currency(float(cr_amt_total)),
                    "total_records": len(processed_df),
                    "found_in_local": found_in_local,
                    "not_found_in_local": len(processed_df) - found_in_local,
                },
                "local_records_stats": {
                    "debit_count": local_debit_count,
                    "debit_value": format_currency(local_debit_value),
                    "credit_count": local_credit_count,
                    "credit_value": format_currency(local_credit_value),
                    "total_local_records": len(local_records_data),
                    "found_in_wema": sum(
                        1 for record in local_records_data if record["wema_status"]
                    ),
                    "not_found_in_wema": sum(
                        1 for record in local_records_data if not record["wema_status"]
                    ),
                    "checker_passed": sum(
                        1 for record in local_records_data if record["checker"]
                    ),
                    "checker_failed": sum(
                        1 for record in local_records_data if not record["checker"]
                    ),
                    "checker_pass_rate": (
                        f"{(sum(1 for record in local_records_data if record['checker']) / len(local_records_data) * 100):.1f}%"
                        if local_records_data
                        else "0.0%"
                    ),
                },
            },
            "download_links": {
                "processed_uploaded_file": uploaded_file_url,
                "local_records_with_status": local_file_url,
            },
            "file_paths": {
                "processed_uploaded_file": uploaded_file_path,
                "local_records_with_status": local_file_path,
            },
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error generating reconciliation files: {str(e)}",
        }
