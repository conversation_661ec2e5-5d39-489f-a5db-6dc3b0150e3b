from import_export import resources

from core import models


# Register your resource(s) here.
class OTPResource(resources.ModelResource):
    class Meta:
        model = models.OTP


class WhitelistedIPAddressResource(resources.ModelResource):

    class Meta:
        model = models.WhitelistedIPAddress


class OneTimeAccountRequestResource(resources.ModelResource):
    class Meta:
        model = models.OneTimeAccountRequest


class OfflineAccountRecordResource(resources.ModelResource):
    class Meta:
        model = models.OfflineAccountRecord


class TransactionMonitoringResource(resources.ModelResource):
    class Meta:
        model = models.TransactionMonitoring
