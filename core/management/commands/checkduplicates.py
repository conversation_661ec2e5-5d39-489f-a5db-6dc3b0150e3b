from django.core.management.base import BaseCommand, CommandError
from django.apps import apps
from django.db.models import Count
from django.db import transaction


class Command(BaseCommand):
    help = "Check for duplicate values in a specific model field and add suffixes to duplicates."

    def add_arguments(self, parser):
        parser.add_argument("app_label", type=str, help="App label of the model")
        parser.add_argument("model_name", type=str, help="Name of the model")
        parser.add_argument(
            "field_name", type=str, help="Field name to check for duplicates"
        )

    def handle(self, *args, **options):
        app_label = options["app_label"]
        model_name = options["model_name"]
        field_name = options["field_name"]

        # Get the model dynamically
        try:
            model = apps.get_model(app_label, model_name)
        except LookupError:
            raise CommandError(f"Model '{model_name}' not found in app '{app_label}'")

        # Check if the fields exist
        model_fields = [f.name for f in model._meta.get_fields()]
        if field_name not in model_fields:
            raise CommandError(
                f"Field '{field_name}' not found in model '{model_name}'"
            )
        self.stdout.write(
            self.style.WARNING(
                f"\nProcessing session ID duplicates for records with existing suffixes..."
            )
        )

        # Find all records that have suffixes (contain '+' in the field)
        suffixed_records = model.objects.filter(
            session_id__regex=r"(\+\w{2}$|\+\w{3}$)"
        ).order_by("-created_at")

        print(f"SUFFIXED RECORDS:	{suffixed_records.count()}", "\n\n\n")
        if suffixed_records.exists():
            # Collect session IDs from suffixed records
            suffixed_session_ids = set()
            for record in suffixed_records:
                suffixed_session_ids.add(record.session_id.split("+")[0])

            # Process duplicates in these session IDs
            print(f"IDs:	{suffixed_session_ids}")
            print(f"IDs:        {len(suffixed_session_ids)}")
            session_total_updated = 0
            for session_id in suffixed_session_ids:
                session_duplicate_records = model.objects.filter(
                    session_id__startswith=session_id
                ).order_by("created_at")

                if session_duplicate_records.count() > 1:
                    self.stdout.write(
                        f"  Session ID {session_id} -> {session_duplicate_records.count()} times"
                    )

                    # Leave or reset the first original session ID
                    first_record = session_duplicate_records.first()
                    setattr(first_record, field_name, session_id)
                    first_record.save(update_fields=[field_name])

                    # Skip the first record and update the rest
                    session_records_to_update = list(session_duplicate_records[1:])

                    for i, record in enumerate(session_records_to_update, 1):
                        new_session_value = f"{session_id}+{i}"

                        setattr(record, field_name, new_session_value)
                        record.save(update_fields=[field_name])
                        session_total_updated += 1
                        self.stdout.write(
                            f"    Updated record ID {record.id} session: {session_id} -> {new_session_value}"
                        )

            if session_total_updated > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully updated {session_total_updated} duplicate session IDs from suffixed records."
                    )
                )
        else:
            self.stdout.write("  No records with suffixes found.")
