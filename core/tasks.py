from datetime import datetime

from celery import shared_task
from django.conf import settings
import pytz

from core import models
from helpers import enums


# Create your task(s) here.
@shared_task
def transaction_monitoring_handler():
    """
    Task to monitor transactions and check for balance consistency.
    - Looks through TransactionDetail logs routinely for transactions
    that have not been checked.
    """
    from accounts.models import TransactionDetail

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    pending_transactions = TransactionDetail.objects.filter(
        created_at__date=TODAY.date(),
        checked=False,
    )

    for transaction in pending_transactions:
        transaction: TransactionDetail

        # Check for balance consistency
        if transaction.transaction_type == enums.TransactionType.DEBIT:
            expected_balance = transaction.balance_before - transaction.amount
        else:
            expected_balance = transaction.balance_before + transaction.amount

        difference = round(expected_balance - transaction.balance_after, 2)
        is_valid = abs(difference) < 0.01

        # Update the transaction monitoring record
        transaction_monitoring = models.TransactionMonitoring.objects.create(
            company=transaction.company,
            provider=transaction.service_provider,
            reference=transaction.reference,
            balance_before=transaction.balance_before,
            amount=transaction.amount,
            balance_after=transaction.balance_after,
            transaction_type=transaction.transaction_type,
            transaction_status=transaction.transaction_status,
            checker=is_valid,
            checker_amount_difference=difference,
        )
        # Update the transaction detail record
        transaction.checked = True
        transaction.save()
