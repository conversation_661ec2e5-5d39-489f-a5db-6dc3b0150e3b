from rest_framework import serializers as rest_serializers
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response as RestResponse
from rest_framework.views import APIView

from core import models, serializers
from core.utils import CentralAccountHelpers, CentralSendMoneyHelper, get_serializer_key_error
from fidelity.models import AccountSystem
from helpers.custom_permissions import APIAccessPermission
from helpers.custom_responses import Response
from helpers.enums import ServiceProvider
from wema_bank.models import VirtualAccount


# Create your view(s) here.
class OneTimeAccountRequestAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.OneTimeAccountSummarySerializer

    def post(self, request, *args, **kwargs):
        serializer = serializers.RequestOneTimeAccountSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        request_reference = serializer.validated_data.get("request_reference")
        duplicate_request = models.OneTimeAccountRequest.objects.filter(
            company=request.user,
            request_active=True,
            request_reference=request_reference,
        ).first()
        if duplicate_request:
            return Response(
                errors={"message": "duplicate request, try again."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if serializer.validated_data["provider"] == ServiceProvider.WEMA_BANK:
            account = VirtualAccount.get_instant_account(
                company=request.user,
                request_reference=request_reference,
            )
            if isinstance(account, bool):
                return Response(
                    errors={"message": "duplicate request, try again."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if isinstance(account, str):
                return Response(
                    errors={"message": account},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            account_request = models.OneTimeAccountRequest.objects.create(
                company=request.user,
                bank_name=account.bank_name,
                bank_code=account.bank_code,
                account_name=account.fullname,
                nuban=account.account_number,
                request_reference=request_reference,
                request_active=True,
                provider=ServiceProvider.WEMA_BANK,
            )
            wema_serializer = self.serializer_class(instance=account_request)
            data = {
                "message": "success.",
                "account_details": wema_serializer.data,
            }
        if serializer.validated_data["provider"] == ServiceProvider.FIDELITY:
            account = AccountSystem.get_instant_account(
                company=request.user,
                request_reference=request_reference,
            )
            if isinstance(account, bool):
                return Response(
                    errors={"message": "duplicate request, try again."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            account_request = models.OneTimeAccountRequest.objects.create(
                company=request.user,
                bank_name=account.bank_name,
                bank_code=account.bank_code,
                account_name=account.account_name,
                nuban=account.account_number,
                request_reference=request_reference,
                request_active=True,
                provider=ServiceProvider.FIDELITY,
            )
            fidelity_serializer = self.serializer_class(
                instance=account_request)
            data = {
                "message": "success.",
                "account_details": fidelity_serializer.data,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CentralAccountCreations(APIView):

    serializer_class = serializers.CentralAccountCreationsSerializer
    permission_classes = [IsAuthenticated | APIAccessPermission]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except rest_serializers.ValidationError:
            data = {
                "status": "failed",
                "message": "Invalid Payload Data",
                "errors": get_serializer_key_error(serializer.errors),
            }
            return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)

        serialised_data = serializer.validated_data
        serialised_data["token"] = request.auth
        provider = serialised_data.get("provider")
        if provider == ServiceProvider.WEMA_BANK:
            response = CentralAccountHelpers.create_wema_account(
                "create_virtual_account",
                serialised_data,
                request
            )

            checker = response.get("status")
            if checker is True:
                account_details = response.get("data", {}).get(
                    "data", {}).get("account_details", {})
                data = CentralAccountHelpers.prepare_api_success_response_data(
                    account_details)
            else:
                data = CentralAccountHelpers.prepare_api_error_response_data(
                    response)

        elif provider == ServiceProvider.FIDELITY:
            response = CentralAccountHelpers.create_fidelity_account(
                "create-account",
                serialised_data,
                request
            )

            checker = response.get("status")
            if checker is True:
                account_details = response.get(
                    "data", {}).get("data", {}).get("data", {}).get("provider_response", {})
                data = CentralAccountHelpers.prepare_api_success_response_data(
                    account_details)
            else:
                data = CentralAccountHelpers.prepare_api_error_response_data(
                    response)

        elif provider == ServiceProvider.CASH_CONNECT:
            response = CentralAccountHelpers.create_cash_connect_account(
                "create_cash_connect_virtual_account",
                serialised_data,
                request
            )

            checker = response.get("status")
            if checker is True:
                account_details = response.get("data", {}).get("data", {})
                data = CentralAccountHelpers.prepare_api_success_response_data(
                    account_details)
            else:
                data = CentralAccountHelpers.prepare_api_error_response_data(
                    response)

        if data.get("status") == "success":
            return RestResponse(data, status=status.HTTP_201_CREATED)
        else:
            return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)


class CentralSendMoneyAPI(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = serializers.CentralSendMoneySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except rest_serializers.ValidationError:
            data = {
                "status": "failed",
                "message": "Invalid Payload Data",
                "errors": get_serializer_key_error(serializer.errors),
            }
            return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)

        serialised_data = serializer.validated_data
        serialised_data["token"] = request.auth
        provider = serialised_data.get("provider")
        if provider == ServiceProvider.WEMA_BANK:
            response = CentralSendMoneyHelper.wema_send_money(
                "default_transfer_money",
                serialised_data,
                request
            )
            checker = response.get("status")
            if checker == True:
                transaction_status = response.get(
                    "data", {}).get("data", {}).get("status")
                if transaction_status == True:
                    transaction_data = response.get("data", {}).get(
                        "data", {}).get("transaction", {})
                    data = {
                        "status": "success",
                        "reference": transaction_data.get("request_reference"),
                        "account_number": transaction_data.get("account_number"),
                        "account_name": transaction_data.get("account_name"),
                        "amount": transaction_data.get("amount")
                    }
                    return RestResponse(data, status=status.HTTP_200_OK)
                else:
                    data = CentralSendMoneyHelper.prepare_api_error_response_data(
                        response)
                    return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = CentralSendMoneyHelper.prepare_api_error_response_data(
                    response)
                return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)

        elif provider == ServiceProvider.CASH_CONNECT:
            response = CentralSendMoneyHelper.cash_connect_inter_send_money(
                "inter_cash_connect_transfer",
                serialised_data,
                request
            )

            checker = response.get("status")
            if checker == True:
                transaction_status = response.get("data", {}).get("status", {})
                if transaction_status == True:
                    transaction_data = response.get("data", {}).get(
                        "data", {}).get("transaction", {})
                    print("transaction_data", transaction_data)
                    data = {
                        "status": "success",
                        "reference": transaction_data.get("transaction_reference"),
                        "account_number": transaction_data.get("beneficiary_account_number"),
                        "account_name": transaction_data.get("beneficiary_account_name"),
                        "amount": transaction_data.get("amount")
                    }
                    return RestResponse(data, status=status.HTTP_200_OK)
                else:
                    data = {
                        "status": "failed",
                        "message": response.get("data", {}).get("message")
                    }
                    return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = CentralSendMoneyHelper.prepare_api_error_response_data(
                    response)
                return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)

        elif provider == ServiceProvider.FIDELITY:
            response = CentralSendMoneyHelper.fidelity_send_money(
                "make-transfer",
                serialised_data,
                request
            )

            checker = response.get("status")
            if checker == "Successful":
                transaction_status = response.get(
                    "data", {}).get("provider_response_code", {})
                if transaction_status == "00":
                    transaction_data = response.get(
                        "data", {}).get("provider_response", {})
                    data = {
                        "status": "success",
                        "reference": transaction_data.get("reference"),
                        "account_number": transaction_data.get("beneficiary_account_number"),
                        "account_name": transaction_data.get("beneficiary_account_name"),
                        "amount": serialised_data.get("amount")
                    }

                    return RestResponse(data, status=status.HTTP_200_OK)
            else:
                data = CentralSendMoneyHelper.prepare_api_error_response_data(
                    response)
                return RestResponse(data, status=status.HTTP_400_BAD_REQUEST)
