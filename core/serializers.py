from datetime import datetime

from rest_framework import serializers

from core import models
from helpers import enums
from helpers.enums import ServiceProvider, UserTitle


# Create your serializer(s) here.
class OneTimeAccountRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OneTimeAccountRequest
        fields = "__all__"


class OfflineAccountRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OfflineAccountRecord
        fields = "__all__"


class RequestOneTimeAccountSerializer(serializers.Serializer):
    request_reference = serializers.CharField(max_length=255)
    provider = serializers.ChoiceField(choices=ServiceProvider.choices)


class OneTimeAccountSummarySerializer(serializers.ModelSerializer):
    account_number = serializers.CharField(source="nuban")

    class Meta:
        model = models.OneTimeAccountRequest
        fields = [
            "company",
            "sub_company",
            "account_name",
            "account_number",
            "bank_name",
            "bank_code",
            "request_reference",
            "request_active",
        ]
        
class CentralAccountCreationsSerializer(serializers.Serializer):
    
    customer_firstname = serializers.Char<PERSON>ield(max_length=255)
    customer_surname = serializers.Char<PERSON><PERSON>(max_length=255)    
    customer_email = serializers.EmailField()
    customer_mobile_no = serializers.CharField(max_length=255)
    name_on_account = serializers.CharField(max_length=255)
    customer_middle_name = serializers.CharField(max_length=255)
    date_of_birth = serializers.CharField(max_length=255)
    gender = serializers.CharField(max_length=10)
    title = serializers.CharField(max_length=25)
    address_line_1 = serializers.CharField(max_length=255)
    address_line_2 = serializers.CharField(max_length=255, required=False)
    city = serializers.CharField(max_length=255)
    state = serializers.CharField(max_length=255)
    country = serializers.CharField(max_length=255)
    marital_status = serializers.CharField(max_length=255, required=False)
    bvn = serializers.CharField(max_length=255)
    local_govt = serializers.CharField(max_length=255)
    provider = serializers.ChoiceField(choices=ServiceProvider.choices)

    def validate(self, attrs):
        title = attrs.get("title")
        gender = attrs.get("gender")
        date_of_birth = attrs.get("date_of_birth")
        if title.upper() not in UserTitle.values:
            raise serializers.ValidationError("Invalid title")
        if gender.upper() not in ["MALE", "FEMALE"]:
            raise serializers.ValidationError("Invalid gender")
        
        try:
            date_obj = datetime.strptime(date_of_birth, "%d-%m-%Y")
            attrs["date_of_birth"] = date_obj.strftime("%Y-%m-%d")
        except ValueError:
            try:
                datetime.strptime(date_of_birth, "%Y-%m-%d")
                attrs["date_of_birth"] = date_of_birth
            except ValueError:
                raise serializers.ValidationError("Date must be in DD-MM-YYYY or YYYY-MM-DD format")
        
        attrs["gender"] = gender
        attrs["title"] = title
        return attrs

      
class CentralSendMoneySerializer(serializers.Serializer):
    
    first_name = serializers.CharField(max_length=255)
    last_name = serializers.CharField(max_length=255)
    customer_ref = serializers.CharField(max_length=255)
    email = serializers.EmailField()
    phone_number = serializers.CharField(max_length=255)
    transaction_reference = serializers.CharField()
    amount = serializers.FloatField()
    beneficiary_account_number = serializers.CharField()
    beneficiary_account_name = serializers.CharField()
    beneficiary_bank_code = serializers.CharField()
    narration = serializers.CharField()
    mode = serializers.ChoiceField(
        enums.APIMode.choices
    )
    source_account = serializers.CharField()
    mode = serializers.ChoiceField(
        enums.APIMode.choices
    )
    provider = serializers.ChoiceField(choices=ServiceProvider.choices)
    
    """
    Fidelity Bank Transfer Request Example
    {
        "amount": 2000,
        "customer_ref": "CUST12345",
        "customer_firstname": "Alice",
        "customer_surname": "Johnson",
        "customer_email": "<EMAIL>",
        "customer_mobile_no": "+*********0",
        "transaction_ref": "TXN987654321",
        "destination_account": "************",
        "destination_bank_code": "BANK456",
        "transaction_desc": "Payment for services rendered"
    }

    """
    
    """
    Wema Bank Transfer Request Example
        {
        "company": "ABC Corp",
        "source_account": "*********012",
        "mode": "online",
        "account_name": "John Doe",
        "account_number": "************",
        "bank_code": "BANK123",
        "request_reference": "REQ987654321",
        "amount": 1500.75,
        "narration": "Payment for services rendered"
    }
    
    """
    
    """
    CashConnect Transfer Request Example
    
    {
        "transaction_reference": "TXN*********",
        "amount": 2500.50,
        "beneficiary_account_number": "************",
        "beneficiary_account_name": "Jane Doe",
        "beneficiary_bank_code": "BANK456",
        "narration": "Payment for consulting services",
        "mode": "online"
        }

    """
    
    """
    {
        "first_name": "John",
        "last_name": "Doe",
        customer_ref: "*********",
        email: "<EMAIL>",
        "phone_number": "*********0",
        "transaction_reference": "TXN*********",
        "amount": 1500.75,
        "beneficiary_account_number": "*********012",
        "beneficiary_account_name": "John Doe",
        "beneficiary_bank_code": "BANK123",
        "narration": "Payment for invoice #987654",
        "mode": "online", 
        "source_account": "************",
    }

    """