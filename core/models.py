from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
import uuid

from django.conf import settings
from django.contrib.auth.hashers import check_password
from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext as _
import pytz

from core.managers import OTPManager
from helpers import enums


Company = settings.AUTH_USER_MODEL


# Create your model(s) here.
class BaseModel(models.Model):
    """Base model for reuse.
    Args:
        models (Model): Django's model class.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    class Meta:
        abstract = True


class OTP(BaseModel):
    """
    Model representing a One-Time Password (OTP).
    Relationships:
        objects (OTPManager): Custom manager for OTP objects.
    """

    type = models.CharField(
        max_length=255,
        help_text="The type of OTP being generated, e.g.,'REGISTRATION', e.t.c.",
    )
    recipient = models.CharField(
        max_length=255,
        help_text="The recipient's identifier for whom the OTP is being generated.",
    )
    length = models.IntegerField(
        default=8, help_text="The length of the OTP to be generated (max=10)."
    )
    expiry_time = models.IntegerField(
        default=10, help_text="The validity time of the OTP in minutes (default=10)."
    )
    code = models.CharField(max_length=255, editable=False)
    is_used = models.BooleanField(default=False)

    objects = OTPManager()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ONE TIME PASSWORD"
        verbose_name_plural = "ONE TIME PASSWORDS"

    def __str__(self) -> str:
        return f"{self.type} OTP sent to {self.recipient}"

    @property
    def time_valid(self) -> bool:
        """
        Property that checks if the object's created time is still within the valid time range.
        Returns:
            bool: True if the object's created time is within the valid time range, False otherwise.
        """
        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        return (
            True
            if (current_time - self.created_at) <= timedelta(minutes=self.expiry_time)
            else False
        )

    @classmethod
    def get_otp(
        cls,
        type: str,
        recipient: str,
        length: Optional[int] = 8,
        expiry_time: Optional[int] = 10,
    ) -> str:
        """
        Generate and retrieve a new OTP (One-Time Password) object.
        Returns:
            OTP: The newly created OTP object.
        """
        otp = cls.objects.create_otp(
            type=type, recipient=recipient, length=length, expiry_time=expiry_time
        )
        return otp

    @classmethod
    def verify_otp(cls, recipient: str, otp: str) -> dict:
        """
        Verify the OTP (One-Time Password) for the given recipient.
        Returns:
            dict:
            - a dictionary containing the verification status and message.
        """
        one_time_password = cls.objects.filter(
            recipient=recipient, is_used=False
        ).first()
        if one_time_password is None:
            return {"status": False, "message": "invalid or expired OTP."}
        else:
            if not one_time_password.time_valid:
                return {"status": False, "message": "invalid or expired OTP."}
            else:
                verified = check_password(otp, one_time_password.code)
                if not verified:
                    return {"status": False, "message": "invalid or expired OTP."}
                else:
                    one_time_password.is_used = True
                    one_time_password.save()
                    return {"status": True, "message": "OTP is valid for recipient."}


class WhitelistedIPAddress(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    ip = models.CharField(max_length=15)
    is_active = models.BooleanField(default=True)

    def __str__(self) -> str:
        return self.company.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY WHITELISTED IP ADDRESS"
        verbose_name_plural = "COMPANY WHITELISTED IP ADDRESSES"
        constraints = [
            models.UniqueConstraint(
                fields=[
                    "company",
                    "ip",
                ],
                name="company_whitelisted_ip",
            )
        ]

    @classmethod
    def validate_ip(cls, ip_address: str, company: Company):
        """
        This method checks if an IP address is present in the database and is marked as active.
        Args:
            ip_address (str): The IP address to validate.
            company (Company): The Company associated with the IP address.
        Returns:
            bool: True if the IP address exists and is active in the database, False otherwise.
        """
        try:
            cls.objects.get(company=company, ip=ip_address, is_active=True)
            return True
        except cls.DoesNotExist:
            return False


class OneTimeAccountRequest(BaseModel):
    company = models.ForeignKey(
        "user_profiles.Company",
        on_delete=models.PROTECT,
    )
    sub_company = models.ForeignKey(
        "user_profiles.SubCompany",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    bank_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=25)
    account_name = models.CharField(max_length=255)
    nuban = models.CharField(max_length=10)
    request_reference = models.CharField(
        max_length=255,
        unique=True,
        help_text="one-time account request tracker.",
    )
    request_active = models.BooleanField(
        default=False,
        help_text="indicator `True` if the one-time account is currently in use.",
    )
    provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
    )

    def __str__(self) -> str:
        return self.company.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ONE TIME ACCOUNT REQUEST"
        verbose_name_plural = "ONE TIME ACCOUNT REQUESTS"


class OfflineAccountRecord(BaseModel):
    company = models.ForeignKey(
        "user_profiles.Company",
        on_delete=models.PROTECT,
    )
    sub_company = models.ForeignKey(
        "user_profiles.SubCompany",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    nuban = models.CharField(max_length=10)
    unique_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="offline ussd unique code.",
    )
    confirmation_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="offline ussd confirmation code.",
    )
    is_used = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.company.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "OFFLINE ACCOUNT RECORD"
        verbose_name_plural = "OFFLINE ACCOUNT RECORDS"


class TransactionMonitoring(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        editable=False,
    )
    provider = models.CharField(
        max_length=255,
        choices=enums.ServiceProvider.choices,
        editable=False,
    )
    reference = models.CharField(
        max_length=1200,
        unique=True,
        editable=False,
    )
    balance_before = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The account/wallet balance before the transaction.",
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The amount applicable after the deduction or addition of charges",
    )
    balance_after = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The account/wallet balance after the transaction.",
    )
    transaction_type = models.CharField(
        max_length=10,
        choices=enums.TransactionType.choices,
        editable=False,
    )
    transaction_status = models.CharField(
        max_length=255,
        choices=enums.TransactionStatus.choices,
        editable=False,
    )
    checker = models.BooleanField(
        default=True,
        editable=False,
        help_text="True if the transaction balance is valid.",
    )
    checker_amount_difference = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
        help_text="The difference between the expected and actual balance.",
    )

    def __str__(self) -> str:
        return self.transaction.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TRANSACTION MONITORING"
        verbose_name_plural = "TRANSACTION MONITORING"
