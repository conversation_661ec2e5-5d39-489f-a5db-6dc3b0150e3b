import json
import uuid
import requests
from rest_framework.reverse import reverse


class CentralAccountHelpers:
    """
    This class provides utility functions for managing central account information.
    By calling the appropriate url with reverse url.
    """

    @classmethod
    def create_wema_account(
        cls,
        url_name: str, 
        serialiser_object,
        request
    ):
        """
        This function calls Wema URL internally to create a Wema account.
        """
        url = reverse(url_name, request=request)
        payload = {
            "first_name": serialiser_object.get("customer_firstname"),
            "middle_name": serialiser_object.get("customer_middle_name"),
            "last_name": serialiser_object.get("customer_surname"),
            "middle_name": serialiser_object.get("customer_middle_name"),
            "bvn": serialiser_object.get("bvn"),
            "email": serialiser_object.get("customer_email"),
            "phone": serialiser_object.get("customer_mobile_no"),
            "date_of_birth": serialiser_object.get("date_of_birth"),
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {str(serialiser_object.get("token"))}',
        }
        
        request = cls._handle_request("POST", url, payload, headers)
        response = cls._handle_response(request)
        return response
    
    @classmethod
    def create_cash_connect_account(
        cls,
        url_name: str, 
        serialiser_object,
        request
    ):
        """
        This function calls Wema URL internally to create a Wema account.
        """
        url = reverse(url_name, request=request)
        payload = {
            "title": serialiser_object.get("title"), 
            "first_name": serialiser_object.get("customer_firstname"),
            "last_name": serialiser_object.get("customer_surname"),
            "email_address": serialiser_object.get("customer_email"),
            "phone_number": serialiser_object.get("customer_mobile_no"),
            "dob": serialiser_object.get("date_of_birth"),
            "address": serialiser_object.get("address_line_1"),
            "country": serialiser_object.get("country"),
            "state": serialiser_object.get("state"),
            "city": serialiser_object.get("city"),
            "local_govt": serialiser_object.get("local_govt"),
            "marital_status": serialiser_object.get("marital_status").upper(), 
            "gender": serialiser_object.get("gender").upper(),
            "bvn": serialiser_object.get("bvn"), 
        }
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {str(serialiser_object.get("token"))}',
        }
        
        request = cls._handle_request("POST", url, payload, headers)
        response = cls._handle_response(request)
        return response
        
    @classmethod
    def create_fidelity_account(
        cls,
        url_name: str, 
        serialiser_object,
        request
    ):
        """
        This function calls Wema URL internally to create a Wema account.
        """
        url = reverse(url_name, request=request)
       
        payload = {
            "customer_firstname": serialiser_object.get("customer_firstname"),
            "customer_surname": serialiser_object.get("customer_surname"),
            "customer_email":serialiser_object.get("customer_email"),
            "customer_mobile_no": serialiser_object.get("customer_mobile_no"),
            "name_on_account": serialiser_object.get("name_on_account"),
            "customer_middle_name": serialiser_object.get("customer_middle_name"),
            "date_of_birth": serialiser_object.get("date_of_birth"),
            "gender": "M" if serialiser_object.get("gender").lower() == "male" else "F",
            "title": serialiser_object.get("title"),
            "address_line_1": serialiser_object.get("address_line_1"),
            "address_line_2": serialiser_object.get("address_line_2"),
            "city": serialiser_object.get("city"),
            "state": serialiser_object.get("state"),
            "country": serialiser_object.get("country"),
            "transaction_ref": str(uuid.uuid4()),
            "customer_ref": str(uuid.uuid4()),
            "transaction_desc":"create a virtual account",
            "amount": 0,
            "request_ref": str(uuid.uuid4()),
            "customer_bvn": serialiser_object.get("bvn"),
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {str(serialiser_object.get("token"))}',
        }
        
        request = cls._handle_request("POST", url, payload, headers)
        response = cls._handle_response(request)
        return response
        
    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {},
        headers: dict = {},
    ):
        """
        Helper method to handle API requests.
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=headers)
            data = {
                "status": "success",
                "status_code": response.status_code,
                "response": response.json(),
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "status": "failed",
                "status_code": response.status_code,
                "error": str(err)
            }
            return data
    
    @classmethod
    def _handle_response(cls, request):
        """
        Helper method to handle API responses.
        """
        response = request.get("response", {})
        if (
            response.get("status") == "success" 
            or response.get("status") == "Successful" 
            or response.get("status") == True
        ): 
            return {
                "status": True,
                "data": response
            }
                
        else:
            return {
                "status": "failed",
                "message": response.get("message") or response.get("errors"),
            }
    
    @staticmethod
    def prepare_api_success_response_data(account_details: dict):
        """
        Helper method to prepare API response data.
        """
        return {
            "status": "success",
            "message": "Account Created Successfully",
            "data": {
                "account_number": account_details.get("account_number"),
                "bank_name": account_details.get("bank_name"),
                "account_name": account_details.get("account_name"),
            }
        }
        
    @staticmethod
    def prepare_api_error_response_data(response: dict):
        """
        Helper method to prepare API error response data.
        """
        return {
            "status": "failed",
            "message": response.get("message"),
        }
        
def get_serializer_key_error(errors_dict: dict):
    try:
        key = list(errors_dict)[0]
        error = errors_dict.get(key)
        return f"{key} - {error[0]}"
    except Exception:
        return ""
    
    
class CentralSendMoneyHelper:
    """
    This class provides utility functions for sending money.
    By calling the appropriate url with reverse url.
    """

    @classmethod
    def wema_send_money(
        cls,
        url_name: str, 
        serialiser_object,
        request
    ):
        """
        This method calls Wema URL internally to create a Wema account.
        """
        url = reverse(url_name, request=request)
        payload = {
            "source_account": serialiser_object.get("source_account"),
            "mode": serialiser_object.get("mode"),
            "account_name": serialiser_object.get("beneficiary_account_name"),
            "account_number": serialiser_object.get("beneficiary_account_number"),
            "bank_code": serialiser_object.get("beneficiary_account_number"),
            "request_reference": str(uuid.uuid4()),
            "amount": serialiser_object.get("amount"),
            "narration": serialiser_object.get("narration"),
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {str(serialiser_object.get("token"))}',
        }
        
        request = cls._handle_request("POST", url, payload, headers)
        response = cls._handle_response(request)
        return response
    
    @classmethod
    def fidelity_send_money(
        cls,
        url_name: str,
        serialiser_object,
        request
    ):
        url = reverse(url_name, request=request)
        payload = {
            "amount": int(serialiser_object.get("amount")),
            "customer_ref": serialiser_object.get("customer_ref"),
            "customer_firstname": serialiser_object.get("first_name"),
            "customer_surname": serialiser_object.get("last_name"),
            "customer_email": serialiser_object.get("email"),
            "customer_mobile_no": serialiser_object.get("phone_number"),
            "transaction_ref": str(uuid.uuid4()),
            "destination_account": serialiser_object.get("beneficiary_account_number"),
            "destination_bank_code": serialiser_object.get("beneficiary_bank_code"),
            "transaction_desc": serialiser_object.get("narration"),
        }
       
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {str(serialiser_object.get("token"))}',
        }
        
        request = cls._handle_request("POST", url, payload, headers)
        response = cls._handle_response(request)
        return response
        
    @classmethod
    def cash_connect_inter_send_money(
        cls,
        url_name: str,
        serialiser_object,
        request
    ):
        url = reverse(url_name, request=request)
        print(f"URL >> {url}")
        payload =  {
            "transaction_reference": str(uuid.uuid4()),
            "amount": serialiser_object.get("amount"),
            "beneficiary_account_number": serialiser_object.get("beneficiary_account_number"),
            "beneficiary_account_name": serialiser_object.get("beneficiary_account_name"),
            "beneficiary_bank_code": serialiser_object.get("beneficiary_bank_code"),
            "narration": serialiser_object.get("narration"),
            "mode": serialiser_object.get("mode"),
        }
        print(f"PAYLOAD: {payload}")
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {str(serialiser_object.get("token"))}',
        }
        request = cls._handle_request("POST", url, payload, headers)
        response = cls._handle_response(request)
        return response
        
    
    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {},
        headers: dict = {},
    ):
        """
        Helper method to handle API requests.
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=headers)
            data = {
                "status": "success",
                "status_code": response.status_code,
                "response": response.json(),
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "status": "failed",
                "status_code": response.status_code,
                "error": str(err)
            }
            return data
        
    @classmethod
    def _handle_response(cls, request):
        """
        Helper method to handle API responses.
        """
        response = request.get("response", {})
        if (
            response.get("status") == "success" 
            or response.get("status") == "Successful" 
            or response.get("status") == True
        ): 
            return {
                "status": True,
                "data": response
            }
                
        else:
            return {
                "status": "failed",
                "message": response.get("message") or response.get("errors"),
            }
    
    @staticmethod
    def prepare_api_success_response_data(account_details: dict):
        """
        Helper method to prepare API response data.
        """
        return {
            "status": "success",
            "message": "Account Created Successfully",
            "data": {
                "account_number": account_details.get("account_number"),
                "bank_name": account_details.get("bank_name"),
                "account_name": account_details.get("account_name"),
            }
        }
        
    @staticmethod
    def prepare_api_error_response_data(response: dict):
        """
        Helper method to prepare API error response data.
        """
        return {
            "status": "failed",
            "message": response.get("message"),
        }