from django.contrib.auth.hashers import make_password
from django.conf import settings
from django.db import models
from pyotp.totp import TOTP

from helpers.reusable import convert_string_to_base32


# Create your manager(s) here.
class OTPManager(models.Manager):
    """
    Handles the OTP generation.
    OTP is unique per user as the user identifier is used as secret.
    """

    SECRET = settings.OTP_SECRET

    def create_otp(self, type: str, recipient: str, length: int, expiry_time: int):
        """
        Create a One-Time Password (OTP).
        Returns:
            str: The generated OTP code as a string.
        """
        code_generator = TOTP(
            convert_string_to_base32(f"{self.SECRET};{recipient}"), digits=length
        )
        code_value = code_generator.now()
        otp = self.model(
            type=type,
            recipient=recipient,
            length=length,
            expiry_time=expiry_time,
            code=make_password(code_value),
        )
        otp.save()
        return code_value
