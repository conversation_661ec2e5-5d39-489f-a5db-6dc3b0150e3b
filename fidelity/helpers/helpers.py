import sys, os, django
from django.conf import settings

from fidelity.models import FidelityR<PERSON><PERSON>Log
from fidelity.model_choices import RequestType

sys.path.append("/Users/<USER>/Desktop/corebanking")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()
import requests
import hashlib
import json
import uuid


class FidelityClass:
    api_key = settings.FIDELITY_API_KEY
    base_url = settings.FIDELITY_BASE_URL
    secret_key = settings.FIDELITY_SECRET_KEY

    if settings.ENVIRONMENT == "development":
        mock_mode = "Inspect"
    else:
        mock_mode = "Live"

    def md_5_hasher(request_ref: str) -> str:
        text = f"{request_ref};{settings.FIDELITY_SECRET_KEY}"

        md5_hash = hashlib.md5()
        md5_hash.update(text.encode())
        hash_result = md5_hash.hexdigest()

        return hash_result

    @classmethod
    def triple_des_encrypt(cls, plaintext, key=None):
        from Crypto.Cipher import DES3
        from Crypto.Random import get_random_bytes
        from Crypto.Util.Padding import pad

        if key is None:
            key = DES3.adjust_key_parity(
                get_random_bytes(24)
            )  # Key should be 24 bytes for Triple DES
        else:
            # print("Key:", key)
            key = DES3.adjust_key_parity(key.encode("utf-8"))
        iv = get_random_bytes(8)  # IV should be 8 bytes for Triple DES

        cipher = DES3.new(key, DES3.MODE_CBC, iv)
        plaintext = plaintext.encode("utf-8")
        padded_plaintext = pad(plaintext, DES3.block_size)
        ciphertext = cipher.encrypt(padded_plaintext)

        # print("Ciphertext:", ciphertext)

        # Create a new cipher object for decryption
        # cipher_decrypt = DES3.new(key, DES3.MODE_CBC, iv)

        # print("Encrypted plaintext:", ciphertext)
        return ciphertext

    @classmethod
    def triple_des_encryption(cls, plaintext, key=None):
        from Crypto.Cipher import DES3
        import base64

        # Ensure the key is parity-adjusted
        key = DES3.adjust_key_parity(key)

        # Create a TripleDES cipher object
        cipher = DES3.new(key, DES3.MODE_CBC)

        # Generate an Initialization Vector (IV)
        iv = cipher.iv

        # Pad the plaintext to be a multiple of 8 bytes (block size of DES3)
        pad_len = 8 - (len(plaintext) % 8)
        padded_account_number = plaintext + chr(pad_len) * pad_len

        # Encrypt the padded data
        encrypted_data = cipher.encrypt(padded_account_number.encode())

        # Encode the IV and encrypted data as base64 for storage or transmission
        iv_base64 = base64.b64encode(iv).decode("utf-8")
        encrypted_data_base64 = base64.b64encode(encrypted_data).decode("utf-8")

        print(f"IV (Base64): {iv_base64}")
        print(f"Encrypted Data (Base64): {encrypted_data_base64}")
        return encrypted_data_base64

    # @classmethod
    # def triple_des_encrypt_with_key(cls, plaintext):
    #     key = cls.secret_key.encode('utf-8')
    #     iv = b'********'  # Initialization vector should be 8 bytes for Triple DES

    #     cipher = DES3.new(key, DES3.MODE_CBC, iv)
    #     plaintext = plaintext.encode('utf-8')
    #     padded_plaintext = pad(plaintext, DES3.block_size)
    #     ciphertext = cipher.encrypt(padded_plaintext)

    #     return ciphertext

    @classmethod
    def triple_des_encrypt_new(cls, plaintext, key):
        from Crypto.Cipher import DES3
        from Crypto.Util.Padding import pad

        # Ensure the key is adjusted for parity and is 24 bytes long
        key = DES3.adjust_key_parity(key.encode("utf-8"))

        # Create the cipher in ECB mode
        cipher = DES3.new(key, DES3.MODE_ECB)
        plaintext = plaintext.encode("utf-8")
        padded_plaintext = pad(plaintext, DES3.block_size)
        ciphertext = cipher.encrypt(padded_plaintext)

        # Return only the ciphertext
        return ciphertext

    # @classmethod
    # def final_triple_des_encryption(cls, plaintext: str):

    @classmethod
    def final_triple_des_encryption(cls, shared_key, plain_text):
        import hashlib
        from Crypto.Cipher import DES3
        import base64

        # Convert the key to bytes and get MD5 hash
        buffered_key = shared_key.encode('utf-16le')
        key = hashlib.md5(buffered_key).digest()

        # Create a new key by concatenating the MD5 hash with its first 8 bytes
        new_key = key + key[:8]

        # Create IV (Initialization Vector) of 8 zero bytes
        iv = b'\0' * 8

        # Create the cipher object with Triple DES in CBC mode
        cipher = DES3.new(new_key, DES3.MODE_CBC, iv)

        # Encrypt and encode as base64
        # DES3 requires input length to be a multiple of 8, padding is handled automatically
        plain_text_bytes = plain_text.encode('utf-8')
        encrypted = cipher.encrypt(cls.pad_data(data=plain_text_bytes))
        return base64.b64encode(encrypted).decode('utf-8')

    classmethod
    def pad_data(data):
        # PKCS#7 padding
        pad_len = 8 - (len(data) % 8)
        return data + bytes([pad_len]) * pad_len

    @classmethod
    def create_static_account(
        cls,
        amount,
        request_ref,
        customer_ref,
        customer_firstname,
        customer_surname,
        customer_email,
        customer_mobile_no,
        name_on_account,
        customer_middle_name,
        date_of_birth,
        gender,
        title,
        address_line_1,
        address_line_2,
        city,
        state,
        country,
        transaction_ref,
        transaction_desc,
    ):
        url = f"{cls.base_url}/transact"
        sinature_string = cls.md_5_hasher(request_ref)
        # print(":<<<<<<<<<<<<<<<", cls.mock_mode)

        headers = {
            "Authorization": f"Bearer {cls.api_key}",
            "Content-Type": "application/json",
            "Signature": sinature_string,
        }

        data = {
            "request_ref": f"{request_ref}",
            "request_type": "open_account",
            "auth": {
                "type": None,
                "secure": None,
                "auth_provider": "FidelityVirtual",
                "route_mode": None,
            },
            "transaction": {
                "mock_mode": f"{cls.mock_mode}",
                "transaction_ref": f"{transaction_ref}",
                "transaction_desc": f"{transaction_desc}",
                "transaction_ref_parent": None,
                "amount": amount * 100,
                "customer": {
                    "customer_ref": f"{customer_ref}",
                    "firstname": f"{customer_firstname}",
                    "surname": f"{customer_surname}",
                    "email": f"{customer_email}",
                    "mobile_no": f"{customer_mobile_no}",
                },
                "meta": {"a_key": "a_meta_value_1", "b_key": "a_meta_value_2"},
                "details": {
                    "name_on_account": f"{name_on_account}",
                    "middlename": f"{customer_middle_name}",
                    "dob": date_of_birth,
                    "gender": gender,
                    "title": title,
                    "address_line_1": address_line_1,
                    "address_line_2": address_line_2,
                    "city": city,
                    "state": state,
                    "country": country,
                },
            },
        }

        try:
            response: requests.Request = requests.request(
                "POST", url=url, headers=headers, data=json.dumps(data)
            )
            r = response.json()

            status = r.get("status")
            if response.status_code == 200:
                if status == "Successful":
                    data = {
                        "status": True,
                        "status_code": response.status_code,
                        "data": r,
                    }
                else:
                    data = {
                        "status": False,
                        "status_code": response.status_code,
                        "data": r,
                    }

            else:
                data = {"status": False, "status_code": response.status_code, "data": r}
            # print(r)
        except requests.RequestException as err:
            data = {"status": False, "status_code": "400", "data": str(err)}
        return data

    @classmethod
    def transfer_to_bank_account(
        cls,
        amount,
        request_ref,
        customer_ref,
        customer_firstname,
        customer_surname,
        customer_email,
        customer_mobile_no,
        transaction_ref,
        transaction_desc,
        destination_account,
        destination_bank_code,
    ):
        url = f"{cls.base_url}/transact"
        sinature_string = cls.md_5_hasher(request_ref)
        source_account_number = settings.FIDELITY_SOURCE_ACCOUNT_NUMBER

        headers = {
            "Authorization": f"Bearer {cls.api_key}",
            "Content-Type": "application/json",
            "Signature": sinature_string,
        }

        data = {
            "request_ref": str(request_ref),
            "request_type": "disburse",
            "auth": {
                "type": "bank.account",
                "secure": None,
                "auth_provider": "Fidelity",
                "route_mode": None,
            },
            "transaction": {
                "mock_mode": cls.mock_mode,
                "transaction_ref": str(transaction_ref),
                "transaction_desc": transaction_desc,
                "transaction_ref_parent": None,
                "amount": amount * 100,
                "customer": {
                    "customer_ref": customer_ref,
                    "firstname": customer_firstname,
                    "surname": customer_surname,
                    "email": customer_email,
                    "mobile_no": customer_mobile_no,
                },
                "meta": {"a_key": "a_meta_value_1", "b_key": "a_meta_value_2"},
                "details": {
                    "destination_account": destination_account,
                    "destination_bank_code": destination_bank_code,
                    "otp_override": True,
                },
            },
        }

        fidelity_log = FidelityRequestLog.objects.create(
            request_data=data,
            request_type=RequestType.TRANSFER,
        )

        try:
            response: requests.Request = requests.request(
                "POST", url=url, headers=headers, data=json.dumps(data)
            )
            r = response.json()

            status = r.get("status")
            if response.status_code == 200:
                if status == "Successful":
                    data = {
                        "status": True,
                        "status_code": response.status_code,
                        "data": r,
                    }
                else:
                    data = {
                        "status": False,
                        "status_code": response.status_code,
                        "data": r,
                    }

            else:
                data = {"status": False, "status_code": response.status_code, "data": r}
            # print(r)
        except requests.RequestException as err:
            data = {"status": False, "status_code": "400", "data": str(err)}

        fidelity_log.response_data = data.get("data")
        fidelity_log.save()
        return data

    @classmethod
    def check_transaction_status(cls, transaction_ref, request_ref=None):
        request_ref = str(uuid.uuid4)
        url = f"{cls.base_url}/transact/query"
        sinature_string = cls.md_5_hasher(request_ref)

        headers = {
            "Authorization": f"Bearer {cls.api_key}",
            "Content-Type": "application/json",
            "Signature": sinature_string,
        }

        data = {
            "request_ref": f"{request_ref}",
            "request_type": "disburse",
            "auth": {"secure": None, "auth_provider": "Fidelity"},
            "transaction": {"transaction_ref": f"{transaction_ref}"},
        }

        fidelity_log = FidelityRequestLog.objects.create(
            request_data=data,
            request_type=RequestType.TSQ,
        )

        try:
            response: requests.Request = requests.request(
                "POST", url=url, headers=headers, data=json.dumps(data)
            )
            r = response.json()

            status = r.get("status")
            if response.status_code == 200:
                if status == "Successful":
                    data = {
                        "status": True,
                        "status_code": response.status_code,
                        "data": r,
                    }
                else:
                    data = {
                        "status": False,
                        "status_code": response.status_code,
                        "data": r,
                    }

            else:
                data = {"status": False, "status_code": response.status_code, "data": r}
            # print(r)
        except requests.RequestException as err:
            data = {"status": False, "status_code": "400", "data": str(err)}

        fidelity_log.response_data = data.get("data")
        fidelity_log.save()
        return data

    @classmethod
    def check_account_balance(
        cls,
        customer_ref,
        customer_firstname,
        customer_surname,
        customer_email,
        customer_mobile_no,
        transaction_ref,
    ):
        url = f"{cls.base_url}/transact"
        request_ref = str(uuid.uuid4)
        sinature_string = cls.md_5_hasher(request_ref)
        account_number = (
            f"{settings.FIDELITY_SOURCE_ACCOUNT_NUMBER};{settings.FIDELITY_BANK_CODE}"
        )
        encrypted_account_number = cls.final_triple_des_encryption(
            shared_key=cls.secret_key,
            plain_text=account_number,
        )
        print(":<<<<<<<<<<<<<<<", encrypted_account_number)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {cls.api_key}",
            "Signature": sinature_string,
        }

        # data = {
        #     "request_ref": f"{request_ref}",
        #     "request_type": "get_balance",
        #     "auth": {
        #         "type": "bank.account",
        #         "secure": f"{encrypted_account_number}",
        #         "auth_provider": "Fidelity",
        #         "route_mode": None,
        #     },
        #     "transaction": {
        #         "mock_mode": cls.mock_mode,
        #         "transaction_ref": f"{transaction_ref}",
        #         "transaction_desc": "check account balance",
        #         "transaction_ref_parent": None,
        #         "amount": 0,
        #         "customer": {
        #             "customer_ref": f"{customer_ref}",
        #             "firstname": customer_firstname,
        #             "surname": customer_surname,
        #             "email": customer_email,
        #             "mobile_no": customer_mobile_no,
        #         },
        #         "meta": {"a_key": "a_meta_value_1", "b_key": "a_meta_value_2"},
        #         "details": None,
        #     },
        # }

        data = {
            "request_ref": f"{request_ref}",
            "request_type": "get_balance",
            "auth": {
                "type": "bank.account",
                "secure": f"{encrypted_account_number}",
                "auth_provider": "Fidelity",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": cls.mock_mode,
                "transaction_ref": f"{transaction_ref}",
                "transaction_desc": "Check account balance",
                "transaction_ref_parent": None,
                "amount": 0,
                "customer": {
                "customer_ref": f"{customer_ref}",
                "firstname": customer_firstname,
                "surname": customer_surname,
                "email": customer_email,
                "mobile_no": customer_mobile_no
                },
                "meta": {
                "a_key": "a_meta_value_1",
                "b_key": "a_meta_value_2"
                },
                "details": None
            }
        }

        fidelity_log = FidelityRequestLog.objects.create(
            request_data=data,
            request_type=RequestType.ACCOUNT_BALANCE,
        )

        try:
            response: requests.Request = requests.request(
                "POST", url=url, headers=headers, data=json.dumps(data)
            )
            r = response.json()

            status = r.get("status")
            if response.status_code == 200:
                if status == "Successful":
                    data = {
                        "status": True,
                        "status_code": response.status_code,
                        "data": r,
                    }
                else:
                    data = {
                        "status": False,
                        "status_code": response.status_code,
                        "data": r,
                    }

            else:
                data = {"status": False, "status_code": response.status_code, "data": r}
        except requests.RequestException as err:
            data = {"status": False, "status_code": "400", "data": str(err)}
        fidelity_log.response_data = data.get("data")
        fidelity_log.save()
        return data


if __name__ == "__main__":
    # hash_res = FidelityClass.md_5_hasher(request_ref="WFEF23E3")
    run = FidelityClass.transfer_to_bank_account(
        amount=3000,
        request_ref="LIBERTY_XDDF",
        customer_ref="22",
        customer_firstname="Daniel",
        customer_surname="Ugah",
        customer_email="<EMAIL>",
        customer_mobile_no="*************",
        transaction_ref="pkioikioiojrjj333",
        transaction_desc=" A quick transfer to an external account",
        destination_account="**********",
        destination_bank_code="058",
    )
    # print("::::::::run::::::::::::::::")
    # print(run)
    # print("::::::::run::::::::::::::::")
