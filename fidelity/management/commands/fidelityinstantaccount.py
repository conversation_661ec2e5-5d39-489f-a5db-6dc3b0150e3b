import time
import uuid

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,
)

from fidelity.helpers.helpers import FidelityClass
from fidelity.models import AccountSystem
from user_profiles.models import Company


class Command(BaseCommand):
    help = "CREATE MULTIPLE INSTANT/ONE_TIME VIRTUAL ACCOUNTS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("company_id")
        parser.add_argument("total_accounts")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        company_id = kwargs["company_id"]
        total_accounts = int(kwargs["total_accounts"])

        company = Company.objects.filter(id=company_id).first()
        if company is None:
            raise CommandError("INVALID COMPANY ID PROVIDED!")

        for _ in range(total_accounts):
            account_system = AccountSystem.objects.create(
                company=company,
                customer_bvn=settings.MOTHER_BVN,
                one_time=True,
            )
            data = {
                "customer_firstname": "<PERSON>timeyin",
                "customer_surname": "<PERSON><PERSON><PERSON><PERSON>",
                "customer_email": "<EMAIL>",
                "customer_mobile_no": "***********",
                "name_on_account": "PAYBOX360 PAYMENTS",
                "customer_middle_name": "Oti",
                "date_of_birth": "1985-01-05",
                "gender": "M",
                "title": "Mr",
                "address_line_1": "Lagos",
                "address_line_2": "Nigeria",
                "city": "Lagos",
                "state": "Lagos",
                "country": "Nigeria",
                "amount": 0,
                "request_ref": str(account_system.request_ref),
                "transaction_ref": str(uuid.uuid4()),
                "customer_ref": str(account_system.customer_ref),
                "transaction_desc": "create a virtual account",
            }
            create_account = FidelityClass.create_static_account(**data)
            response_data = (
                create_account.get("data", {})
                .get("data", {})
                .get("provider_response", {})
            )
            response_data = response_data or {}
            account_system.request_data = data
            account_system.payload = create_account.get("data")
            account_system.reference = response_data.get("reference")
            account_system.account_number = response_data.get("account_number")
            account_system.account_reference = response_data.get(
                "account_reference"
            )
            account_system.account_name = response_data.get("account_name")
            account_system.customer_email = response_data.get("customer_email")
            account_system.bank_name = response_data.get("bank_name")
            account_system.bank_code = response_data.get("bank_code")
            account_system.account_type = response_data.get("account_type")
            account_system.status = response_data.get("status")
            account_system.created_on = response_data.get("created_on")
            account_system.transation_reference = data.get("transaction_ref")
            account_system.save()
            time.sleep(3)
        self.stdout.write(
            self.style.SUCCESS(
                "COMPANY VIRTUAL ACCOUNTS CREATED SUCCESSFULLY."
            )
        )
