from datetime import datetime
import random
from typing import Optional
import uuid

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models

from accounts.models import AccountDetail, TransactionDetail, TransferMoneyRequest
from core.models import BaseModel
from fidelity.model_choices import Status, TransactionType, RequestType

from helpers.enums import (
    AccountType, TransactionStatus,
    TransactionType, ServiceProvider, APIMode,
)
from user_profiles.models import Company, SubCompany


# Create your model(s) here.
class AccountSystem(BaseModel):
    """
    Table to hold Fidelity Bank Accounts
    NOTE: ``
    """

    request_ref = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    customer_ref = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    company = models.ForeignKey(
        Company, related_name="fidelity_account", on_delete=models.PROTECT
    )
    sub_company = models.ForeignKey(
        SubCompany,
        related_name="sub_fidelity_account",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    cash_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0.00)],
    )
    previous_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0.00)],
    )
    # account_type = models.CharField(choices=AccountType.choices, blank=True, null=True)
    request_data = models.TextField()
    payload = models.TextField()
    reference = models.CharField(max_length=2000, blank=True, null=True)
    account_number = models.CharField(max_length=2000, blank=True, null=True)
    account_reference = models.CharField(max_length=2000, blank=True, null=True)
    account_name = models.CharField(max_length=2000, blank=True, null=True)
    customer_email = models.CharField(max_length=2000, blank=True, null=True)
    bank_name = models.CharField(max_length=2000, blank=True, null=True)
    bank_code = models.CharField(max_length=2000, blank=True, null=True)
    account_type = models.CharField(max_length=2000, blank=True, null=True)
    status = models.CharField(max_length=2000, blank=True, null=True)
    transation_reference = models.CharField(max_length=2000, blank=True, null=True)
    customer_bvn = models.CharField(max_length=200, blank=True, null=True)
    created_on = models.DateTimeField(blank=True, null=True)
    one_time = models.BooleanField(
        default=False,
        help_text="represents accounts used for one-time transactions.",
    )
    request_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="one-time account request tracker.",
    )
    request_active = models.BooleanField(
        default=False,
        help_text="indicator `True` if the one-time account is currently in use.",
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "FIDELITY VIRTUAL ACCOUNT"
        verbose_name_plural = "FIDELITY VIRTUAL ACCOUNTS"

    @classmethod
    def fund_customer_account(cls, account, amount):
        account: AccountSystem
        # Update Customer Account Balance
        account.previous_balance = account.cash_balance
        account.cash_balance = float(account.cash_balance) + float(amount)
        account.save()

        # Update Company Main account
        # try:
        #     company_main_wallet = AccountSystem.objects.get(
        #         company=account,
        #         account_type=AccountType.MAIN,
        #     )

        #     company_main_wallet.previous_balance = company_main_wallet.cash_balance
        #     company_main_wallet.cash_balance += amount
        #     company_main_wallet.save()
        # except AccountSystem.DoesNotExist:
        #     pass

        return True

    @property
    def fetch_account_balance(self, account):
        return self.cash_balance

    # def save(self, *args, **kwargs):
    #     existing_main_account = AccountSystem.objects.filter(
    #         company=self.company,
    #         account_type=AccountType.MAIN,
    #     )

    #     if existing_main_account:
    #         raise ValueError("Main Account already exists for this company")
    #     return super().save(*args, **kwargs)

    # @staticmethod

    def update_account_instance(self, data):
        self.request_data = data["request_data"]
        self.payload = data["payload"]
        self.reference = data["reference"]
        self.account_number = data["account_number"]
        self.account_reference = data["account_reference"]
        self.account_name = data["account_name"]
        self.customer_email = data["customer_email"]
        self.bank_name = data["bank_name"]
        self.bank_code = data["bank_code"]
        self.account_type = data["account_type"]
        self.status = data["status"]
        self.created_on = (
            datetime.strptime(data["created_on"], "%Y-%m-%dT%H:%M:%S.%fZ")
            if data["created_on"]
            else None
        )
        self.save()

    @classmethod
    def get_instant_account(
        cls,
        company: Company,
        request_reference: str,
        sub_company: Optional[SubCompany] = None,
    ) -> object:
        """
        Assigns a one time collection account for a given company.
        Returns:
            VirtualAccount: The assigned VirtualAccount instance.
        """
        instant_virtual_account = cls.objects.filter(
            request_reference=request_reference
        ).first()
        if instant_virtual_account is not None:
            return False
        company_instant_accounts = cls.objects.filter(
            company=company,
            one_time=True,
            request_active=False,
            account_number__isnull=False,
        )
        instant_virtual_account = random.choice(company_instant_accounts)
        instant_virtual_account.sub_company = sub_company
        instant_virtual_account.request_reference = request_reference
        instant_virtual_account.request_active = True
        instant_virtual_account.save()
        return instant_virtual_account

    @classmethod
    def perform_transfer_reversal(
        cls,
        query,
        service_provider,
        customer_account=None,
        shadow_reverse=False,
        bank_code: str=None,
        session_id: str=None,
        source_account: str=None,
    ):
        from accounts.models import AccountDetail
        from accounts.models import TransactionDetail

        from helpers.enums import ServiceProvider, TransactionStatus, APIMode
        # from fidelity.tasks import email_notify_owner_on_inflow

        if settings.ENVIRONMENT == "development":
            api_mode = APIMode.TEST
        else:
            api_mode = APIMode.LIVE

        query: TransferRequest

        reversal_reference = f"REVERSAL_{query.transaction_detail_instance.reference}"

        if TransactionDetail.objects.filter(reference=reversal_reference).exists():
            return "Reversal already exists for this transaction"

        # Fetch Company Account Details
        company_account_detail = AccountDetail.objects.filter(
            company=query.company,
            account_type=AccountType.MAIN,
            provider=service_provider,
        ).last()

        # Create Reversal Transaction
        transaction_detail = TransactionDetail.objects.create(
            company=query.company,
            beneficiary_account_number=company_account_detail.account_number,
            beneficiary_account_name=company_account_detail.account_name,
            amount=query.amount,
            fee=0.0,
            amount_payable=query.amount,
            transaction_type=TransactionType.CREDIT,
            transaction_status=TransactionStatus.REVERSED,
            narration=f"Reversal for transaction: {query.request_ref}",
            service_provider=service_provider,
            mode=api_mode,
            bank_code=bank_code,
            source_account=source_account,
            company_reference=str(query.company.id),
            session_id=session_id,
            reference=reversal_reference,
        )

        # if customer_account:
        #     # FUND CUSTOMER ACCOUNT
        #     fund_virtual_wallet = cls.fund_customer_account(
        #         account=customer_account,
        #         amount=query.amount,
        #     )

        #     if fund_virtual_wallet:
        #         query.virtual_wallet_funded = True
        #         query.save()

        if shadow_reverse:
            # Fund Company Account
            fund_main_wallet = AccountDetail.fund_account(
                company=query.company,
                account_type=AccountType.MAIN,
                amount=0.00,
                charges=0.00,
                sub_company=query.sub_company,
                provider=service_provider,
            )
        else:
            # Fund Company Account
            fund_main_wallet = AccountDetail.fund_account(
                company=query.company,
                account_type=AccountType.MAIN,
                amount=float(query.amount),
                charges=0.00,
                sub_company=query.sub_company,
                provider=service_provider,
            )

        if fund_main_wallet.get("status"):
            # query.main_wallet_funded = True
            # query.save()

            transaction_detail.balance_before = fund_main_wallet.get("previous_balance")
            transaction_detail.balance_after = fund_main_wallet.get("account_balance")
            transaction_detail.save()
        # email_notify_owner_on_inflow(transaction_detail)


class TransferRequest(BaseModel):
    """
    Table to hold Fidelity Transfer Requests
    NOTE: `This is same as Transactions`
    """

    request_ref = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    company = models.ForeignKey(
        Company, related_name="fidelity_transfer", on_delete=models.PROTECT
    )
    sub_company = models.ForeignKey(
        SubCompany,
        related_name="sub_fidelity_transfer",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    transaction_detail_instance = models.ForeignKey(
        "accounts.TransactionDetail",
        related_name="transfer_requests",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    request_data = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    status = models.CharField(
        max_length=255, default=Status.PENDING, choices=Status.choices
    )
    destination_institution_code = models.CharField(
        max_length=255, blank=True, null=True
    )
    beneficiary_account_name = models.CharField(max_length=255, blank=True, null=True)
    beneficiary_account_number = models.CharField(max_length=255, blank=True, null=True)
    beneficiary_kyc_level = models.CharField(max_length=255, blank=True, null=True)
    originator_account_name = models.CharField(max_length=255, blank=True, null=True)
    originator_account_number = models.CharField(max_length=255, blank=True, null=True)
    originator_kyc_level = models.CharField(max_length=255, blank=True, null=True)
    narration = models.CharField(max_length=255, blank=True, null=True)
    transaction_final_amount = models.FloatField(default=0.00, blank=True, null=True)
    amount = models.FloatField(default=0.00, blank=True, null=True)
    reference = models.CharField(max_length=255, blank=True, null=True)
    payment_id = models.CharField(max_length=255, blank=True, null=True)
    transaction_type = models.CharField(
        max_length=200,
        default=TransactionType.DEBIT,
        choices=TransactionType.choices,
        blank=True,
        null=True,
    )
    transaction_ref = models.CharField(max_length=3000, blank=True, null=True)
    verification_response = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "Transaction"
        verbose_name_plural = "Transactions"

    @staticmethod
    def update_transfer_instance(self, data):
        self.request_data = data["request_data"]
        self.payload = data["payload"]
        self.destination_institution_code = data["destination_institution_code"]
        self.beneficiary_account_name = data["beneficiary_account_name"]
        self.beneficiary_account_number = data["beneficiary_account_number"]
        self.beneficiary_kyc_level = data["beneficiary_kyc_level"]
        self.originator_account_name = data["originator_account_name"]
        self.originator_account_number = data["originator_account_number"]
        self.originator_kyc_level = data["originator_kyc_level"]
        self.narration = data["narration"]
        self.transaction_final_amount = data["transaction_final_amount"]
        self.amount = data["amount"]
        self.reference = data["reference"]
        self.payment_id = data["payment_id"]
        self.transaction_ref = data["transaction_ref"]
        self.transaction_detail_instance = data["transaction_detail_instance"]
        self.save()

        # Create Transaction Detal Recored

    @classmethod
    def admin_transfer_to_external_account(cls, transfer_money_obj: TransferMoneyRequest, request):
        from fidelity.helpers.helpers import FidelityClass
        from fidelity.serializers import TransferRequestSerializer
        from fidelity.tasks import update_transfer_instance

        if settings.ENVIRONMENT == "development":
            api_mode = APIMode.TEST
        else:
            api_mode = APIMode.LIVE

        data = {
            "customer_ref": transfer_money_obj.request_reference,
            "customer_firstname": transfer_money_obj.account_name,
            "customer_surname": transfer_money_obj.account_name,
            "customer_email": transfer_money_obj.company.email,
            "customer_mobile_no": transfer_money_obj.company.phone,
            "transaction_ref": transfer_money_obj.request_reference,
            "transaction_desc": transfer_money_obj.narration,
            "amount": float(transfer_money_obj.amount),
            "destination_account": transfer_money_obj.account_number,
            "destination_bank_code": transfer_money_obj.bank_code,
        }

        # print("::::::::::::REQUEST OBJECT CHECK:::::::\n")
        # print(request.user)
        # print("::::::::::::REQUEST OBJECT CHECK:::::::\n")

        serializer = TransferRequestSerializer(
            data=data,
            context={"request": request}
            )

        if serializer.is_valid():
            data = serializer.validated_data
            transfer_request: TransferRequest = data.pop("transfer_instance")
            company_source_account = data.pop("company_source_account")

            # Deplete Company account
            deplete_wallet = AccountDetail.charge_account(
                company=transfer_request.company,
                source_account=company_source_account,
                amount=data.get("amount"),
                charges=0.0,
            )

            if deplete_wallet.get("status") == False:
                return {"status": False, "data": "Insufficient balance"}

            make_transfer = FidelityClass.transfer_to_bank_account(**data)
            # Update account creation record
            response_data = (
                make_transfer.get("data", {})
                .get("data", {})
                .get("provider_response", {})
            )
            response_data = response_data or {}

            # CREATE TRANSACTION DETAIL
            transaction_detail_instance = TransactionDetail.objects.create(
                company=transfer_request.company,
                beneficiary_account_number=response_data.get(
                    "beneficiary_account_number", ""
                ),
                beneficiary_account_name=response_data.get(
                    "beneficiary_account_name", ""
                ),
                amount=transfer_request.amount,
                fee=0.0,
                amount_payable=transfer_request.amount,
                transaction_type=TransactionType.DEBIT,
                transaction_status=TransactionStatus.PENDING,
                narration=response_data.get("narration", ""),
                service_provider=ServiceProvider.FIDELITY,
                mode=api_mode,
                bank_code=response_data.get(
                    "destination_institution_code"
                ),
                source_account=response_data.get(
                    "originator_account_number"
                ),
                company_reference=str(transfer_request.company.id),
                balance_before=deplete_wallet.get("previous_balance"),
                balance_after=deplete_wallet.get("account_balance"),
                reference=serializer.validated_data.get("transaction_ref"),
            )

            update_transfer_instance(
                transfer_request.id,
                {
                    "request_data": serializer.validated_data,
                    "payload": make_transfer.get("data"),
                    "status": response_data.get("status"),
                    "destination_institution_code": response_data.get(
                        "destination_institution_code"
                    ),
                    "beneficiary_account_name": response_data.get(
                        "beneficiary_account_name"
                    ),
                    "beneficiary_account_number": response_data.get(
                        "beneficiary_account_number"
                    ),
                    "beneficiary_kyc_level": response_data.get(
                        "beneficiary_kyc_level"
                    ),
                    "originator_account_name": response_data.get(
                        "originator_account_name"
                    ),
                    "originator_account_number": response_data.get(
                        "originator_account_number"
                    ),
                    "originator_kyc_level": response_data.get(
                        "originator_kyc_level"
                    ),
                    "narration": response_data.get("narration"),
                    "transaction_final_amount": response_data.get(
                        "transaction_final_amount"
                    ),
                    "amount": transfer_request.amount,
                    "reference": response_data.get("reference"),
                    "payment_id": response_data.get("payment_id"),
                    "transaction_ref": serializer.validated_data.get("transaction_ref"),
                    "transaction_detail_instance": transaction_detail_instance,
                },
            )

            transfer_money_obj.request_response = response_data
            transfer_money_obj.save(update_fields=["request_response"])

            if make_transfer.get("status"):
                return make_transfer
            else:
                return make_transfer
        else:
            # print(serializer.errors)
            return {"status": False, "data": serializer.errors}


class AccountInflowPayload(BaseModel):
    payload = models.TextField()
    headers = models.TextField()
    is_processed = models.BooleanField(default=False)
    owner_notified = models.BooleanField(default=False)
    status = models.CharField(
        max_length=255,
        default=Status.PENDING,
        choices=Status.choices,
    )
    source = models.CharField(
        max_length=255,
        blank=True,
        null=True,
    )
    ip_address = models.CharField(
        max_length=200,
        blank=True,
        null=True,
    )
    transaction_id = models.UUIDField(default=uuid.uuid4, blank=True, null=True)
    company_notification_feedback = models.TextField(blank=True, null=True)
    main_wallet_funded = models.BooleanField(default=False, blank=True, null=True)
    virtual_wallet_funded = models.BooleanField(default=False, blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "Account Inflow Payload"
        verbose_name_plural = "Account Inflow Payloads"


class CallbackNotificationLog(BaseModel):
    """ "
    Log for notification to Companies.
    """

    request_data = models.TextField()
    response_data = models.TextField()
    sent = models.BooleanField(default=False, blank=True, null=True)
    status = models.CharField(
        max_length=200,
        default=Status.PENDING,
        choices=Status.choices,
        blank=True,
        null=True,
    )

    class Meta:
        ordering = ["-created_at"]


class FidelityRequestLog(BaseModel):
    request_data = models.JSONField(blank=True, null=True)
    response_data = models.JSONField(blank=True, null=True)
    request_type = models.TextField(choices=RequestType.choices, blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]
