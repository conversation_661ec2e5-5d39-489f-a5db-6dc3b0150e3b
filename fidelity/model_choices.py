from django.db import models
from django.utils.translation import gettext_lazy as _

class Status(models.TextChoices):
    PENDING = "PENDING", _("PENDING")
    SUCCESS = "SUCCESS", _("SUCCESS")
    FAILED = "FAILED", _("FAILED")

class TransactionType(models.TextChoices):
    DEBIT = "DEBIT", _("DEBIT")
    CREDIT = "CREDIT", _("CREDIT")

class RequestType(models.TextChoices):
    TRANSFER = "TRANSFER", _("TRANSFER")
    VIRTUAL_ACCOUNT = "VIRTUAL_ACCOUNT", _("VIRTUAL_ACCOUNT")
    TSQ = "TSQ", _("TSQ")
    ACCOUNT_BALANCE = "ACCOUNT_BALANCE", _("ACCOUNT_BALANCE")