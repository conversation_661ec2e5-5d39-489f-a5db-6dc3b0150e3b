from django.conf import settings

from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.models import AccountDetail, TransactionDetail
from fidelity.helpers.helpers import FidelityClass
from fidelity.models import (
    AccountSystem, TransferRequest, AccountInflowPayload,
)
from fidelity.serializers import (
    AccountCreationSerializer,
    TransferRequestSerializer,
    AccountBalanceSerializer,
)
from fidelity.tasks import update_account_instance, update_transfer_instance
from helpers.custom_permissions import APIAccessPermission

from helpers.enums import TransactionStatus, TransactionType, ServiceProvider, APIMode


# Create your view(s) here.
class AccountCreationView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = AccountCreationSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid(raise_exception=True):
            data = serializer.validated_data
            account_system: AccountSystem = data.pop("account")
            data["customer_ref"] = account_system.customer_ref
            data["transaction_desc"] = "create a virtual account"

            create_account = FidelityClass.create_static_account(**data)

            response_data = (
                create_account.get("data", {})
                .get("data", {})
                .get("provider_response", {})
            )

            response_data = response_data or {}
            account_system.request_data = data
            account_system.payload = create_account.get("data")
            account_system.reference = response_data.get("reference")
            account_system.account_number = response_data.get("account_number")
            account_system.account_reference = response_data.get(
                "account_reference"
            )
            account_system.account_name = response_data.get("account_name")
            account_system.customer_email = response_data.get("customer_email")
            account_system.bank_name = response_data.get("bank_name")
            account_system.bank_code = response_data.get("bank_code")
            account_system.account_type = response_data.get("account_type")
            account_system.status = response_data.get("status")
            account_system.created_on = response_data.get("created_on")
            account_system.transation_reference = data.get("transaction_ref")
            account_system.save()

            if create_account.get("status"):
                # Update account creation record
                return Response(create_account, status=status.HTTP_200_OK)
            else:
                return Response(create_account, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                {"status": False, "data": "Account creation failed"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TransferToAccountView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = TransferRequestSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )

        if settings.ENVIRONMENT == "development":
            api_mode = APIMode.TEST
        else:
            api_mode = APIMode.LIVE

        if serializer.is_valid(raise_exception=True):
            data = serializer.validated_data
            transfer_request: TransferRequest = data.pop("transfer_instance")
            company_source_account = data.pop("company_source_account")

            # Deplete Company account
            deplete_wallet = AccountDetail.charge_account(
                company=transfer_request.company,
                source_account=company_source_account,
                amount=data.get("amount"),
                charges=0.0,
            )

            if deplete_wallet.get("status") == False:
                return Response(
                    {"status": False, "data": "Insufficient balance"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            make_transfer = FidelityClass.transfer_to_bank_account(**data)
            # Update account creation record
            response_data = (
                make_transfer.get("data", {})
                .get("data", {})
                .get("provider_response", {})
            )
            response_data = response_data or {}

            # CREATE TRANSACTION DETAIL
            transaction_detail_instance = TransactionDetail.objects.create(
                company=transfer_request.company,
                beneficiary_account_number=response_data.get(
                    "beneficiary_account_number", ""
                ),
                beneficiary_account_name=response_data.get(
                    "beneficiary_account_name", ""
                ),
                amount=transfer_request.amount,
                fee=0.0,
                amount_payable=transfer_request.amount,
                transaction_type=TransactionType.DEBIT,
                transaction_status=TransactionStatus.PENDING,
                narration=response_data.get("narration", ""),
                service_provider=ServiceProvider.FIDELITY,
                mode=api_mode,
                bank_code=response_data.get(
                    "destination_institution_code"
                ),
                source_account=response_data.get(
                    "originator_account_number"
                ),
                company_reference=str(transfer_request.company.id),
                balance_before=deplete_wallet.get("previous_balance"),
                balance_after=deplete_wallet.get("account_balance"),
                reference=serializer.validated_data.get("transaction_ref"),
            )

            update_transfer_instance(
                transfer_request.id,
                {
                    "request_data": serializer.validated_data,
                    "payload": make_transfer.get("data"),
                    "status": response_data.get("status"),
                    "destination_institution_code": response_data.get(
                        "destination_institution_code"
                    ),
                    "beneficiary_account_name": response_data.get(
                        "beneficiary_account_name"
                    ),
                    "beneficiary_account_number": response_data.get(
                        "beneficiary_account_number"
                    ),
                    "beneficiary_kyc_level": response_data.get(
                        "beneficiary_kyc_level"
                    ),
                    "originator_account_name": response_data.get(
                        "originator_account_name"
                    ),
                    "originator_account_number": response_data.get(
                        "originator_account_number"
                    ),
                    "originator_kyc_level": response_data.get(
                        "originator_kyc_level"
                    ),
                    "narration": response_data.get("narration"),
                    "transaction_final_amount": response_data.get(
                        "transaction_final_amount"
                    ),
                    "amount": transfer_request.amount,
                    "reference": response_data.get("reference"),
                    "payment_id": response_data.get("payment_id"),
                    "transaction_ref": serializer.validated_data.get("transaction_ref"),
                    "transaction_detail_instance": transaction_detail_instance,
                },
            )

            if make_transfer.get("status"):
                return Response(make_transfer, status=status.HTTP_200_OK)
            else:
                return Response(make_transfer, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                {"status": False, "data": "Request failed"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class CheckTransactionStatusView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]

    def post(self, request):
        transaction_ref = request.query_params.get("transaction_ref")
        check_transfer_status = FidelityClass.check_transaction_status(
            transaction_ref=transaction_ref
        )
        if check_transfer_status.get("status"):
            return Response(check_transfer_status, status=status.HTTP_200_OK)
        else:
            return Response(check_transfer_status, status=status.HTTP_400_BAD_REQUEST)


class CheckAccountBalanceView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = AccountBalanceSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )

        if serializer.is_valid(raise_exception=True):
            data = serializer.validated_data
            check_balance = FidelityClass.check_account_balance(**data)
            if check_balance.get("status"):
                response_data = (
                    check_balance.get("data", {})
                    .get("data", {})
                    .get("provider_response", {})
                )
                response_data = response_data or {}
                return Response(check_balance, status=status.HTTP_200_OK)
            else:
                return Response(check_balance, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                {"status": False, "data": "Request failed"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AccountInflowCallback(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        data = request.data
        # print(data, "Data came here::::::::::::")

        AccountInflowPayload.objects.create(
            payload=data,
            ip_address=request.META.get("REMOTE_ADDR"),
            headers=request.headers,
        )

        return Response(
            {
                "status": True,
                "data": "Request received"},
            status=status.HTTP_200_OK
        )
