from django.dispatch import receiver
from django.db.models.signals import post_save
from helpers.enums import ServiceProvider, AccountType

import uuid


@receiver(post_save, sender="accounts.AccountDetail")
def create_fidelity_account_detail(sender, instance, created, **kwargs):
    from accounts.models import AccountDetail
    from fidelity.helpers.helpers import FidelityClass
    from fidelity.models import AccountSystem

    transaction_ref = uuid.uuid4()

    if created:
        if instance.account_type == AccountType.MAIN:
            existing_fidelity_main = AccountDetail.objects.filter(
                company=instance.company,
                provider=ServiceProvider.FIDELITY,
                account_type=AccountType.MAIN,
                )

            if existing_fidelity_main:
                return
            else:
                data = {
                    "customer_firstname": instance.company.name.split()[0],
                    "customer_surname": instance.company.name.split()[-1],
                    "customer_email": instance.company.email,
                    "customer_mobile_no": instance.company.phone,
                    "name_on_account": instance.account_name,
                    "customer_middle_name": instance.account_name.split()[0],
                    "date_of_birth": "22-02-1994",
                    "gender": "M",
                    "title": "Mr.",
                    "address_line_1": instance.company.address,
                    "address_line_2": instance.company.address,
                    "city": "Lagos",
                    "state": "Lagos",
                    "country": "Nigeria",
                    "transaction_ref": str(transaction_ref),
                    # "customer_bvn": instance.company.bvn if instance.company and instance.company.bvn else "************",
                }

                account_system = AccountSystem.objects.create(
                        company=instance.company,
                        sub_company=instance.sub_company,
                        customer_bvn=instance.company.bvn if instance.company
                        and instance.company.bvn else "************",
                    )
                
                data["customer_ref"] = account_system.customer_ref
                data["transaction_desc"] = "create a virtual account"
                data["amount"] = 0
                data["request_ref"] = account_system.request_ref

                create_account = FidelityClass.create_static_account(**data)

                response_data = (
                    create_account.get("data", {}).get("data", {}).get("provider_response", {})
                    )
                response_data = response_data or {}

                account_system.request_data = data
                account_system.payload = create_account.get("data")
                account_system.reference = response_data.get("reference")
                account_system.account_number = response_data.get("account_number")
                account_system.account_reference = response_data.get("account_reference")
                account_system.account_name = response_data.get("account_name")
                account_system.customer_email = response_data.get("customer_email")
                account_system.bank_name = response_data.get("bank_name")
                account_system.bank_code = response_data.get("bank_code")
                account_system.account_type = response_data.get("account_type")
                account_system.status = response_data.get("status")
                account_system.created_on = response_data.get("created_on")
                account_system.transation_reference = data.get("transaction_ref")
                account_system.save()

                if create_account.get("status"):
                    AccountDetail.objects.create(
                        company=instance.company,
                        provider=ServiceProvider.FIDELITY,
                        account_type=AccountType.MAIN,
                        account_name=response_data.get("account_name"),
                        account_number=response_data.get("account_number"),
                        sub_company=instance.sub_company,
                        )
        elif instance.account_type == AccountType.SUB:
            existing_fidelity_sub = AccountDetail.objects.filter(
                company=instance.company,
                sub_company=instance.sub_company,
                provider=ServiceProvider.FIDELITY,
                account_type=AccountType.SUB,
                )

            if existing_fidelity_sub:
                return
            else:
                data = {
                    "customer_firstname": instance.sub_company.company_name.split()[0],
                    "customer_surname": instance.sub_company.company_name.split()[-1],
                    "customer_email": instance.sub_company.company_email,
                    "customer_mobile_no": instance.sub_company.company_phone,
                    "name_on_account": instance.account_name,
                    "customer_middle_name": instance.account_name.split()[0],
                    "date_of_birth": "22-02-1994",
                    "gender": "M",
                    "title": "Mr.",
                    "address_line_1": instance.sub_company.company_address,
                    "address_line_2": instance.sub_company.company_address,
                    "city": "Lagos",
                    "state": "Lagos",
                    "country": "Nigeria",
                    "transaction_ref": str(transaction_ref),
                }

                account_system = AccountSystem.objects.create(
                        company=instance.company,
                        sub_company=instance.sub_company,
                        customer_bvn=instance.company.bvn if instance.sub_company
                        and instance.company.bvn else "************",
                    )

                data["customer_ref"] = account_system.customer_ref
                data["transaction_desc"] = "create a virtual account"
                data["amount"] = 0
                data["request_ref"] = account_system.request_ref
    
                create_account = FidelityClass.create_static_account(**data)

                response_data = (
                    create_account.get("data", {}).get("data", {}).get("provider_response", {})
                    )
                response_data = response_data or {}

                account_system.request_data = data
                account_system.payload = create_account.get("data")
                account_system.reference = response_data.get("reference")
                account_system.account_number = response_data.get("account_number")
                account_system.account_reference = response_data.get("account_reference")
                account_system.account_name = response_data.get("account_name")
                account_system.customer_email = response_data.get("customer_email")
                account_system.bank_name = response_data.get("bank_name")
                account_system.bank_code = response_data.get("bank_code")
                account_system.account_type = response_data.get("account_type")
                account_system.status = response_data.get("status")
                account_system.created_on = response_data.get("created_on")
                account_system.transation_reference = data.get("transaction_ref")
                account_system.save()

                if create_account.get("status"):
                    AccountDetail.objects.create(
                        company=instance.company,
                        provider=ServiceProvider.FIDELITY,
                        account_type=AccountType.SUB,
                        account_name=response_data.get("account_name"),
                        account_number=response_data.get("account_number"),
                        sub_company=instance.sub_company,
                        )
        else:
            return
                    