from django.contrib import admin
from import_export.admin import ImportExportModelAdmin

from one_pipe import resources
from one_pipe.models import ConstantTable, LookupAccount, Mandate, PayWithAccount, Payment, RawNotificationData, RawMandateNotificationData
# Register your models here.


class PayWithAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PayWithAccountResource
    search_fields = [
        "request_ref",
        "transaction_ref",
        "bvn_number",
        "account_number"
    ]
    list_filter = ["created_at", "mock_mode", "request_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class LookupAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.LookupAccountResource
    search_fields = [
        "request_ref",
        "transaction_ref",
        "email",
        "phone_number",
        "bvn_number",
        "account_number"
    ]
    list_filter = ["created_at", "mock_mode"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class MandateResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.MandateResource
    search_fields = [
        "request_ref",
        "transaction_ref",
        "email",
        "phone_number",
    ]
    list_filter = ["created_at", "mock_mode"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class PaymentResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PaymentResource
    search_fields = [
        "request_ref",
        "transaction_ref",
        "email",
        "phone_number",
    ]
    list_filter = ["created_at", "mock_mode"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class ConstantTableResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ConstantTableResource
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RawNotificationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.RawNotificationDataResource
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RawMandateNotificationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.RawMandateNotificationDataResource
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

admin.site.register(PayWithAccount, PayWithAccountResourceAdmin)
admin.site.register(LookupAccount, LookupAccountResourceAdmin)
admin.site.register(Mandate, MandateResourceAdmin)
admin.site.register(Payment, PaymentResourceAdmin)
admin.site.register(ConstantTable, ConstantTableResourceAdmin)
admin.site.register(RawNotificationData, RawNotificationDataResourceAdmin)
admin.site.register(RawMandateNotificationData, RawMandateNotificationDataResourceAdmin)