from import_export import resources

from one_pipe.models import ConstantTable, LookupAccount, Mandate, PayWithAccount, Payment, RawMandateNotificationData, RawNotificationData


class PayWithAccountResource(resources.ModelResource):
    class Meta:
        model = PayWithAccount


class LookupAccountResource(resources.ModelResource):
    class Meta:
        model = LookupAccount


class MandateResource(resources.ModelResource):
    class Meta:
        model = Mandate


class PaymentResource(resources.ModelResource):
    class Meta:
        model = Payment


class ConstantTableResource(resources.ModelResource):
    class Meta:
        model = ConstantTable


class RawNotificationDataResource(resources.ModelResource):
    class Meta:
        model = RawNotificationData


class RawMandateNotificationDataResource(resources.ModelResource):
    class Meta:
        model = RawMandateNotificationData