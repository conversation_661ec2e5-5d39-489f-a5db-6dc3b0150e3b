import hashlib
from django.conf import settings
import hashlib
import base64
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad
import secrets
import string
import time

from one_pipe.models import ConstantTable, PayWithAccount

class Onepipe:
    secret_key = settings.ONEPIPE_SECRET_KEY
    ONEPIPE_API_KEY = settings.ONEPIPE_API_KEY

    @classmethod
    def signature(cls, request_ref):
        return hashlib.md5(f"{request_ref};{cls.secret_key}".encode()).hexdigest()
    
    @classmethod
    def verify_signature(cls, request_ref, signature):
        expected_signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode()).hexdigest()
        return expected_signature == signature

    @classmethod
    def encrypted_secure(cls, request_ref):
        text = f"{request_ref};{cls.secret_key}"
        key = cls.secret_key
        # Generate MD5 hash of the key
        md5 = hashlib.md5()
        md5.update(key.encode("utf-16le"))
        digest_of_password = md5.digest()

        # Create a 24-byte key (first 16 bytes + first 8 bytes again)
        key_bytes = digest_of_password[:16] + digest_of_password[:8]

        # Initialize Triple DES cipher in CBC mode with a zero IV
        iv = bytes(8)  # 8-byte IV initialized to zero
        cipher = DES3.new(key_bytes, DES3.MODE_CBC, iv)

        # Encrypt the text with PKCS5 padding
        plain_text_bytes = text.encode("utf-16le")
        cipher_text = cipher.encrypt(pad(plain_text_bytes, DES3.block_size))

        # Convert to base64 encoding
        return base64.b64encode(cipher_text).decode("utf-8")
    
    @classmethod
    def encrypted_secure_bank_account(cls, account_number, bank_cbn_code):
        
        text = f"{account_number};{bank_cbn_code}"
        key = cls.secret_key
        # Generate MD5 hash of the key
        md5 = hashlib.md5()
        md5.update(key.encode("utf-16le"))
        digest_of_password = md5.digest()

        # Create a 24-byte key (first 16 bytes + first 8 bytes again)
        key_bytes = digest_of_password[:16] + digest_of_password[:8]

        # Initialize Triple DES cipher in CBC mode with a zero IV
        iv = bytes(8)  # 8-byte IV initialized to zero
        cipher = DES3.new(key_bytes, DES3.MODE_CBC, iv)

        # Encrypt the text with PKCS5 padding
        plain_text_bytes = text.encode("utf-16le")
        cipher_text = cipher.encrypt(pad(plain_text_bytes, DES3.block_size))

        # Convert to base64 encoding
        return base64.b64encode(cipher_text).decode("utf-8")

    @classmethod
    def encrypted_secure_bvn(cls, bvn_number):

        text = f"{bvn_number}"
        key = cls.secret_key
        # Generate MD5 hash of the key
        md5 = hashlib.md5()
        md5.update(key.encode("utf-16le"))
        digest_of_password = md5.digest()

        # Create a 24-byte key (first 16 bytes + first 8 bytes again)
        key_bytes = digest_of_password[:16] + digest_of_password[:8]

        # Initialize Triple DES cipher in CBC mode with a zero IV
        iv = bytes(8)  # 8-byte IV initialized to zero
        cipher = DES3.new(key_bytes, DES3.MODE_CBC, iv)

        # Encrypt the text with PKCS5 padding
        plain_text_bytes = text.encode("utf-16le")
        cipher_text = cipher.encrypt(pad(plain_text_bytes, DES3.block_size))

        # Convert to base64 encoding
        return base64.b64encode(cipher_text).decode("utf-8")
    
    @classmethod
    def generate_request_ref(cls):

        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
        else:
            mock_mode = constant_variable.mock_mode

        if mock_mode == "Inspect":
            request_ref_range = 15
        else:
            request_ref_range = 22


        alphabet = string.ascii_lowercase + string.digits
        loop_condition = True
        while loop_condition:
            request_ref = "".join(secrets.choice(alphabet) for i in range(request_ref_range)) + str(int(time.time()))
            if (
                any(c.islower() for c in request_ref)
                and any(c.islower() for c in request_ref)
                and sum(c.isdigit() for c in request_ref) >= 6
            ):
                loop_condition = False

        if PayWithAccount.objects.filter(request_ref=request_ref).exists():
            cls.generate_request_ref()

        return request_ref

    @classmethod
    def generate_transaction_ref(cls):

        constant_variable = ConstantTable.get_constant_variable()
        if not constant_variable:
            mock_mode = "Inspect"
        else:
            mock_mode = constant_variable.mock_mode

        if mock_mode == "Inspect":
            request_ref_range = 20
        else:
            request_ref_range = 25

        alphabet = string.ascii_lowercase + string.digits
        loop_condition = True
        while loop_condition:
            transaction_ref = "".join(secrets.choice(alphabet) for i in range(request_ref_range)) + str(int(time.time()))
            if (
                any(c.islower() for c in transaction_ref)
                and any(c.islower() for c in transaction_ref)
                and sum(c.isdigit() for c in transaction_ref) >= 9
            ):
                loop_condition = False

        if PayWithAccount.objects.filter(transaction_ref=transaction_ref).exists():
            cls.generate_transaction_ref()

        return transaction_ref
    
    @classmethod
    def amount_in_kobo(cls, amount):
        if amount is None:
            return 0
        return (int(amount) * 100)
    
    @classmethod
    def amount_in_naira(cls, amount):
        if amount is None:
            return 0
        return (int(amount) / 100)
    
    @classmethod
    def match_api_key(cls, api_key: str) -> bool:
        return api_key == cls.ONEPIPE_API_KEY
