from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed

from one_pipe.helpers.func import Onepipe


class OnePipeIsAuthenticated(BaseAuthentication):
    def authenticate(self, request):
        # auth_header = request.headers.get('Authorization')
        # signature = request.headers.get('Signature')
        # request_body = request.data

        # request_ref = request_body.get('request_ref', None)

        # if not auth_header or not auth_header.startswith('Bearer '):
        #     raise AuthenticationFailed('Unauthorized: Missing or invalid Bearer token')

        # api_key = auth_header.split('Bearer ')[1].strip()

        # is_match = Onepipe.match_api_key(api_key)
        # if is_match is False:
        #     raise AuthenticationFailed('Unauthorized: Invalid token')
        
        # if request_ref:
        #     if Onepipe.verify_signature(request_ref, signature) is False:
        #         raise AuthenticationFailed('Unauthorized')

        from django.contrib.auth.models import AnonymousUser
        return (AnonymousUser(), None)
