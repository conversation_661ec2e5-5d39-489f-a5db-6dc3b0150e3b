from rest_framework import serializers

from one_pipe.models import REQUEST_TYPE

class GetBanksSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False) 
    phone_number = serializers.Char<PERSON>ield(required=True, allow_null=False) 
    first_name = serializers.CharField(required=True, allow_null=False) 
    last_name = serializers.CharField(required=True, allow_null=False)

class LookUpAccountMaxSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False) 
    phone_number = serializers.CharField(required=True, allow_null=False) 
    bvn_number = serializers.CharField(required=True, allow_null=False) 
    first_name = serializers.Char<PERSON>ield(required=True, allow_null=False) 
    last_name = serializers.Char<PERSON>ield(required=True, allow_null=False)
    account_number = serializers.Char<PERSON>ield(required=True, allow_null=False)
    bank_cbn_code = serializers.Cha<PERSON><PERSON><PERSON>(required=True, allow_null=False)

class CreateMandateSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False) 
    phone_number = serializers.CharField(required=True, allow_null=False) 
    bvn_number = serializers.CharField(required=True, allow_null=False) 
    first_name = serializers.CharField(required=True, allow_null=False) 
    last_name = serializers.CharField(required=True, allow_null=False)
    amount = serializers.FloatField(required=True, allow_null=False)
    account_number = serializers.CharField(required=True, allow_null=False)
    bank_cbn_code = serializers.CharField(required=True, allow_null=False)
    customer_consent = serializers.CharField(required=True, allow_null=False)
    
    def validate(self, attrs):
        if attrs.get("amount") <= 0:
            raise serializers.ValidationError("amount must be greater than zero.")
        return attrs
    
class CollectPaymentSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False) 
    phone_number = serializers.CharField(required=True, allow_null=False)
    first_name = serializers.CharField(required=True, allow_null=False) 
    last_name = serializers.CharField(required=True, allow_null=False)
    amount = serializers.FloatField(required=True, allow_null=False)
    account_number = serializers.CharField(required=True, allow_null=False)
    bank_cbn_code = serializers.CharField(required=True, allow_null=False)
    
    def validate(self, attrs):
        if attrs.get("amount") <= 0:
            raise serializers.ValidationError("amount must be greater than zero.")
        return attrs
    
class QueryReferenceSerializer(serializers.Serializer):
    request_type = serializers.ChoiceField(choices=REQUEST_TYPE)
    transaction_ref = serializers.CharField(required=True, allow_null=False)
    def validate(self, attrs):
        request_type = attrs.get("request_type")
        
        if request_type == "COLLECT_PAYMENT":
            this_request_type = "collect"
        # elif request_type == "LOOK_UP_ACCOUNT":
        #     this_request_type = "lookup account max"
        # elif request_type == "CREATE_MANDATE":
        #     this_request_type = "create mandate"
        # elif request_type == "GET_BANKS":
        #     this_request_type = "get_banks"
        else:
            raise serializers.ValidationError("Invalid request type.")
        
        attrs["request_type"] = this_request_type
        return attrs
