from django.db import models
from django.core.validators import  MinV<PERSON>ueValidator

from core.models import BaseModel
from django.conf import settings

Company = settings.AUTH_USER_MODEL
# Create your models here.

REQUEST_TYPE = [
    ("LOOK_UP_ACCOUNT", "lookup account max"),
    ("CREATE_MANDATE", "create mandate"),
    ("COLLECT_PAYMENT", "collect"),
    ("GET_BANKS", "get_banks"),
    ("QUERY_TRANSACTION", "query_transaction"),
]

MOCK_MODE = [
    ("Live", "Live"),
    ("Inspect", "Inspect"),
]

class PayWithAccount(BaseModel):
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.PROTECT)
    request_type = models.CharField(max_length=200, choices=REQUEST_TYPE, null=True, blank=True)
    mock_mode = models.CharField(max_length=200, choices=MOCK_MODE, null=True, blank=True)
    request_ref = models.CharField(max_length=255, unique=True, null=True, blank=True)
    transaction_ref = models.CharField(max_length=255, unique=True, null=True, blank=True)
    request = models.TextField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)
    bvn_number = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=255, null=True, blank=True)
    bank_cbn_code = models.CharField(max_length=255, null=True, blank=True)
    # bvn_number = models.CharField(max_length=255, null=True, blank=True)
    # phone_number = models.CharField(max_length=255, null=True, blank=True)
    # first_name = models.CharField(max_length=255, null=True, blank=True)
    # last_name = models.CharField(max_length=255, null=True, blank=True)
    # amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    # amount_in_kobo = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PAY_WITH_ACCOUNT DUMP"
        verbose_name_plural = "PAY_WITH_ACCOUNT DUMPS"


class LookupAccount(BaseModel):
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.PROTECT)
    email = models.EmailField(null=True, blank=True)
    mock_mode = models.CharField(max_length=200, choices=MOCK_MODE, null=True, blank=True)
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    request_ref = models.CharField(max_length=255, null=True, blank=True)
    transaction_ref = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=255, null=True, blank=True)
    account_name = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    middle_name = models.CharField(max_length=255, null=True, blank=True)
    gender = models.CharField(max_length=255, null=True, blank=True)
    dob = models.CharField(max_length=255, null=True, blank=True)
    available_balance = models.CharField(max_length=255, null=True, blank=True)
    kyc_level = models.CharField(max_length=255, null=True, blank=True)
    address1 = models.CharField(max_length=255, null=True, blank=True)
    address2 = models.CharField(max_length=255, null=True, blank=True)
    address3 = models.CharField(max_length=255, null=True, blank=True)
    customer_id = models.CharField(max_length=255, null=True, blank=True)
    account_currency = models.CharField(max_length=255, null=True, blank=True)
    name_enquiry_id = models.CharField(max_length=255, null=True, blank=True)
    account_branch = models.CharField(max_length=255, null=True, blank=True)
    account_status = models.CharField(max_length=255, null=True, blank=True)
    ledger_balance = models.CharField(max_length=255, null=True, blank=True)
    account_type = models.CharField(max_length=255, null=True, blank=True)
    meta = models.TextField(null=True, blank=True)
    bvn_number = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "LOOKUP ACCOUNT"
        verbose_name_plural = "LOOKUP ACCOUNTS"

class Mandate(BaseModel):
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.PROTECT)
    email = models.EmailField(null=True, blank=True)
    mock_mode = models.CharField(max_length=200, choices=MOCK_MODE, null=True, blank=True)
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    reference = models.CharField(max_length=255, null=True, blank=True)
    request_ref = models.CharField(max_length=255, null=True, blank=True)
    transaction_ref = models.CharField(max_length=255, null=True, blank=True)
    contract_code = models.CharField(max_length=255, null=True, blank=True)
    account_reference = models.CharField(max_length=255, null=True, blank=True)
    amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    amount_in_kobo = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    bank_name = models.CharField(max_length=255, null=True, blank=True)
    bank_code = models.CharField(max_length=255, null=True, blank=True)
    currency_code = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=255, null=True, blank=True)
    account_name = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    mock_mode = models.CharField(max_length=200, choices=MOCK_MODE, null=True, blank=True)
    status = models.CharField(max_length=255, null=True, blank=True)
    created_on = models.CharField(max_length=255, null=True, blank=True)
    account_type = models.CharField(max_length=255, null=True, blank=True)
    bvn_number = models.CharField(max_length=255, null=True, blank=True)
    meta = models.TextField(null=True, blank=True)    

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "MANDATE"
        verbose_name_plural = "MANDATES"

class Payment(BaseModel):
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.PROTECT)
    email = models.EmailField(null=True, blank=True)
    reference = models.CharField(max_length=255, null=True, blank=True)
    request_ref = models.CharField(max_length=255, null=True, blank=True)
    transaction_ref = models.CharField(max_length=255, null=True, blank=True)
    transaction_final_amount_in_kobo = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    transaction_final_amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    mock_mode = models.CharField(max_length=200, choices=MOCK_MODE, null=True, blank=True)
    amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    amount_in_kobo = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])  
    bank_code = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=255, null=True, blank=True)
    meta = models.TextField(null=True, blank=True) 

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PAYMENT"
        verbose_name_plural = "PAYMENTS"


class ConstantTable(BaseModel):
    on_off = models.BooleanField(default=False)
    mock_mode = models.CharField(max_length=200, choices=MOCK_MODE, null=True, blank=True, default="Inspect")

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CONSTANT TABLE"
        verbose_name_plural = "CONSTANT TABLES"

    @classmethod
    def get_constant_variable(cls):
        constant_variable = cls.objects.last()
        return constant_variable
    

class RawNotificationData(BaseModel):
    data = models.TextField(null=True, blank=True)
    auth_header = models.TextField(null=True, blank=True)
    signature = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PAYMENT NOTIFICATION"
        verbose_name_plural = "PAYMENT NOTIFICATIONS"


class RawMandateNotificationData(BaseModel):
    data = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "MANDATE NOTIFICATION"
        verbose_name_plural = "MANDATE NOTIFICATIONS"
