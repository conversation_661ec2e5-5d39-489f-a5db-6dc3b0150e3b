from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from helpers.custom_responses import Response
from one_pipe import models, serializers
from one_pipe.helpers.apis import OnepipePayWithAccount
from one_pipe.authentication import OnePipeIsAuthenticated
# Create your views here.


class GetBanksAPIView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request):
        serializer = serializers.GetBanksSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lookup_account_max = OnepipePayWithAccount.get_banks(validated_data=serializer.validated_data, company=request.user)
        if lookup_account_max.get("data", {}).get("provider_response_code") == "400":
            return Response(lookup_account_max, status=status.HTTP_400_BAD_REQUEST)
        return Response(lookup_account_max, status=status.HTTP_200_OK)
    
class LookUpAccountMaxAPIView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request):
        serializer = serializers.LookUpAccountMaxSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lookup_account_max = OnepipePayWithAccount.lookup_account_max(validated_data=serializer.validated_data, company=request.user)
        if lookup_account_max.get("data", {}).get("provider_response_code") == "400":
            return Response(lookup_account_max, status=status.HTTP_400_BAD_REQUEST)
        return Response(lookup_account_max, status=status.HTTP_200_OK)
    
class CreateMandateAPIView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request):
        serializer = serializers.CreateMandateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lookup_account_max = OnepipePayWithAccount.create_mandate(validated_data=serializer.validated_data, company=request.user)
        if lookup_account_max.get("data", {}).get("provider_response_code") == "400":
            return Response(lookup_account_max, status=status.HTTP_400_BAD_REQUEST)
        return Response(lookup_account_max, status=status.HTTP_200_OK)
    
class CollectPaymentAPIView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request):
        serializer = serializers.CollectPaymentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lookup_account_max = OnepipePayWithAccount.collect_payment(validated_data=serializer.validated_data, company=request.user)
        if lookup_account_max.get("data", {}).get("provider_response_code") == "400":
            return Response(lookup_account_max, status=status.HTTP_400_BAD_REQUEST)
        return Response(lookup_account_max, status=status.HTTP_200_OK)
        

class OnePipeNotificationAPIView(APIView):
    authentication_classes = [OnePipeIsAuthenticated,]

    def post(self, request):
        auth_header = request.headers.get('Authorization')
        signature = request.headers.get('Signature')
        models.RawNotificationData.objects.create(
            data=request.data,
            auth_header=auth_header,
            signature=signature
            )
     
        return Response(
            {
                "status": "Success", 
                "message": "Received Successfully",
        }, status=status.HTTP_200_OK)
    

class OnePipeMandateNotificationAPIView(APIView):
    authentication_classes = [OnePipeIsAuthenticated,]

    def post(self, request):
        models.RawMandateNotificationData.objects.create(data=request.data)
     
        return Response(
            {
                "status": "Success", 
                "message": "Received Successfully",
        }, status=status.HTTP_200_OK)


class QueryReferenceAPIView(APIView):
    permission_classes = [IsAuthenticated,]
    
    def post(self, request):
        serializer = serializers.QueryReferenceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        query_transaction = OnepipePayWithAccount.query_reference(validated_data=serializer.validated_data, company=request.user)
        return Response(query_transaction, status=status.HTTP_200_OK)
        