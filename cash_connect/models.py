from django.db import models
import json
import random
import string

from cash_connect.helpers.cash_connect_call_backs import CashConnectHelperClass
from core.models import BaseModel, Company
from helpers import enums
from user_profiles.models import SubCompany
from django.core.validators import MinValueValidator
from django.conf import settings

from wema_bank.tasks import send_company_transaction_callbacks

# Create your models here.


def generate_customer_reference():
    code = "".join(random.choices(string.ascii_uppercase + string.digits, k=15))
    return code


class CashConnectWebHookData(BaseModel):
    data = models.TextField()
    processor_status_code = models.CharField(max_length=300, blank=True, null=True)
    processor_code = models.CharField(max_length=300, blank=True, null=True)
    transaction_status = models.CharField(max_length=300, blank=True, null=True)
    currency = models.CharField(max_length=300, blank=True, null=True)
    customer_reference = models.CharField(max_length=300, blank=True, null=True)
    notification_request_reference = models.Char<PERSON>ield(
        max_length=300, blank=True, null=True
    )
    transaction_amount = models.FloatField(default=0)
    transaction_value_amount = models.FloatField(default=0)
    transaction_fee = models.FloatField(default=0)
    transaction_reference = models.CharField(
        max_length=300, blank=True, null=True, unique=True
    )
    session_id = models.CharField(max_length=300, blank=True, null=True)
    expected_collection_amount = models.FloatField(default=0)
    amount_collected = models.FloatField(default=0)
    collection_value_amount = models.FloatField(default=0)
    collection_fee_amount = models.FloatField(default=0)
    collection_count = models.IntegerField(default=0)
    expected_multiple_amount = models.FloatField(default=0)
    accept_multiple_amount = models.BooleanField(default=False)
    accept_lesser_amount = models.BooleanField(default=False)
    transaction_start_time = models.DateTimeField(null=True, blank=True)
    expiration_date = models.DateTimeField(null=True, blank=True)
    narration = models.CharField(max_length=300, blank=True, null=True)
    beneficiary_account_number = models.CharField(max_length=300, blank=True, null=True)
    beneficiary_account_name = models.CharField(max_length=300, blank=True, null=True)
    beneficiary_bank_code = models.CharField(max_length=300, blank=True, null=True)
    beneficiary_bank_name = models.CharField(max_length=300, blank=True, null=True)
    beneficiary_account_id = models.CharField(max_length=300, blank=True, null=True)
    source_account_number = models.CharField(max_length=300, blank=True, null=True)
    source_account_name = models.CharField(max_length=300, blank=True, null=True)
    source_bank_name = models.CharField(max_length=300, blank=True, null=True)
    source_bank_code = models.CharField(max_length=300, blank=True, null=True)
    collection_status = models.CharField(max_length=300, blank=True, null=True)
    status_reason = models.CharField(max_length=300, blank=True, null=True)
    event_send_count = models.IntegerField(default=0)
    event_updated_at = models.DateTimeField(null=True, blank=True)
    event_sent = models.BooleanField(default=False)
    company_event_response = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = "CASH CONNECT WEBHOOK DATA"
        verbose_name_plural = "CASH CONNECT WEBHOOK DATAS"

    @classmethod
    def create_record(cls, data):
        from accounts.models import AccountDetail, TransactionDetail

        """
        Create a record in the database from the provided data.

        This method accepts either a JSON string or a dictionary. If a JSON string is provided,
        it attempts to parse it into a dictionary. If parsing fails, it creates a record with the
        original string data. The method extracts various fields from the 'data' dictionary to
        populate the record.

        Parameters:
            data (str or dict): The data to create the record from. If a string is provided, it
                                should be in JSON format.

        Raises:
            json.JSONDecodeError: If the provided string cannot be parsed into a JSON object.


        SAMPLE DATA:

            {
                "responseCode" : 200,
                "processorStatusCode" : "00",
                "responseMessage" : "Approved or completed successfully",
                "requiredVerification" : false,
                "responseTime" : "2025-01-03 18:42:17",
                "data" : {
                    "processorStatusCode" : "00",
                    "currency" : "NGN",
                    "transactionReference" : "Testing-Client-483024249389489329959",
                    "sessionId" : "090633100913103301000039999999",
                    "expectedCollectionAmount" : 2000.00,
                    "amountCollected" : 2000.00,
                    "collectionValueAmount" : 1997.50,
                    "collectionFeeAmount" : 2.50,
                    "collectionCount" : 1,
                    "expectedCollectionCount" : 1,
                    "acceptMultipleAmount" : false,
                    "acceptLesserAmount" : false,
                    "transactionStartTime" : "2025-01-03 18:09:18",
                    "expirationDate" : "2025-01-03 18:09:33",
                    "narration" : "Payment Connection For Tax",
                    "beneficiaryAccountNumber" : "**********",
                    "beneficiaryAccountName" : "SYSTEMSPEC NNPC Payment",
                    "sourceAccountNumber" : "**********",
                    "sourceAccountName" : "Adewale Hassan",
                    "sourceBankName" : "AWACASH MFB",
                    "sourceBankCode" : "090633",
                    "collectionStatus" : "Completed",
                    "statusReason" : "Approved or completed successfully",
                    "collectionStatusHistory" : [ {
                    "amountReceived" : 2000.00,
                    "collectionValueAmount" : 1997.50,
                    "collectionFeeAmount" : 2.50,
                    "currency" : "NGN",
                    "dateReceived" : "2025-01-03 18:09:33",
                    "collectionCount" : 1,
                    "sourceBankCode" : "090633",
                    "sourceBankName" : "AWACASH MFB",
                    "sourceAccountNumber" : "**********",
                    "sourceAccountName" : "Adewale Hassan",
                    "collectionStatus" : "Completed",
                    "processorStatusCode" : "00",
                    "sessionId" : "090633100913103301000039999999",
                    "reason" : "Approved or completed successfully"
                    } ]
                }
            }
        """

        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                cls.objects.create(
                    data=data,
                )
                return

        main_data = data.get("data")

        try:
            cls.objects.get(transaction_reference=main_data.get("transactionReference"))
            return
        except cls.DoesNotExist:
            pass

            beneficiary_account_number = main_data.get("beneficiaryAccountNumber")
            beneficiary_account_name = main_data.get("beneficiaryAccountName")
            beneficiary_bank_code = main_data.get("beneficiaryBankCode")
            beneficiary_bank_name = main_data.get("beneficiaryBankName")
            beneficiary_account_id = main_data.get("beneficiaryAccountId")
            session_id = main_data.get("sessionId")
            amount_collected = main_data.get("amountCollected", 0)
            transaction_amount = main_data.get("transactionAmount", 0)
            transaction_status = main_data.get("transactionStatus", "")

            source_account_number = main_data.get("sourceAccountNumber")
            source_account_name = main_data.get("sourceAccountName")
            source_bank_name = main_data.get("sourceBankName")
            source_bank_code = main_data.get("sourceBankCode")
            narration = main_data.get("narration", f"Deposit from {source_account_name}")
            transactionValueAmount = main_data.get("transactionValueAmount", 0)

            notification_instance = cls.objects.create(
                data=data,
                processor_status_code=main_data.get("processorStatusCode"),
                processor_code=main_data.get("processorCode"),
                transaction_status=transaction_status,
                currency=main_data.get("currency"),
                transaction_reference=main_data.get("transactionReference"),
                session_id=session_id,
                expected_collection_amount=main_data.get("expectedCollectionAmount", 0),
                amount_collected=amount_collected,
                collection_value_amount=transactionValueAmount,
                collection_fee_amount=main_data.get("collectionFeeAmount", 0),
                collection_count=main_data.get("collectionCount", 0),
                expected_multiple_amount=main_data.get("expectedMultipleAmount", 0),
                accept_multiple_amount=main_data.get("acceptMultipleAmount", False),
                accept_lesser_amount=main_data.get("acceptLesserAmount", False),
                transaction_start_time=main_data.get("transactionStartTime"),
                expiration_date=main_data.get("expirationDate"),
                narration=narration,
                beneficiary_account_number=beneficiary_account_number,
                beneficiary_account_name=beneficiary_account_name,
                beneficiary_bank_code=beneficiary_bank_code,
                beneficiary_bank_name=beneficiary_bank_name,
                beneficiary_account_id=beneficiary_account_id,
                source_account_number=source_account_number,
                source_account_name=main_data.get("sourceAccountName"),
                source_bank_name=main_data.get("sourceBankName"),
                source_bank_code=main_data.get("sourceBankCode"),
                collection_status=main_data.get("collectionStatus"),
                status_reason=main_data.get("statusReason"),
                customer_reference=main_data.get("customerReference"),
                notification_request_reference=main_data.get(
                    "notificationRequestReference"
                ),
                transaction_amount=main_data.get("transactionAmount", 0),
                transaction_value_amount=transactionValueAmount,
                transaction_fee=main_data.get("transactionFeeAmount", 0),
            )

            ##############################################################################################
            ##############################################################################################
            ##############################################################################################
            ##############################################################################################
            ##############################################################################################

            if settings.ENVIRONMENT == "development":
                api_mode = enums.APIMode.TEST
            else:
                api_mode = enums.APIMode.LIVE

            account_details = CashConnectVirtualAccount.objects.filter(
                account_number=beneficiary_account_number, is_active=True
            ).first()

            if account_details is not None:
                # Identify the company's/sub-company's details.
                company = account_details.company
                if account_details.sub_company is not None:
                    sub_company = account_details.sub_company
                    charges = float(sub_company.service_fee)
                    account_type = enums.AccountType.SUB
                else:
                    sub_company = None
                    charges = float(company.service_fee)
                    account_type = enums.AccountType.MAIN
                # Instantiate the transaction record.
                credit_transaction = TransactionDetail.register_transaction(
                    company=company,
                    sub_company=sub_company,
                    beneficiary_account_number=beneficiary_account_number,
                    beneficiary_account_name=beneficiary_account_name,
                    amount=transactionValueAmount,
                    fee=charges,
                    amount_payable=transactionValueAmount - charges,
                    transaction_type=enums.TransactionType.CREDIT,
                    transaction_status=(
                        enums.TransactionStatus.SUCCESSFUL
                        if transaction_status.upper() == "COMPLETED"
                        else enums.TransactionStatus.FAILED
                    ),
                    narration=narration,
                    service_provider=enums.ServiceProvider.CASH_CONNECT,
                    mode=api_mode,
                    session_id=session_id,
                    bank_code=source_bank_code,
                    bank_name=source_bank_name,
                    source_account=source_account_number,
                    source_name=source_account_name,
                    is_verified=True,
                    one_time=account_details.one_time,
                    request_reference=account_details.request_reference,
                    offline=account_details.offline,
                    unique_code=account_details.unique_code,
                    confirmation_code=account_details.confirmation_code,
                )

                CashConnectVirtualAccount.fund(
                    nuban=beneficiary_account_number,
                    amount=transactionValueAmount,
                )
                # Update the associated wallet.
                wallet_transaction = AccountDetail.fund_account(
                    company=company,
                    account_type=account_type,
                    amount=transactionValueAmount,
                    charges=charges,
                    sub_company=sub_company,
                )
              
                if wallet_transaction.get("status"):
                    credit_transaction.balance_before = wallet_transaction.get(
                        "previous_balance"
                    )
                    credit_transaction.balance_after = wallet_transaction.get(
                        "account_balance"
                    )
                    credit_transaction.save()

                    send_company_transaction_callbacks.delay(
                        transaction_id=credit_transaction.id
                    )

            return notification_instance


class CashConnectCustomers(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    customer_reference = models.CharField(
        max_length=300, unique=True, default=generate_customer_reference
    )
    phone_number = models.CharField(max_length=200, blank=True, null=True, unique=True)
    title = models.CharField(
        max_length=255,
        choices=enums.UserTitle.choices,
        default=enums.UserTitle.MR,
        blank=True,
        null=True,
    )
    first_name = models.CharField(max_length=300, blank=True, null=True)
    middle_name = models.CharField(max_length=300, blank=True, null=True)
    last_name = models.CharField(max_length=300, blank=True, null=True)
    email = models.CharField(max_length=300, blank=True, null=True, unique=True)
    bvn = models.CharField(max_length=300, blank=True, null=True, unique=True)
    nin = models.CharField(max_length=300, blank=True, null=True, unique=True)
    dob = models.CharField(max_length=300, blank=True, null=True)
    address = models.CharField(max_length=300, blank=True, null=True)
    country = models.CharField(max_length=300, blank=True, null=True)
    state = models.CharField(max_length=300, blank=True, null=True)
    city = models.CharField(max_length=300, blank=True, null=True)
    local_govt = models.CharField(max_length=300, blank=True, null=True)
    gender = models.CharField(
        max_length=300,
        choices=enums.UserGender.choices,
        default=enums.UserGender.MALE,
        blank=True,
        null=True,
    )
    marital_status = models.CharField(
        max_length=300,
        choices=enums.UserMaritalStatus.choices,
        default=enums.UserMaritalStatus.SINGLE,
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = "CASH CONNECT CUSTOMER"
        verbose_name_plural = "CASH CONNECT CUSTOMERS"

    @classmethod
    def created_record(cls, **kwargs):
        return cls.objects.create(kwargs)

    @classmethod
    def customer_instance(cls, phone_number):
        try:
            return cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            return None


class CashConnectVirtualAccount(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    customer_reference = models.CharField(max_length=300, blank=True, null=True)
    account_name = models.CharField(max_length=300, blank=True, null=True)
    account_number = models.CharField(max_length=300, blank=True, null=True)
    account_id = models.CharField(max_length=300, blank=True, null=True)
    bank_code = models.CharField(max_length=300, blank=True, null=True)
    bank_name = models.CharField(max_length=300, blank=True, null=True)
    type_of_account = models.CharField(
        max_length=300, choices=enums.CashConnectTypeOfVirtualAccount.choices
    )
    current_inflow_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    previous_inflow_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    expiration_minute = models.IntegerField(default=0)
    transaction_reference = models.CharField(
        max_length=300, blank=True, null=True, unique=True
    )
    narration = models.CharField(max_length=300, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    deactivate_reason = models.TextField(null=True, blank=True)
    one_time = models.BooleanField(
        default=False,
        help_text="represents accounts used for one-time transactions.",
    )
    request_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="one-time account request tracker.",
    )
    request_active = models.BooleanField(
        default=False,
        help_text="indicator `True` if the one-time account is currently in use.",
    )
    offline = models.BooleanField(
        default=False,
        help_text="represents accounts used for offline transactions.",
    )
    unique_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="offline ussd unique code.",
    )
    confirmation_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="offline ussd confirmation code.",
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CASH CONNECT VIRTUAL ACCOUNT"
        verbose_name_plural = "CASH CONNECT VIRTUAL ACCOUNTS"

    @classmethod
    def get_account_details(cls, account_number: str):
        """
        Retrieve account details based on the provided account number.
        Returns:
            VirtualAccount: The VirtualAccount object if found else None.
        """
        try:
            account = cls.objects.get(account_number=account_number, is_active=True)
        except cls.DoesNotExist:
            account = None
        return account

    @classmethod
    def fund(cls, nuban: str, amount: float):
        """
        It aids `CREDIT` transaction(s).
        """
        account_details = cls.get_account_details(account_number=nuban)
        if account_details is None:
            return False
        current_state = float(account_details.current_inflow_balance)
        account_details.previous_inflow_balance = current_state
        account_details.current_inflow_balance = models.F(
            "current_inflow_balance"
        ) + float(amount)
        account_details.save()
        return True


class CashConnectRequestLogs(BaseModel):
    payload = models.TextField()
    response_data = models.TextField(blank=True, null=True)
    type_of_request = models.CharField(
        max_length=300,
        choices=enums.CashConnectRquestTypes.choices,
    )

    class Meta:
        verbose_name = "CASH CONNECT REQUEST LOG"
        verbose_name_plural = "CASH CONNECT REQUEST LOGS"

    @classmethod
    def make_request(cls, type_of_request, *args, **kwargs):
        """
        Creates a cash connect request log entry and processes the request based on its type.

        Args:
            cls: The class reference.
            type_of_request (str): The type of request being made.
            **kwargs: Additional parameters to be included in the request payload.

        Returns:
            dict: The response data from the request if applicable.
        """

        instance = cls.objects.create(payload=kwargs, type_of_request=type_of_request)

        if type_of_request == enums.CashConnectRquestTypes.VIRTUAL_ACCOUNT:

            # return {'responseCode': 200, 'processorStatusCode': '00', 'responseMessage': 'Approved or completed successfully', 'responseTime': '2025-02-10 21:26:40', 'data': {'accountNumber': '**********', 'accountName': 'OKORO CHIOMA LIBERTYPAY', 'customerReference': 'LIBERTYPAY-APUTT5FND5ASPFK', 'accountId': '1338607091786022912', 'bankCode': '090360', 'bankName': 'Cash Connect'}}
            res = CashConnectHelperClass().create_virtual_account(**kwargs)

            instance.response_data = res
            instance.save()

            return res

        if type_of_request == enums.CashConnectRquestTypes.INSTANT_VIRTUAL_ACCOUNT:
            res = CashConnectHelperClass().create_dynamic_virtual_account(**kwargs)

            instance.response_data = res
            instance.save()

            return res

        if type_of_request == enums.CashConnectRquestTypes.INTER_DISBURSEMENT:
            res = CashConnectHelperClass().inter_disbursement(**kwargs)

            instance.response_data = res
            instance.save()

            return res
        
        if type_of_request == enums.CashConnectRquestTypes.INTRA_DISBURSEMENT:
            res = CashConnectHelperClass().intra_disbursement(**kwargs)

            instance.response_data = res
            instance.save()

            return res

        if type_of_request == enums.CashConnectRquestTypes.DISBURSEMENT_VERIFICATION:
            res = CashConnectHelperClass().transaction_status_by_trans_ref(
                kwargs.get("transaction_reference")
            )

            instance.response_data = res
            instance.save()

            return res

        if type_of_request == enums.CashConnectRquestTypes.VIRTUAL_ACCOUNT_BY_NIN:
            res = CashConnectHelperClass().create_personal_account_by_nin(**kwargs)

            instance.response_data = res
            instance.save()

            return res
        if type_of_request == enums.CashConnectRquestTypes.VIRTUAL_ACCOUNT_BY_BVN:
            res = CashConnectHelperClass().create_personal_account_by_bvn(
                *args, **kwargs
            )

            instance.response_data = res
            instance.save()

            return res
    
    @classmethod
    def make_loan_request(cls, type_of_request, *args, **kwargs):
        """
        Creates a cash connect loan request log entry and processes the request based on its type.

        Args:
            cls: The class reference.
            type_of_request (str): The type of request being made.
            **kwargs: Additional parameters to be included in the request payload.

        Returns:
            dict: The response data from the request if applicable.
        """

        instance = cls.objects.create(payload=kwargs, type_of_request=type_of_request)

        if type_of_request == enums.CashConnectRquestTypes.INTER_DISBURSEMENT:
            res = CashConnectHelperClass().loan_inter_disbursement(**kwargs)

            instance.response_data = res
            instance.save()
            return res
        
        if type_of_request == enums.CashConnectRquestTypes.INTRA_DISBURSEMENT:
            res = CashConnectHelperClass().loan_intra_disbursement(**kwargs)

            instance.response_data = res
            instance.save()
            return res

        if type_of_request == enums.CashConnectRquestTypes.DISBURSEMENT_VERIFICATION:
            res = CashConnectHelperClass().loan_transaction_status_by_trans_ref(
                kwargs.get("transaction_reference"),
            )

            instance.response_data = res
            instance.save()

            return res
        
    @classmethod
    def admin_transfer_to_external_account(cls, transfer_money_obj):
        from cash_connect.helpers.manage_request import CashConnectRequestManagers

        # Initiate intra-bank transfer
        request_data = {
            "transaction_reference": transfer_money_obj.request_reference,
            "amount": float(transfer_money_obj.amount),
            "beneficiary_account_number": transfer_money_obj.account_number,
            "beneficiary_account_name": transfer_money_obj.account_name,
            "beneficiary_bank_code": transfer_money_obj.bank_code,
            "narration": transfer_money_obj.narration,
            "mode": transfer_money_obj.mode,
            "company_id": transfer_money_obj.company.id,
            "type_of_request": enums.CashConnectRquestTypes.INTER_DISBURSEMENT,
        }

        response_status, response_data = CashConnectRequestManagers().handle_inter_bank_transfer(
            **request_data
        )

        transfer_money_obj.request_response = response_data
        transfer_money_obj.save(update_fields=["request_response"])

        if response_status:
            data = {
                "status": response_data.get("status"),
                "message": response_data.get("message"),
                "transaction": request_data,
                "account_balance": response_data.get("account_balance"),
            }

            # transfer_money_obj.status = enums.TransactionStatus.SUCCESSFUL
            # transfer_money_obj.save(update_fields=["status"])
            return data
        else:
            return response_data

class CashConnectDisbursementWalletFundingFromSettlementWalletLogs(BaseModel):
    """
    This model is used to track the funding of cash connect disbursement from the settlement wallet.
    """

    transaction_reference = models.CharField(max_length=300, blank=True, null=True)
    amount = models.FloatField(default=0)
    status = models.CharField(max_length=300, blank=True, null=True)
    payload = models.TextField()
    response_data = models.TextField(blank=True, null=True)


    @classmethod
    def fund_disbursement_account_from_collections(cls, transfer_money_obj):
        from cash_connect.helpers.manage_request import CashConnectRequestManagers

        # Initiate intra-bank transfer
        cash_connect_intra_bank_payload = {
            "disbursementReference": transfer_money_obj.request_reference,
            "disbursementAmount": float(transfer_money_obj.amount),
            "beneficiaryAccount": transfer_money_obj.account_number,
            "beneficiaryAccountName": transfer_money_obj.account_name,
            "narration": transfer_money_obj.narration,
        }

        response_data = CashConnectRequestLogs.make_request(
            type_of_request=enums.CashConnectRquestTypes.INTRA_DISBURSEMENT,
            **cash_connect_intra_bank_payload
        )

        log_instance = cls.objects.create(
            transaction_reference=transfer_money_obj.request_reference,
            amount=float(transfer_money_obj.amount),
            status=enums.TransactionStatus.PENDING,
            payload=cash_connect_intra_bank_payload,
            response_data=response_data,
        )

        if response_data:
            transfer_money_obj.status = enums.TransactionStatus.SUCCESSFUL
            log_instance.status = enums.TransactionStatus.SUCCESSFUL
            log_instance.save(update_fields=["status"])
            transfer_money_obj.save(update_fields=["status"])
        else:
            log_instance.status = enums.TransactionStatus.FAILED
            log_instance.save(update_fields=["status"])

        return response_data
