import json
from django.contrib import admin
from accounts.helpers.functions import parse_to_dict
from accounts.models import AccountDetail, TransactionDetail
from cash_connect import resources
from import_export.admin import ImportExportModelAdmin
from cash_connect import models
from helpers import enums
# from pprint import pprint
from django.conf import settings
from wema_bank.tasks import send_company_transaction_callbacks


# Register your models here.
class CashConnectWebHookDataResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CashConnectWebHookDataResource
    search_fields = [
        "source_account_number",
        "source_account_name",
        "beneficiary_account_number",
        "beneficiary_account_name",
        "session_id",
        "transaction_reference",
    ]

    list_filter = [
        "created_at",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def resolve_unsettled_transfer(
        self, request, queryset: models.CashConnectWebHookData
    ):
        message = "Invalid Action"

        for instance in queryset:

            data = instance.data
            result = parse_to_dict(data)
            main_data = result.get("data")
            # pprint(main_data)

            beneficiary_account_number = main_data.get("beneficiaryAccountNumber")
            beneficiary_account_name = main_data.get("beneficiaryAccountName")
            # beneficiary_bank_code = main_data.get("beneficiaryBankCode")
            # beneficiary_bank_name = main_data.get("beneficiaryBankName")
            # beneficiary_account_id = main_data.get("beneficiaryAccountId")
            session_id = main_data.get("sessionId")
            # amount_collected = main_data.get("amountCollected", 0)
            # transaction_amount = main_data.get("transactionAmount", 0)
            transaction_status = main_data.get("transactionStatus", "")

            source_account_number = main_data.get("sourceAccountNumber")
            source_account_name = main_data.get("sourceAccountName")
            source_bank_name = main_data.get("sourceBankName")
            source_bank_code = main_data.get("sourceBankCode")
            narration = main_data.get(
                "narration", f"Deposit from {source_account_name}"
            )
            transactionValueAmount = main_data.get("transactionValueAmount", 0)

            account_details = models.CashConnectVirtualAccount.objects.filter(
                account_number=beneficiary_account_number, is_active=True
            ).first()

            if settings.ENVIRONMENT == "development":
                api_mode = enums.APIMode.TEST
            else:
                api_mode = enums.APIMode.LIVE

            if account_details is not None:
                # Identify the company's/sub-company's details.
                company = account_details.company
                if account_details.sub_company is not None:
                    sub_company = account_details.sub_company
                    charges = float(sub_company.service_fee)
                    account_type = enums.AccountType.SUB
                else:
                    sub_company = None
                    charges = float(company.service_fee)
                    account_type = enums.AccountType.MAIN
                # Instantiate the transaction record.
                credit_transaction = TransactionDetail.register_transaction(
                    company=company,
                    sub_company=sub_company,
                    beneficiary_account_number=beneficiary_account_number,
                    beneficiary_account_name=beneficiary_account_name,
                    amount=transactionValueAmount,
                    fee=charges,
                    amount_payable=transactionValueAmount - charges,
                    transaction_type=enums.TransactionType.CREDIT,
                    transaction_status=(
                        enums.TransactionStatus.SUCCESSFUL
                        if transaction_status.upper() == "COMPLETED"
                        else enums.TransactionStatus.FAILED
                    ),
                    narration=narration,
                    service_provider=enums.ServiceProvider.CASH_CONNECT,
                    mode=api_mode,
                    session_id=session_id,
                    bank_code=source_bank_code,
                    bank_name=source_bank_name,
                    source_account=source_account_number,
                    source_name=source_account_name,
                    is_verified=True,
                    one_time=account_details.one_time,
                    request_reference=account_details.request_reference,
                    offline=account_details.offline,
                    unique_code=account_details.unique_code,
                    confirmation_code=account_details.confirmation_code,
                )

                models.CashConnectVirtualAccount.fund(
                    nuban=beneficiary_account_number,
                    amount=transactionValueAmount,
                )
                # Update the associated wallet.
                wallet_transaction = AccountDetail.fund_account(
                    company=company,
                    account_type=account_type,
                    amount=transactionValueAmount,
                    charges=charges,
                    sub_company=sub_company,
                )

                if wallet_transaction.get("status"):
                    credit_transaction.balance_before = wallet_transaction.get(
                        "previous_balance"
                    )
                    credit_transaction.balance_after = wallet_transaction.get(
                        "account_balance"
                    )
                    credit_transaction.save()

                    send_company_transaction_callbacks.delay(
                        transaction_id=credit_transaction.id
                    )
                message = credit_transaction

            else:
                message = f"Account DoesNotExist {beneficiary_account_number}"
        self.message_user(request, str(message))

    resolve_unsettled_transfer.short_description = "Resolve Unsettled Transfer"
    resolve_unsettled_transfer.allow_tags = True

    actions = [
        resolve_unsettled_transfer,
    ]


class CashConnectCustomersResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CashConnectCustomersResource
    search_fields = [
        "customer_reference",
        "phone_number",
        "first_name",
        "middle_name",
        "last_name",
        "email_name",
        "bvn",
        "nin",
    ]

    list_filter = ["created_at", "company", "title", "gender", "marital_status"]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CashConnectVirtualAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CashConnectVirtualAccountResource
    search_fields = [
        "customer_reference",
        "first_name",
        "last_name",
        "account_number",
    ]

    list_filter = [
        "created_at",
        "company",
        "type_of_account",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CashConnectRequestLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CashConnectRequestLogsResource

    list_filter = [
        "created_at",
        "type_of_request",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class CashConnectDisbursementWalletFundingFromSettlementWalletLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CashConnectDisbursementWalletFundingFromSettlementWalletLogsResource

    list_filter = [
        "created_at",
    ]

    date_hierarchy = "created_at"

    search_fields = ["transaction_reference"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.CashConnectWebHookData, CashConnectWebHookDataResourceAdmin)
admin.site.register(models.CashConnectCustomers, CashConnectCustomersResourceAdmin)
admin.site.register(
    models.CashConnectVirtualAccount, CashConnectVirtualAccountResourceAdmin
)
admin.site.register(models.CashConnectRequestLogs, CashConnectRequestLogsResourceAdmin)
admin.site.register(models.CashConnectDisbursementWalletFundingFromSettlementWalletLogs, CashConnectDisbursementWalletFundingFromSettlementWalletLogsResourceAdmin)
