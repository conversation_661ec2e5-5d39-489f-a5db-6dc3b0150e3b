from django.urls import path, re_path

from cash_connect.views import (
    AccountNameLookupInterApiView, CashConnectWebhookNotificationApiView,
    CreateDynamicInstantVirtualAccountApiView, CreateVirtualAccountApiView,
    CreateVirtualAccountWithIdApiView, FetchBankListApiView,
    FetchCashConnectFloatAccountBalanceApiView, GetCashConnectAccountDetailApiView,
    InterBankTransferApiView, IntraBankTransferApiView,
    MoveValueFromSettlementWalletToDisubursementWalletApiView,
    VerifyCashConnectBankTransferApiView, BookCashConnectLoanApiView,
    FetchCashConnectLoansRuntimeParamsView, DisburseCashConnectLoanApiView,
    CheckSettlementAccountBalancesView, CashConnectLoanRepaymentScheduleView,
    CashConnectFetchCustomerLoansView, CashConnectLoanDetailsView,
    CashConnectFetchCustomerLoanSettlementAccountView,
    CashConnectAccountEnquiryView, CashConnectFetchAccountDetailsView,
    LoanIntraBankTransferApiView, LoanInterBankTransferApiView,
    LoanVerifyCashConnectBankTransferApiView,
)

LOANS_URLS = [
    path("book_cash_connect_loan/", BookCashConnectLoanApiView.as_view()),
    path("fetch_cash_connect_loan_params/", FetchCashConnectLoansRuntimeParamsView.as_view()),
    path("disburse_cash_connect_loan/", DisburseCashConnectLoanApiView.as_view()),
    path("fetch-cash-connect-loan-repayment-schedule/", CashConnectLoanRepaymentScheduleView.as_view()),
    path("fetch-cash-connect-customer-loans/", CashConnectFetchCustomerLoansView.as_view()),
    path("fetch-cash-connect-loans-details/", CashConnectLoanDetailsView.as_view()),
    path("fetch-cash-connect-customer-loans-settlement-account/", CashConnectFetchCustomerLoanSettlementAccountView.as_view()),
    path("make-cash-connect-account-enquiry/", CashConnectAccountEnquiryView.as_view()),
    path("fetch-cash-connect-account-details/", CashConnectFetchAccountDetailsView.as_view()),
    # path("fetch-cash-connect-loan-settlement-balance/", CashConnectFetchLoanSettlementBalanceView.as_view()),
    path("loan_intra-transfer/", LoanIntraBankTransferApiView.as_view(), name="loan_intra_cash_connect_transfer"), ##
    path("loan_inter-transfer/", LoanInterBankTransferApiView.as_view(), name="loan_inter_cash_connect_transfer"), ##
    path("loan_partner_disbursement_status/", LoanVerifyCashConnectBankTransferApiView.as_view()),
]

urlpatterns = [
    re_path("webhook/", CashConnectWebhookNotificationApiView.as_view()),
    # path("webhook/", cash_connect_webhook_two),
    path("create-virtual-account/", CreateVirtualAccountApiView.as_view(), name="create_cash_connect_virtual_account"),
    path("create-virtual-account-with-id/", CreateVirtualAccountWithIdApiView.as_view()),
    path("get-cash-connect-account-details/", GetCashConnectAccountDetailApiView.as_view()),
    path("create-instant-account/", CreateDynamicInstantVirtualAccountApiView.as_view()),
    path("inter-transfer/", InterBankTransferApiView.as_view(), name="inter_cash_connect_transfer"), ##
    path("intra-transfer/", IntraBankTransferApiView.as_view(), name="intra_cash_connect_transfer"), ##
    path("partner_disbursement_status/", VerifyCashConnectBankTransferApiView.as_view()),
    path("fetch-bank-list/", FetchBankListApiView.as_view()),
    path("account-name-lookup-inter/", AccountNameLookupInterApiView.as_view()),
    path("account-account-pool-details/", FetchCashConnectFloatAccountBalanceApiView.as_view()),
    path("disbursement_wallet_funding_from_settlement_wallet/", MoveValueFromSettlementWalletToDisubursementWalletApiView.as_view()),
    path("check-settlement-account-balance/", CheckSettlementAccountBalancesView.as_view()),
    *LOANS_URLS,
]
