from django.core.management.base import (
    BaseCommand,
    Command<PERSON>rror,
    CommandParser,
)
from django.db.models import Q

from accounts import models
from cash_connect.helpers.cash_connect_call_backs import CashConnectHelperClass
from helpers import enums


class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        CashConnectHelperClass().account_transaction_status_by_account_and_ref(account=**********, ref="*****************")

        CashConnectHelperClass().account_transaction_status_by_ref(ref="*****************")

        CashConnectHelperClass().account_transaction_status_by_session_id(session_id="835684-84326326803426030567")

        CashConnectHelperClass().account_transaction_status_by_account_number_and_session_id(account_number=**********, session_id="835684-84326326803426030567")

        CashConnectHelperClass().account_transaction_status_by_account_id(account_id=**************)

        payload = {
            "account_name": **********,
            "expected_collection_amount": 2000,
            "customer_id": "RMD-*************",
            "transaction_reference": "Testing-Client-483024249389489324928",
            "vn": "*************",  # optional
            "kyc_level": 1,  # 1,2,3 optional
            "narration": "Payment Connection For Tax"
        }
        CashConnectHelperClass().generate_account(validated_data=payload)

        payload = {
            "account_name": "Wallace Johnny",
            "expected_collection_amount": 2000,
            "customer_id": "RMD-*************",
            "transaction_reference": "Testing-Client-483024249389489324928",
            "bvn": "*************",  # optional
            "kyc_level": 1,  # 1,2,3 optional
            "narration": "Payment Connection For Tax",
            "expired_minute": 20  # please note that default expiration time is 30 minutes should you wish to reduce it
        }
        CashConnectHelperClass().generate_account_with_expiration_time(validated_data=payload)

        payload = {
            "account_name": "Wallace Johnny",
            "expected_collection_amount": 2000.00,
            "minimum_collection_amount_allowed": 1000.00,
            "customer_id": "RMD-*************",
            "transaction_reference": "Testing-Client-****************",
            "narration": "Payment Connection For Tax",
            "bvn": "*************",  # optional
            "kyc_level": 1,  # 1,2,3 optional
            "expired_minutes": 30,
            "collection_count": 3  # the number of time the account should collect found before expiring
        }
        CashConnectHelperClass().generate_account_accept_multiple_amount(validated_data=payload)

        payload = {
            "source_account_name": "Femi Leke",
            "source_account_number": "**********",
            "source_bank_code": "011",
            "source_bank_name": "Access Bank PLC",
            "beneficiary_account_number": **********9,  # your dynmaic account number
            "beneficiary_account_name": "Payment Collection",  # your dynamic account name
            "transaction_amount": 2000.00,
            "narration": "Sample Funding"
        }
        CashConnectHelperClass().testing_funding(validated_data=payload)

        CashConnectHelperClass().dynamic_account_name_enquiry(account_name="Wallace Johnny", account_number=***********)

        payload = {
            "account_name": "NNPC Collection Payment",
            "account_number": ***********,  # please note that this account number should be unique for a day
            "expected_collection_amount": 2000,
            "creation_time": "01:30:00",
            "expired_time": "02:00:00",
            "customer_id": "RMD-*************",
            "transaction_reference": "Testing-Client-483024249389489324908",  # your transaction reference should have your partner code and should also be unique
            "bvn": "*************",  # optional
            "kycLevel": 1,  # 0,1,2,3 optional
            "narration": "Payment Connection For Tax"
        }
        CashConnectHelperClass().post_dynamic_virtual_account(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "title": "Mr",
            "first_name": "Ayomide",
            "middle_name": "Smauel",
            "last_name": "Adewale",
            "email_address": "<EMAIL>",
            "phone_number": "***********",
            "bvn": "2**********2",  # optional bvn or nin
            "nin": "***********",  # optional bvn or nin
            "date_of_birth": "2020-10-11",
            "address": "plot 12 chop road ibeju lekki lagos state",
            "country": "Nigeria",
            "state": "Lagos",
            "city": "Ikoyi",
            "local_govt": "Ibeju Lekki",
            "marital_status": "Single",  # Single, Married, Widowed, Divorced, Separated;
            "gender": "Male"
        }
        CashConnectHelperClass().create_personal_account_verified_partner(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "bvn": "***********",
            "date_of_birth": "1988-03-21"
        }
        CashConnectHelperClass().create_personal_account_by_bvn(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "nin": "***********",
            "date_of_birth": "1982-03-11"
        }
        CashConnectHelperClass().create_personal_account_by_nin(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "bvn": "2**********1",
            "previous_account_number": "**********"
        }
        CashConnectHelperClass().additional_personal_account_by_bvn(validated_data=payload)

        payload = {
            "customerR_reference": "Testing-Client-*************3323232",
            "nin": "***********",
            "previous_account_number": "**********"
        }
        CashConnectHelperClass().additional_personal_account_by_nin(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "business_name": "SystemSpecs Limited",
            "business_address": "79 Lewis Street Obalende Lagos",
            "country": "Nigeria",
            "state": "Lagos",
            "local_govt": "ETI-OSA",
            "contact_email": "<EMAIL>",
            "contact_phone_number": "+*************",
            "business_type": " Business_Name",
            "city": "Oniru",
            "rc_number": "RC-********",
            "incorporation_date": "2010-10-10",
            "bvn": "***********",  # optional information either bvn or nin
            "nin": "***********"  # optional information bvn or nin
        }
        CashConnectHelperClass().create_corporate_account(validated_data=payload)

        payload = {
            "customer_reference": "SPECS-2345678765434566577",
            "rc_number": "RC-********",
            "incorporation_date": "2020-10-11",
            "bvn": "***********00"
        }
        CashConnectHelperClass().create_corporate_account_by_bvn(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "rc_number": "RC-********",
            "incorporation_date": "2020-10-11",
            "nin": "***********"
        }
        CashConnectHelperClass().create_corporate_account_by_nin(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "rc_number": "RC-********",
            "bvn": "***********",
            "previous_account_number": "**********"
        }
        CashConnectHelperClass().additional_corporate_account_by_bvn(validated_data=payload)

        payload = {
            "customer_reference": 2345678765434566577,
            "rc_number": "RC-********",
            "nin": "***********",
            "previous_account_number": "**********"
        }
        CashConnectHelperClass().additional_corporate_account_by_nin(validated_data=payload)

        payload = {
            "disbursement_reference": 2345678765434566577,
            "disbursement_amount": 100.00,
            "beneficiary_account": "**********",
            "beneficiary_account_name": "Okki Samuel Cash",
            "beneficiary_bank_code": "000004",
            "narration": "From My Payment",
            "customer_wallet_account_number": ""  # optional for showing who is sending the wallet account
        }
        CashConnectHelperClass().inter_disbursement(validated_data=payload)

        payload = {
            "ddisbursement_reference": 2345678765434566577,
            "ddisbursement_amount": 500.00,
            "bbeneficiary_account": "**********",
            "bbeneficiary_account_name": "REMITASTP NNPC Checkout",
            "nnarration": "Account Transfer",
            "ccustomer_wallet_account_number": ""
        }
        CashConnectHelperClass().intra_disbursement(validated_data=payload)
