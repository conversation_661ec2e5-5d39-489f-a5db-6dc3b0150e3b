from decouple import config
import json
import requests

# from cash_connect.helpers import CashConnectRedisStorage, STSLEncryptionHandlerFactory
# from .cash_connect_base_file import CashConnectRedisStorage, STSLEncryptionHandlerFactory
from cash_connect.helpers.cash_connect_base_file import CashConnectRedisStorage, STSLEncryptionHandlerFactory
from cash_connect.helpers.cash_connect_loans_base_file import STSLEncryptionHandlerFactory as LoanHandlerFactory


class CashConnectHelperClass:
    def __init__(self):
        self.base_url = config("CASH_CONNECT_BASE_URL")
        self.partnerCode = config("CASH_CONNECT_PARTNER_CODE")
        self.client_id = config("CASH_CONNECT_CLIENT_ID")
        self.loan_partner_code = config("CASH_CONNECT_LOAN_PARTNER_CODE")
        self.loan_client_id = config("CASH_CONNECT_LOAN_CLIENT_ID")
        self.handler = STSLEncryptionHandlerFactory()
        self.loan_handler = LoanHandlerFactory()
        print("BASE_URL =============>", self.base_url)

    def get_headers(self):
        return {
            "Content-Type": "application/json",
            "client-id": self.client_id,
        }
    
    def get_loan_headers(self):
        return {
            "Content-Type": "application/json",
            "client-id": self.loan_client_id,
        }

    def get_auth_token(self, re_run=3):

        # Check if the token is in redis
        token = CashConnectRedisStorage(redis_key="cash_connect_auth_token").get_data()

        if token:
            print("Token from redis")
            return token

        url = f"{self.base_url}/authenticationservice/rest/api/generate/auth/token"

        print("url", url)

        payload = {
            "partnerCode": config('CASH_CONNECT_PARTNER_CODE'),
            "username": config('CASH_CONNECT_USERNAME'),
            "password": config('CASH_CONNECT_PASSWORD')
        }

        print("payload", payload)

        headers = self.get_headers()

        # Convert to JSON
        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        print("encrypted_payload", encrypted_payload)

        # final_payload = json.dumps({"dencrypted_payload)

        response = requests.post(url, data=encrypted_payload, headers=headers)

        print("response", response.text)

        # Decrypt the response
        decrypted_response = self.handler.decrypt_data(response.text)

        print("decrypted_response", decrypted_response)

        """
            SAMPLE DECRYPTED RESPONSE

            {
                "responseCode" : 200,
                "processorStatusCode" : "00",
                "responseMessage" : "Approved or completed successfully",
                "responseTime" : "2025-01-30 11:43:10",
                "data" : {
                    "token" : "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xHD2IE4L28XEd5FATg8n_rnCDklG0yDdsW_6Re7049Alb093pMAmQ61Zz32BzqpfYK6HoN6kvr8ERX91a0JXqg",
                    "expiredAt" : 7200,
                    "creationDate" : "2025-JAN-30 11:43:10 AM",
                    "expiredDate" : "2025-JAN-30 13:43:10 PM",
                    "tokenType" : "Bearer"
                }
            }


        """

        try:
            serialized_data = json.loads(decrypted_response)
        except Exception as e:
            serialized_data = decrypted_response

        if isinstance(serialized_data, dict):
            if serialized_data.get("responseCode") != 200:

                if re_run <= 0:
                    return None

                return self.get_auth_token(re_run - 1)

            expiration_time = serialized_data.get("data", {}).get("expiredAt", 60)
            token = serialized_data.get("data", {}).get("token", "")

            # save the token in redis
            CashConnectRedisStorage(redis_key="cash_connect_auth_token").set_data(token, expiration_time)

            return token

        else:
            if re_run <= 0:
                return None

            return self.get_auth_token(re_run - 1)
        
    def get_loan_auth_token(self, re_run=3):

        # Check if the token is in redis
        # token = CashConnectRedisStorage(redis_key="cash_connect_loan_auth_token").get_data()

        # if token:
        #     print("Token from redis")
        #     return token

        url = f"{self.base_url}/authenticationservice/rest/api/generate/auth/token"

        # print("url", url)

        payload = {
            "partnerCode": config("CASH_CONNECT_LOAN_PARTNER_CODE"),
            "username": config("CASH_CONNECT_LOAN_USERNAME"),
            "password": config("CASH_CONNECT_LOAN_PASSWORD"),
        }

        headers = self.get_loan_headers()

        # Convert to JSON
        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        # print(":::::::::ENCRYPTED AUTHENTICATION PAYLOAD::::", encrypted_payload)

        response = requests.post(url, headers=headers, data=encrypted_payload)
        # print("DECRYPTED PAYLOAD>>>>>>>>", self.loan_handler.decrypt_data(encrypted_payload))
        # print("RAW RESPONSE>>>>>>>>", response.text)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        # print("LOGIN decrypted_response", decrypted_response)

        try:
            serialized_data = json.loads(decrypted_response)
        except Exception as e:
            serialized_data = decrypted_response

        if isinstance(serialized_data, dict):
            # print("SERIALIZED DATA:::::::::::::", serialized_data)
            if serialized_data.get("responseCode") != 200:

                if re_run <= 0:
                    return None

                return self.get_loan_auth_token(re_run - 1)

            expiration_time = serialized_data.get("data", {}).get("expiredAt", 60)
            token = serialized_data.get("data", {}).get("token", "")

            # save the token in redis
            CashConnectRedisStorage(redis_key="cash_connect_loan_auth_token").set_data(
                token, expiration_time
            )

            return token

        else:
            if re_run <= 0:
                return None

            return self.get_loan_auth_token(re_run - 1)

    def account_transaction_status_by_account_and_ref(self, account, ref):
        url = f"{self.base_url}/accountservice/rest/api/account/status/{account}/{ref}/status"
        print("URL =============>", url)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        print("headers", headers)

        response = requests.get(url, headers=headers)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
            NOT FOUND RESPONSE

            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/account/status/**********/Testing-Client-****************/status.",
                "instance" : "/accountservice/rest/api/account/status/**********/Testing-Client-****************/status",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def account_transaction_status_by_ref(self, ref):
        url = f"{self.base_url}/accountservice/rest/api/account/status/{ref}/status"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def account_transaction_status_by_session_id(self, session_id):
        url = f"{self.base_url}/accountservice/rest/api/account/status/{session_id}"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def account_transaction_status_by_account_number_and_session_id(self, account_number, session_id):
        url = f"{self.base_url}/accountservice/rest/api/account/status/status/{account_number}/{session_id}"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def account_transaction_status_by_account_id(self, account_id):
        url = f"{self.base_url}/accountservice/rest/api/account/status/{account_id}/status"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def generate_account(self, validated_data):
        account_name = validated_data.get("account_name")
        validated_data["partner_code"] = self.partnerCode

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "accountName": validated_data.get("account_name"),
            "expectedCollectionAmount": validated_data.get("expected_collection_amount"),
            "customerId": validated_data.get("customer_id"),
            "transactionReference": validated_data.get("transaction_reference"),
            "bvn": validated_data.get("bvn"),
            "kycLevel": validated_data.get("kyc_level"),
            "narration": validated_data.get("narration")
        }

        base_url = f'{config("CASH_CONNECT_BASE_URL")}/virtualaccountservice/rest/api/generate/dynamic/virtual/{account_name}/request'

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=base_url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        403 RESPONSE
        {
          "type" : "about:blank",
          "title" : "Not Found",
          "status" : 404,
          "detail" : "No static resource rest/api/generate/dynamic/virtual/account_name/request.",
          "instance" : "/virtualaccountservice/rest/api/generate/dynamic/virtual/account_name/request",
          "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def generate_account_with_expiration_time(self, validated_data):
        account_name = validated_data.get("account_name")
        validated_data["partner_code"] = self.partnerCode

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "accountName": validated_data.get("account_name"),
            "expectedCollectionAmount": validated_data.get("expected_collection_amount"),
            "customerId": validated_data.get("customer_id"),
            "transactionReference": validated_data.get("transaction_reference"),
            "bvn": validated_data.get("bvn"),
            "kycLevel": validated_data.get("kyc_level"),
            "narration": validated_data.get("narration"),
            "expiredMinutes": validated_data.get("expired_minutes")
        }

        base_url = f'{config("CASH_CONNECT_BASE_URL")}/virtualaccountservice/rest/api/generate/dynamic/virtual/{account_name}/request'

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=base_url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        403 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/generate/dynamic/virtual/account_name/request.",
            "instance" : "/virtualaccountservice/rest/api/generate/dynamic/virtual/account_name/request",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def generate_account_accept_multiple_amount(self, validated_data):
        account_name = validated_data.get("account_name")
        validated_data["partner_code"] = self.partnerCode

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "accountName": validated_data.get("account_name"),
            "expectedCollectionAmount": validated_data.get("expected_collection_amount"),
            "minimumCollectionAmountAllowed": validated_data.get("minimum_collection_amount_allowed"),
            "customerId": validated_data.get("customer_id"),
            "transactionReference": validated_data.get("transaction_reference"),
            "narration": validated_data.get("narration"),
            "bvn": validated_data.get("bvn"),
            "kycLevel": validated_data.get("kyc_level"),
            "expiredMinutes": validated_data.get("expired_minutes"),
            "allowMultipleCollections": True,
            "collectionCount": validated_data.get("collection_count"),
        }

        base_url = f'{config("CASH_CONNECT_BASE_URL")}/virtualaccountservice/rest/api/generate/dynamic/virtual/{account_name}/request'

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=base_url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        print("RESPONSE << decrypted_response >> ===========> ", decrypted_response)

        """
        403 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/generate/dynamic/virtual/account_name/request.",
            "instance" : "/virtualaccountservice/rest/api/generate/dynamic/virtual/account_name/request",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def testing_funding(self, validated_data):
        beneficiary_account_number = validated_data.get("beneficiary_account_number")
        base_url = f'{config("CASH_CONNECT_BASE_URL")}/virtualaccountservice/rest/api/sample/dynamic/{beneficiary_account_number}/funding'
        print("TESTING FUNDING BASE_URL =============>", base_url)
        payload = {
            "sourceAccountName": validated_data.get("source_account_name"),
            "sourceAccountNumber": validated_data.get("source_account_number"),
            "sourceBankCode": validated_data.get("source_bank_code"),
            "sourceBankName": validated_data.get("source_bank_name"),
            "beneficiaryAccountNumber": validated_data.get("beneficiary_account_number"),
            "beneficiaryAccountName": validated_data.get("beneficiary_account_name"),
            "transactionAmount": validated_data.get("transaction_amount"),
            "narration": validated_data.get("narration")
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=base_url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/sample/dynamic/**********/funding.",
            "instance" : "/virtualaccountservice/rest/api/sample/dynamic/**********/funding",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def dynamic_account_name_enquiry(self, account_name, account_number):
        base_url = f'{config("CASH_CONNECT_BASE_URL")}/virtualaccountservice/rest/api/account/{account_name}/enquiry/{account_number}'

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=base_url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/account/dynamic_account_name_enquiry/enquiry/1234.",
                "instance" : "/virtualaccountservice/rest/api/account/dynamic_account_name_enquiry/enquiry/1234",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_dynamic_virtual_account(self, **kwargs):

        kwargs["partnerCode"] = self.partnerCode
        kwargs["customerReference"] = f'{self.partnerCode}-{kwargs.get("customerReference")}'

        url = f'{config("CASH_CONNECT_BASE_URL")}/virtualaccountservice/rest/api/generate/dynamic/virtual/account/request'

        json_payload = json.dumps(kwargs)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/post/partner/generated/dynamicvirtual/**********/request.",
                "instance" : "/virtualaccountservice/rest/api/post/partner/generated/dynamicvirtual/**********/request",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_virtual_account(self, **kwargs):

        """
            Creates a virtual account by sending a request to the cash connect service.

            This method constructs a request payload, encrypts it, and sends it to the 
            specified URL for creating a virtual account. It handles the response by 
            decrypting it and returning the result.

            Args:
                self: The instance of the class.
                **kwargs: Additional parameters required for creating the virtual account, 
                        including customer reference and other necessary details.

            Returns:
                dict or str: The response from the service, parsed as a dictionary if 
                            successful, or the raw decrypted response string in case of 
                            an error during parsing.
        """

        kwargs["partnerCode"] = self.partnerCode
        kwargs["customerReference"] = f'{self.partnerCode}-{kwargs.get("customerReference")}'
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/personal/account/request"

        json_payload = json.dumps(kwargs)

        print(f"""
            json_payload: {json_payload}
            \n\n\n
            """)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        print("encrypted_payload", encrypted_payload, "\n\n")

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/personal/123456/request.",
                "instance" : "/virtualaccountservice/rest/api/create/personal/123456/request",
                "properties" : null
            }
        """

        """
        SAMPLE RESPONSE
            {'responseCode': 200, 'processorStatusCode': '00', 'responseMessage': 'Approved or completed successfully', 'responseTime': '2025-02-10 20:12:20', 'data': {'accountNumber': '**********', 'accountName': 'JOSEPH CHINEDU LIBERTYPAY', 'customerReference': 'LIBERTYPAY-6AA40L9CQ94ZYQE', 'accountId': '1338588382241587200'}}
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_personal_account_by_bvn(self, *args, **kwargs):
        if kwargs.get("is_returning_customer") is True:
            url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/personal/account/byBVN"
        else:
            url = f"{self.base_url}/virtualaccountservice/rest/api/create/personal/account/byBVN"

        kwargs["partnerCode"] = self.partnerCode
        kwargs["previousAccountNumber"] = kwargs["previous_account_number"]

        json_payload = json.dumps(kwargs)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        # print("encrypted_payload", encrypted_payload, "\n\n")

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/personal/123456/byBVN.",
                "instance" : "/virtualaccountservice/rest/api/create/personal/123456/byBVN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_personal_account_by_nin(self, **kwargs):
       
        kwargs["partnerCode"] = self.partnerCode
        kwargs["previousAccountNumber"] = kwargs["previous_account_number"]
        
        if kwargs.get("is_returning_customer") == True:
            url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/personal/account/byNIN"
        else:
            url = f"{self.base_url}/virtualaccountservice/rest/api/create/personal/account/byNIN"


        json_payload = json.dumps(kwargs)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        # print("encrypted_payload", encrypted_payload, "\n\n")

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/personal/123456/request.",
                "instance" : "/virtualaccountservice/rest/api/create/personal/123456/request",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def additional_personal_account_by_bvn(self, validated_data):
        customer_reference = validated_data.get("customerReference")
        validated_data["partner_code"] = self.partnerCode
        # url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/personal/account/byBVN"
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/personal/{customer_reference}/byBVN"

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            # the customer reference when creating the previous account number
            "bvn": validated_data.get("bvn"),
            "previousAccountNumber": validated_data.get("previous_account_number"),
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/additional/personal/Libe32456879rty/byBVN.",
                "instance" : "/virtualaccountservice/rest/api/create/additional/personal/Libe32456879rty/byBVN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def additional_personal_account_by_nin(self, validated_data):
        customer_reference = validated_data.get("customerReference")
        validated_data["partner_code"] = self.partnerCode
        # url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/personal/account/byNIN"
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/personal/{customer_reference}/byNIN"

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            # the customer reference when creating the previous account number
            "nin": validated_data.get("bvn"),
            "previousAccountNumber": validated_data.get("previous_account_number"),
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/additional/personal/Libe32456879rty/byNIN.",
                "instance" : "/virtualaccountservice/rest/api/create/additional/personal/Libe32456879rty/byNIN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_corporate_account(self, validated_data):
        customer_reference = validated_data.get("customer_reference")
        validated_data["partner_code"] = self.partnerCode
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/corporate/{customer_reference}/request"

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            "businessName": validated_data.get("business_name"),
            "businessAddress": validated_data.get("business_address"),
            "country": validated_data.get("country"),
            "state": validated_data.get("state"),
            "localGovt": validated_data.get("local_govt"),
            "contactEmail": validated_data.get("contact_email"),
            "contactPhoneNumber": validated_data.get("contact_phone_number"),
            "businessType": validated_data.get("business_type"),  # Business_Name,Sole_Proprietorship,Partnership,Public_Limited_Company,Limited_Liability,Corporations,Cooperative
            "city": validated_data.get("city"),
            "rcNumber": validated_data.get("rc_number"),
            "incorporationDate": validated_data.get("incorporation_date"),
            "bvn": validated_data.get("bvn"),  # optional information either bvn or nin
            "nin": validated_data.get("nin"),  # optional information bvn or nin
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/corporate/Libe32456879rty/request.",
                "instance" : "/virtualaccountservice/rest/api/create/corporate/Libe32456879rty/request",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_corporate_account_by_bvn(self, validated_data):
        customer_reference = validated_data.get("customer_reference")
        validated_data["partner_code"] = self.partnerCode
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/corporate/{customer_reference}/byBVN"

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            "rcNumber": validated_data.get("rc_number"),
            "incorporationDate": validated_data.get("incorporation_date"),  # "2020-10-11"
            "bvn": validated_data.get("bvn"),  # optional information either bvn or nin
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/corporate/Libe32456879rty/byBVN.",
                "instance" : "/virtualaccountservice/rest/api/create/corporate/Libe32456879rty/byBVN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def create_corporate_account_by_nin(self, validated_data):
        customer_reference = validated_data.get("customer_reference")
        validated_data["partner_code"] = self.partnerCode
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/corporate/{customer_reference}/byNIN"

        payload = {
           "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            "rcNumber": validated_data.get("rc_number"),
            "incorporationDate": validated_data.get("incorporation_date"),  # "2020-10-11"
            "nin": validated_data.get("nin"),  # optional information either bvn or nin
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/corporate/Libe32456879rty/byNIN.",
                "instance" : "/virtualaccountservice/rest/api/create/corporate/Libe32456879rty/byNIN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def additional_corporate_account_by_bvn(self, validated_data):
        customer_reference = validated_data.get("customer_reference")
        validated_data["partner_code"] = self.partnerCode
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/corporate/{customer_reference}/byBVN"

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            "rcNumber": validated_data.get("rc_number"),
            "bvn": validated_data.get("bvn"),
            "previousAccountNumber": validated_data.get("previous_account_number"),
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/additional/corporate/Libe32456879rty/byBVN.",
                "instance" : "/virtualaccountservice/rest/api/create/additional/corporate/Libe32456879rty/byBVN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def additional_corporate_account_by_nin(self, validated_data):
        customer_reference = validated_data.get("customer_reference")
        validated_data["partner_code"] = self.partnerCode
        url = f"{self.base_url}/virtualaccountservice/rest/api/create/additional/corporate/{customer_reference}/byNIN"

        payload = {
            "partnerCode": validated_data.get("partner_code"),
            "customerReference": validated_data.get("customer_reference"),
            "rcNumber": validated_data.get("rc_number"),
            "nin": validated_data.get("nin"),
            "previousAccountNumber": validated_data.get("previous_account_number"),
        }

        json_payload = json.dumps(payload)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/create/additional/corporate/Libe32456879rty/byNIN.",
                "instance" : "/virtualaccountservice/rest/api/create/additional/corporate/Libe32456879rty/byNIN",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def inter_disbursement(self, **kwargs):

        url = f"{self.base_url}/virtualaccountservice/rest/api/inter/bank/disbursement"

        kwargs["disbursementReference"] = f'{self.partnerCode}-{kwargs.get("disbursementReference")}'

        json_payload = json.dumps(kwargs)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/inter/bank/redtfyuhjoyutrytuyr6546789765.",
                "instance" : "/virtualaccountservice/rest/api/inter/bank/redtfyuhjoyutrytuyr6546789765",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def intra_disbursement(self, **kwargs):
        # disbursement_reference = validated_data.get("disbursement_reference")
        # url = f"{self.base_url}/virtualaccountservice/rest/api/intra/bank/{disbursement_reference}"

        url = f"{self.base_url}/virtualaccountservice/rest/api/intra/bank/disbursement"


        kwargs["disbursementReference"] = f'{self.partnerCode}-{kwargs.get("disbursementReference")}'

        json_payload = json.dumps(kwargs)

        encrypted_payload = self.handler.encrypt_data(json_payload)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.handler.decrypt_data(response.text)


        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

        

        # json_payload = json.dumps(payload)

        # encrypted_payload = self.handler.encrypt_data(json_payload)

        # headers = self.get_headers()
        # headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        # response = requests.post(url=url, headers=headers, data=encrypted_payload)

        # decrypted_response = self.handler.decrypt_data(response.text)

        # """
        # 404 RESPONSE
        #     {
        #         "type" : "about:blank",
        #         "title" : "Not Found",
        #         "status" : 404,
        #         "detail" : "No static resource rest/api/intra/bank/redtfyuhjoyutrytuyr6546789765.",
        #         "instance" : "/virtualaccountservice/rest/api/intra/bank/redtfyuhjoyutrytuyr6546789765",
        #         "properties" : null
        #     }
        # """

        # try:
        #     return json.loads(decrypted_response)
        # except Exception as e:
        #     return decrypted_response

    def transaction_status_by_trans_ref(self, trans_ref):
        url = f"{self.base_url}/virtualaccountservice/rest/api/query/transaction/{trans_ref}/status"
        print("URL=============== >", url)

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_bank_list(self):
        url = f"{self.base_url}/virtualaccountservice/rest/api/fetch/banklist"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
        {
            "responseCode" : 200,
            "processorStatusCode" : "00",
            "responseMessage" : "Approved or completed successfully",
            "responseTime" : "2025-02-04 16:54:23",
            "data" : {
                "bankList" : [ 
                    {
                      "name" : "ADH",
                      "bankCode" : "999001",
                      "category" : "3"
                    }, 
                    {
                      "name" : "Access Bank",
                      "bankCode" : "999044",
                      "category" : "2"
                    }, {
                      "name" : "BOSAK",
                      "bankCode" : "999104",
                      "category" : "7"
                    }, {
                      "name" : "Bank ABC",
                      "bankCode" : "000001",
                      "category" : "2"
                    }, {
                      "name" : "Bank XYZ",
                      "bankCode" : "000003",
                      "category" : "2"
                    }, {
                      "name" : "CASHCONNECT MFB",
                      "bankCode" : "090360",
                      "category" : "9"
                    }, {
                      "name" : "Citi Bank",
                      "bankCode" : "999023",
                      "category" : "2"
                    }, {
                      "name" : "Covenant MFB",
                      "bankCode" : "999052",
                      "category" : "7"
                    }, {
                      "name" : "DIAMOND BANK",
                      "bankCode" : "999063",
                      "category" : "2"
                    }, {
                      "name" : "ECOBANK",
                      "bankCode" : "999050",
                      "category" : "2"
                    }, {
                      "name" : "FCMB",
                      "bankCode" : "999214",
                      "category" : "2"
                    }, {
                      "name" : "FETS",
                      "bankCode" : "999003",
                      "category" : "11"
                    }, {
                      "name" : "Fidelity",
                      "bankCode" : "999070",
                      "category" : "2"
                    }, {
                      "name" : "First Bank",
                      "bankCode" : "999011",
                      "category" : "2"
                    }, {
                      "name" : "GTBank",
                      "bankCode" : "999058",
                      "category" : "2"
                    }, {
                      "name" : "Keystone Bank",
                      "bankCode" : "999082",
                      "category" : "2"
                    }, {
                      "name" : "MMO XYZ",
                      "bankCode" : "000002",
                      "category" : "10"
                    }, {
                      "name" : "Merchant Bank XYZ",
                      "bankCode" : "000005",
                      "category" : "2"
                    }, {
                      "name" : "Mutual Benefits",
                      "bankCode" : "999107",
                      "category" : "7"
                    }, {
                      "name" : "NIBSS",
                      "bankCode" : "999999",
                      "category" : "1"
                    }, {
                      "name" : "NOVA",
                      "bankCode" : "999105",
                      "category" : "7"
                    }, {
                      "name" : "NOW NOW",
                      "bankCode" : "999078",
                      "category" : "11"
                    }, {
                      "name" : "NPF",
                      "bankCode" : "999002",
                      "category" : "11"
                    }, {
                      "name" : "PMB X",
                      "bankCode" : "000004",
                      "category" : "6"
                    }, {
                      "name" : "PagaTech",
                      "bankCode" : "999009",
                      "category" : "2"
                    }, {
                      "name" : "Parallex MFB",
                      "bankCode" : "999015",
                      "category" : "7"
                    }, {
                      "name" : "Pse-Test",
                      "bankCode" : "999000",
                      "category" : "2"
                    }, {
                      "name" : "Psuedo",
                      "bankCode" : "999998",
                      "category" : "2"
                    }, {
                      "name" : "STSL DEV",
                      "bankCode" : "999159",
                      "category" : "6"
                    }, {
                      "name" : "Skye Bank",
                      "bankCode" : "999076",
                      "category" : "2"
                    }, {
                      "name" : "Stanbic Ibtc",
                      "bankCode" : "999221",
                      "category" : "2"
                    }, {
                      "name" : "Sterling Bank",
                      "bankCode" : "999232",
                      "category" : "2"
                    }, {
                      "name" : "Teasy",
                      "bankCode" : "999004",
                      "category" : "11"
                    }, {
                      "name" : "Trustbond",
                      "bankCode" : "999018",
                      "category" : "7"
                    }, {
                      "name" : "UBA",
                      "bankCode" : "999033",
                      "category" : "2"
                    }, {
                      "name" : "UNITY BANK",
                      "bankCode" : "999215",
                      "category" : "2"
                    }, {
                      "name" : "VFD MFB",
                      "bankCode" : "999116",
                      "category" : "7"
                    }, {
                      "name" : "WEMA MOBILE",
                      "bankCode" : "999140",
                      "category" : "11"
                    }, {
                      "name" : "Wema Bank",
                      "bankCode" : "999035",
                      "category" : "2"
                    }, {
                      "name" : "Zenith Bank",
                      "bankCode" : "999057",
                      "category" : "2"
                    } 
                ]
            }
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def account_name_lookup_intra(self, account_number):
        url = f"{self.base_url}/virtualaccountservice/rest/api/account/name/lookup/{account_number}"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def account_name_lookup_inter(self, bank_code, account_number):
        url = f"{self.base_url}/virtualaccountservice/rest/api/account/name/inter/lookup/{bank_code}/{account_number}"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def collection_pool_balance(self):
        url = f"{self.base_url}/accountservice/rest/api/collection/pool/balance"

        headers = self.get_headers()
        headers["Authorization"] = f"Bearer {self.get_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.handler.decrypt_data(response.text)


        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def book_loan_request(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/loan/booking"

        payload = {
            "customerId": str(kwargs.get("customer_id")),
            "customerFirstName": kwargs.get("customer_first_name"),
            "customerMiddleName": kwargs.get("customer_middle_name"),
            "customerLastName": kwargs.get("customer_last_name"),
            "customerEmailAddress": kwargs.get("customer_email_address"),
            "customerPhoneNumber": kwargs.get("customer_phone_number"),
            "customerAddress": kwargs.get("customer_address"),
            "customerGender": kwargs.get("customer_gender"), #//Male or Female
            "customerPassportPhoto": kwargs.get("customer_passport_photo"), #//optional
            "customerCountry": kwargs.get("customer_country"),
            "customerState": kwargs.get("customer_state"),
            "customerCity": kwargs.get("customer_city"),
            "customerBVN": kwargs.get("customer_bvn"), #//optional
            "customerNIN": kwargs.get("customer_nin"), #//optional
            "customerDateOfBirth": kwargs.get("customer_date_of_birth"),
            "productCode": config("CASH_CONNECT_LOAN_PRODUCT_CODE"),
            "loanRequestAmount": float(kwargs.get("loan_request_amount")),
            "interestRate": int(kwargs.get("interest_rate")),
            "tenureFrequencyCode": kwargs.get("tenure_frequency_code"),
            "tenure": int(kwargs.get("tenure")),
            "repaymentTypeCode": kwargs.get("repayment_type_code"),
            "startDate": kwargs.get("start_date"),
            "loanSectorCode": kwargs.get("loan_sector_code"),
            "collateralTypeCode": kwargs.get("collateral_type_code"),
            "collateralValue": kwargs.get("collateral_value"),
            "collateralDescription":  kwargs.get("collateral_description"),
            "loanSourceCode": kwargs.get("loan_source_code"),
            "loanPurpose": kwargs.get("loan_purpose"),
            "calculationMethodCode": kwargs.get("calculation_method_code"),
            "lendingModelCode": kwargs.get("lending_model_code"),
            "requestReferenceID": kwargs.get("request_reference_id"),
            "settlementAccount": "" #// optional if the customer already have a cashconnect account
        }
        json_payload = json.dumps(payload)

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload=payload,
            response_data=None,
            type_of_request=enums.CashConnectRquestTypes.BOOK_LOAN,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        encrypted_payload = self.loan_handler.encrypt_data(json_payload)
        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        try:
            decrypted_response = self.loan_handler.decrypt_data(response.text)
        except Exception as e:
            decrypted_response = response.text

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_loan_types(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountingservice/rest/api/loanops/fetch/loan/products"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.FETCH_LOAN_TYPES,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.get(url=url, headers=headers)
        decrypted_response = self.handler.loan_decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_repayment_types(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountingservice/rest/api/loanops/fetch/loan/products"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.FETCH_LOAN_TYPES,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.get(url=url, headers=headers)
        decrypted_response = self.handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def disburse_loan(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/loan/disbursement"

        payload = {
            "customerId": kwargs.get("customer_id"),
            "loanAccount": kwargs.get("loan_account"),
            "loanBookingRequestReferenceID": kwargs.get("loan_booking_request_reference_id")
        }

        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload=payload,
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.LOAN_DISBURSEMENT,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)


        decrypted_response = self.loan_handler.decrypt_data(response.text)


        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_repayment_schedule(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        # url = f"{self.base_url}/accountingservice/rest/api/loanops/fetch/{kwargs.get("loanaccount")}/repayment/scheduled"
        url = f"{self.base_url}/accountservice/rest/api/loan/fetch/{kwargs.get('loan_account_number')}/repayment/scheduled"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.FETCH_LOAN_SCHEDULE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.get(url=url, headers=headers)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_customer_loans(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        # url = f"{self.base_url}/accountingservice/rest/api/loanops/fetch/customer/{kwargs.get("loanaccount")}/loans"
        url = f"{self.base_url}/accountservice/rest/api/loan/fetch/customer/active/{kwargs.get('customer_id')}/loans"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.FETCH_LOAN_SCHEDULE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.get(url=url, headers=headers)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_runtime_params(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/fetch/account/runtime/utility/data"
        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.FETCH_RUNTIME_PARAMS,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        payload = {
            "partnerCode":"LIBERTYPAYLOAN",
            "runtimeParameters":["GENDER_LIST","TITLE_LIST","SECTOR_LIST",
            "ACCOUNT_OFFICER_LIST","LENDING_MODEL_LIST","LOAN_SOURCE_LIST","COLLATERAL_TYPE_LIST",
            "FREQUENCY_LIST","REPAYMENT_TYPE_LIST","CALCULATED_METHOD_LIST"]
        }

        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        response = requests.post(url=url, headers=headers, data=encrypted_payload)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def fetch_settlement_account_balance(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/loan/fetch/settlement/balance"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.CHECK_BALANCE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        payload = {}

        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        response = requests.get(url=url, headers=headers, data=encrypted_payload)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response
    
    def fetch_loan_details_by_account_number(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/loan/fetch/loan/details/{kwargs.get('loan_account_number')}"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.CHECK_BALANCE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        payload = {}

        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        response = requests.get(url=url, headers=headers, data=encrypted_payload)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response
    
    def fetch_customer_loan_settlement_account(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/loan/fetch/customer/{kwargs.get('customer_id')}/settlements/account"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.CHECK_BALANCE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        payload = {}

        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        response = requests.get(url=url, headers=headers, data=encrypted_payload)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response
    
    def make_account_enquiry(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/transactionservice/rest/api/account/name/lookup/{kwargs.get('account_number')}/intra"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.CHECK_BALANCE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        payload = {}
        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        response = requests.get(url=url, headers=headers, data=encrypted_payload)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response
    
    def fetch_account_details(self, **kwargs):
        from cash_connect.models import CashConnectRequestLogs
        from helpers import enums

        url = f"{self.base_url}/accountservice/rest/api/cba/account/name/enquiry/{kwargs.get('account_number')}"

        cash_conn_request_log = CashConnectRequestLogs.objects.create(
            payload={},
            response_data={},
            type_of_request=enums.CashConnectRquestTypes.CHECK_BALANCE,
        )

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        payload = {}
        json_payload = json.dumps(payload)
        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        response = requests.get(url=url, headers=headers, data=encrypted_payload)
        decrypted_response = self.loan_handler.decrypt_data(response.text)

        cash_conn_request_log.response_data = decrypted_response
        cash_conn_request_log.save()

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response


    def loan_inter_disbursement(self, **kwargs):

        url = f"{self.base_url}/virtualaccountservice/rest/api/inter/bank/disbursement"

        kwargs["disbursementReference"] = f'{self.loan_partner_code}-{kwargs.get("disbursementReference")}'

        json_payload = json.dumps(kwargs)

        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.loan_handler.decrypt_data(response.text)

        """
        404 RESPONSE
            {
                "type" : "about:blank",
                "title" : "Not Found",
                "status" : 404,
                "detail" : "No static resource rest/api/inter/bank/redtfyuhjoyutrytuyr6546789765.",
                "instance" : "/virtualaccountservice/rest/api/inter/bank/redtfyuhjoyutrytuyr6546789765",
                "properties" : null
            }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response
        
    def loan_intra_disbursement(self, **kwargs):
        # disbursement_reference = validated_data.get("disbursement_reference")
        # url = f"{self.base_url}/virtualaccountservice/rest/api/intra/bank/{disbursement_reference}"

        url = f"{self.base_url}/virtualaccountservice/rest/api/intra/bank/disbursement"


        kwargs["disbursementReference"] = f'{self.loan_partner_code}-{kwargs.get("disbursementReference")}'

        json_payload = json.dumps(kwargs)

        encrypted_payload = self.loan_handler.encrypt_data(json_payload)

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.post(url=url, headers=headers, data=encrypted_payload)

        decrypted_response = self.loan_handler.decrypt_data(response.text)


        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response

    def loan_transaction_status_by_trans_ref(self, trans_ref):
        url = f"{self.base_url}/virtualaccountservice/rest/api/query/transaction/{trans_ref}/status"
        print("URL=============== >", url)

        headers = self.get_loan_headers()
        headers["Authorization"] = f"Bearer {self.get_loan_auth_token()}"

        response = requests.get(url=url, headers=headers, data={})

        decrypted_response = self.loan_handler.decrypt_data(response.text)
        print("DECRYPTED_RESPONSE =============>", decrypted_response)

        """
        404 RESPONSE
        {
            "type" : "about:blank",
            "title" : "Not Found",
            "status" : 404,
            "detail" : "No static resource rest/api/account/status/12345/status.",
            "instance" : "/accountservice/rest/api/account/status/12345/status",
            "properties" : null
        }
        """

        try:
            return json.loads(decrypted_response)
        except Exception as e:
            return decrypted_response
