from accounts.models import AccountDetail, TransferMoneyRequest
from cash_connect.helpers.cash_connect_base_file import STSLEncryptionHandlerFactory
from cash_connect.helpers.cash_connect_call_backs import CashConnectHelperClass
from cash_connect.models import (
    CashConnectCustomers,
    CashConnectDisbursementWalletFundingFromSettlementWalletLogs,
    CashConnectRequestLogs,
    CashConnectVirtualAccount,
    CashConnectWebHookData,
)
from helpers import enums
from helpers.reusable import make_request
from user_profiles.models import Company
from helpers.enums import CashConnectRquestTypes
from django.conf import settings
import json
from decouple import config
import pytz
from datetime import datetime


class CashConnectRequestManagers:
    @staticmethod
    def create_virtual_account(**kwargs):

        """
        Retrieves a bank account detail instance for a company with error handling.

        This method is used to fetch account details before creating virtual accounts
        or performing other banking operations. It's specifically designed to work with
        both WEMA and CASH_CONNECT provider types.

        Args:
            company (Company): Company model instance to fetch account details for
            provider (BankProvider, optional): Bank provider enum, defaults to WEMA.
                Can also be CASH_CONNECT for virtual account operations
            account_type (AccountType, optional): Type of account, defaults to MAIN

        Returns:
            tuple: A tuple containing:
                - bool: Success status
                    - True if account was found
                    - False if account wasn't found or multiple accounts exist
                - Union[AccountDetail, str]: Either:
                    - AccountDetail instance on success
                    - Error message string on failure ("Account not found" or
                    "Multiple accounts found")

        Example:
            >>> company = Company.objects.get(id=1)
            >>> # For CashConnect virtual account creation
            >>> success, account = AccountDetail.get_account_detail_instance(
            ...     company=company,
            ...     provider=enums.ServiceProvider.CASH_CONNECT
            ... )
            >>> if not success:
            >>>     return False, "Company account details not found"

        Notes:
            - Used as a prerequisite check in CashConnectRequestManagers.create_virtual_account
            - Returns (False, "Account not found") if no matching account exists
            - Returns (False, "Multiple accounts found") if data inconsistency is detected
            - The success boolean in the return tuple allows for easy conditional handling
            in calling functions
        """

        phone_number = kwargs.get("phone_number")

        if phone_number == None:
            return False, "Phone Number is required"

        phone_number = str(phone_number).strip()
        phone_number = f"234{phone_number[-10:]}"

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        creating_account_for_company = kwargs.get("creating_account_for_company", False)

        if creating_account_for_company == False:
            # get account detail for the company
            (
                company_account_details_status,
                company_account_details,
            ) = AccountDetail.get_account_detail_instance(
                company=company_instance,
                provider=enums.ServiceProvider.CASH_CONNECT,
            )

            print("company_account_details_status", company_account_details_status)

            if company_account_details_status == False:
                return False, company_account_details

        try:
            customer_instance = CashConnectCustomers.objects.get(
                phone_number=phone_number
            )
        except CashConnectCustomers.DoesNotExist:
            customer_instance = CashConnectCustomers.objects.create(
                company=company_instance,
                phone_number=phone_number,
                first_name=kwargs.get("first_name"),
                title=kwargs.get("title"),
                middle_name=kwargs.get("middle_name"),
                last_name=kwargs.get("last_name"),
                email=kwargs.get("email"),
                bvn=kwargs.get("bvn"),
                nin=kwargs.get("nin"),
                dob=kwargs.get("dob"),
                address=kwargs.get("address"),
                country=kwargs.get("country"),
                state=kwargs.get("state"),
                city=kwargs.get("city"),
                local_govt=kwargs.get("local_govt"),
                gender=kwargs.get("gender"),
                marital_status=kwargs.get("marital_status"),
            )

        # check this user has virtual account created
        try:
            cash_connect_virtual_account_instance = (
                CashConnectVirtualAccount.objects.get(
                    customer_reference=customer_instance.customer_reference,
                    type_of_account=enums.CashConnectTypeOfVirtualAccount.VIRTUAL,
                    account_number__isnull=False,
                )
            )
            return True, {
                "account_name": cash_connect_virtual_account_instance.account_name,
                "account_number": cash_connect_virtual_account_instance.account_number,
                "account_id": cash_connect_virtual_account_instance.account_id,
                "bank_code": cash_connect_virtual_account_instance.bank_code,
                "bank_name": "Cash Connect MFB",
            }
        except CashConnectVirtualAccount.DoesNotExist:
            pass

        virtual_account_payload = {
            "customerReference": customer_instance.customer_reference,
            "title": kwargs.get("title"),
            "firstName": kwargs.get("first_name"),
            "middleName": kwargs.get("middle_name"),
            "lastName": kwargs.get("last_name"),
            "emailAddress": kwargs.get("email_address"),
            "phoneNumber": kwargs.get("phone_number"),
            "bvn": kwargs.get("bvn"),
            "nin": kwargs.get("nin"),
            "dateOfBirth": kwargs.get("dob"),
            "address": kwargs.get("address"),
            "country": kwargs.get("country"),
            "state": kwargs.get("state"),
            "city": kwargs.get("city"),
            "localGovt": kwargs.get("local_govt"),
            "maritalStatus": str(kwargs.get("marital_status")).capitalize(),
            "gender": str(kwargs.get("gender")).capitalize(),
        }

        request_response = CashConnectRequestLogs.make_request(
            type_of_request=CashConnectRquestTypes.VIRTUAL_ACCOUNT,
            **virtual_account_payload,
        )

        if isinstance(request_response, dict):
            if (
                request_response.get("responseCode") == 200
                and request_response.get("processorStatusCode") == "00"
            ):
                CashConnectVirtualAccount.objects.create(
                    company=company_instance,
                    customer_reference=customer_instance.customer_reference,
                    account_name=request_response.get("data", {}).get("accountName"),
                    account_number=request_response.get("data", {}).get(
                        "accountNumber"
                    ),
                    account_id=request_response.get("data", {}).get("accountId"),
                    bank_code=request_response.get("data", {}).get("bankCode"),
                    bank_name=request_response.get("data", {}).get("bankName"),
                    type_of_account=enums.CashConnectTypeOfVirtualAccount.VIRTUAL,
                )

                res_data = request_response.get("data")

                return True, {
                    "account_name": res_data.get("accountName"),
                    "account_number": res_data.get("accountNumber"),
                    "account_id": res_data.get("accountId"),
                    "bank_code": res_data.get("bankCode"),
                    "bank_name": "Cash Connect MFB",
                }
            else:
                return False, request_response
        else:
            return False, request_response

    @staticmethod
    def create_instant_account(**kwargs):

        phone_number = kwargs.get("phone_number")

        if phone_number == None:
            return False, "Phone Number is required"

        phone_number = str(phone_number).strip()
        phone_number = f"234{phone_number[-10:]}"

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        try:
            customer_instance = CashConnectCustomers.objects.get(
                phone_number=phone_number
            )
        except CashConnectCustomers.DoesNotExist:
            customer_instance = CashConnectCustomers.objects.create(
                company=company_instance,
                phone_number=phone_number,
                first_name=kwargs.get("first_name"),
                last_name=kwargs.get("last_name"),
            )

        instant_dynamic_virtual_account_payload = {
            "customerId": customer_instance.customer_reference,
            "accountName": f"{kwargs.get('first_name')} {kwargs.get('last_name')}",
            "expectedCollectionAmount": kwargs.get("expected_collection_amount"),
            "transactionReference": kwargs.get("transaction_reference"),
            "narration": kwargs.get("narration"),
        }

        cash_connect_virtual_account_instance = (
            CashConnectVirtualAccount.objects.create(
                customer_reference=customer_instance.customer_reference,
                type_of_account=enums.CashConnectTypeOfVirtualAccount.INSTANT,
                account_name=f"{kwargs.get('first_name')} {kwargs.get('last_name')}",
                transaction_reference=kwargs.get("transaction_reference"),
                narration=kwargs.get("narration"),
                company=company_instance,
            )
        )

        request_response = CashConnectRequestLogs.make_request(
            type_of_request=CashConnectRquestTypes.INSTANT_VIRTUAL_ACCOUNT,
            **instant_dynamic_virtual_account_payload,
        )
        print("request_response", request_response)

    @staticmethod
    def handle_inter_bank_transfer(**kwargs):

        if kwargs.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": kwargs,
                "account_balance": 0.0,
            }

            return True, data
        # handle account name lookup
        bank_code = kwargs.get("beneficiary_bank_code")
        account_number = kwargs.get("beneficiary_account_number")
        print("ACCOUNT LOOKUP START")
        account_name_lookup_response = (
            CashConnectRequestManagers.account_name_lookup_inter(
                bank_code, account_number
            )
        )
        print("account_name_lookup_response", account_name_lookup_response)
        if not isinstance(account_name_lookup_response, dict):
            return False, account_name_lookup_response

        if account_name_lookup_response.get("responseCode") != 200:
            return False, account_name_lookup_response

        kwargs["beneficiary_account_name"] = account_name_lookup_response.get(
            "data", {}
        ).get("accountName")
        # kwargs["type_of_request"] = enums.CashConnectRquestTypes.INTRA_DISBURSEMENT

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        transfer_request = TransferMoneyRequest.register_cash_connect_transfer_request(
            company=company_instance,
            **kwargs,
        )

        return True, transfer_request

    @staticmethod
    def handle_intra_bank_transfer(**kwargs):

        if kwargs.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": kwargs,
                "account_balance": 0.0,
            }

            return True, data

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        kwargs[
            "type_of_request"
        ] = enums.CashConnectRquestTypes.INTRA_DISBURSEMENT_DISBURSEMENT

        transfer_request = TransferMoneyRequest.register_cash_connect_transfer_request(
            company=company_instance,
            **kwargs,
        )

        return True, transfer_request

    @staticmethod
    def handle_disbursement_verification(transaction_reference, **kwargs):
        service_partner = kwargs.get("service_partner")

        kwargs = {"transaction_reference": transaction_reference}

        if service_partner == "LOANS":
            request_response = CashConnectRequestLogs.make_loan_request(
                type_of_request=CashConnectRquestTypes.DISBURSEMENT_VERIFICATION,
                **kwargs,
            )
        else:
            request_response = CashConnectRequestLogs.make_request(
                type_of_request=CashConnectRquestTypes.DISBURSEMENT_VERIFICATION,
                **kwargs,
            )

        return request_response

    @staticmethod
    def fetch_bank_list():

        request_response = res = CashConnectHelperClass().fetch_bank_list()
        return request_response

    @staticmethod
    def account_name_lookup_inter(bank_code, account_number):
        request_response = CashConnectHelperClass().account_name_lookup_inter(
            bank_code, account_number
        )
        _payload = {
            "bank_code": bank_code,
            "account_number": account_number,
        }
        CashConnectRequestLogs.objects.create(
            payload=json.dumps(_payload),
            response_data=str(request_response),
            type_of_request="ACCOUNT_NAME_LOCKUP",
        )
        return request_response

    @staticmethod
    def handle_webhook_notification(data):
        handler = STSLEncryptionHandlerFactory()

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        decrypted_data = handler.decrypt_data(data)

        webhook_instance = CashConnectWebHookData.create_record(decrypted_data)
        if isinstance(webhook_instance, CashConnectWebHookData):
            # fund company float account
            # get the company that owns the account
            try:
                company_instance = CashConnectVirtualAccount.objects.get(
                    account_number=webhook_instance.beneficiary_account_number
                )
                company_instance = company_instance.company

            except CashConnectVirtualAccount.DoesNotExist:
                return False, "Company not found"

            fund_response = AccountDetail.fund_account(
                company=company_instance,
                account_type=enums.AccountType.MAIN,
                amount=webhook_instance.transaction_amount,
                charges=webhook_instance.transaction_fee,
                provider=enums.ServiceProvider.CASH_CONNECT,
            )

            print("fund_response", fund_response)

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Hook {settings.CALLBACK_BEARER}",
            }

            # send webhook data to product
            # wehbook_payload = {
            #     "type_of_webhook": "DISBURSEMENT_WEBHOOK",
            #     "transaction_reference": webhook_instance.transaction_reference,
            #     "session_id": webhook_instance.session_id,
            #     "account_number": webhook_instance.beneficiary_account_number,
            #     "bank_name": webhook_instance.beneficiary_bank_name,
            #     "transaction_amount": webhook_instance.transaction_amount,
            #     "transaction_value_amount": webhook_instance.transaction_value_amount,
            #     "transaction_fee": webhook_instance.transaction_fee,
            #     "narration": None,
            #     "status": enums.TransactionStatus.SUCCESSFUL,
            #     "reversed": False,
            # }

            wehbook_payload = json.dumps(
                {
                    "one_time": True,
                    "request_reference": webhook_instance.transaction_reference,
                    "company": company_instance.name,
                    "recipient_account_name": webhook_instance.beneficiary_account_name,
                    "recipient_account_number": webhook_instance.beneficiary_account_number,
                    "amount": float(webhook_instance.transaction_amount),
                    "fee": float(webhook_instance.transaction_fee),
                    "amount_payable": float(webhook_instance.transaction_value_amount),
                    "reference": str(webhook_instance.transaction_reference),
                    "transaction_type": enums.TransactionType.CREDIT,
                    "payer_account_name": webhook_instance.source_account_name,
                    "payer_account_number": webhook_instance.source_account_number,
                    "payer_bank_code": webhook_instance.source_bank_code,
                    "paid_at": str(webhook_instance.created_at),
                    "narration": webhook_instance.narration,
                    "session_id": webhook_instance.session_id,
                    "transaction_reference": webhook_instance.transaction_reference,
                    "settlement_status": True,
                    "currency": webhook_instance.currency,
                    "provider": "CASH CONNECT",
                }
            )
            company_url = company_instance.cash_connect_callback_url
            if company_url != None:

                response = make_request(
                    "POST", dict(url=company_url, headers=headers, data=wehbook_payload)
                )
                if response.get("status_code") == 200:
                    webhook_instance.event_sent = True
                    webhook_instance.company_event_response = json.dumps(
                        response.get("data")
                    )
                else:
                    webhook_instance.company_event_response = json.dumps(
                        response.get("data")
                    )

                webhook_instance.event_send_count += 1
                webhook_instance.event_updated_at = TODAY
                webhook_instance.save()
                return response

    def create_virtual_account_with_id(self, **kwargs):

        phone_number = kwargs.get("phone_number")

        if phone_number == None:
            return False, "Phone Number is required"

        phone_number = str(phone_number).strip()
        phone_number = f"234{phone_number[-10:]}"

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        # get account detail for the company
        (
            company_account_details_status,
            company_account_details,
        ) = AccountDetail.get_account_detail_instance(
            company=company_instance,
            provider=enums.ServiceProvider.CASH_CONNECT,
        )

        if company_account_details_status == False:
            return False, company_account_details

        try:
            customer_instance = CashConnectCustomers.objects.get(
                phone_number=phone_number
            )
        except CashConnectCustomers.DoesNotExist:
            customer_instance = CashConnectCustomers.objects.create(
                company=company_instance,
                phone_number=phone_number,
                first_name=kwargs.get("first_name"),
                title=kwargs.get("title"),
                middle_name=kwargs.get("middle_name"),
                last_name=kwargs.get("last_name"),
                email=kwargs.get("email"),
                bvn=kwargs.get("bvn"),
                nin=kwargs.get("nin"),
                dob=kwargs.get("dob"),
                address=kwargs.get("address"),
                country=kwargs.get("country"),
                state=kwargs.get("state"),
                city=kwargs.get("city"),
                local_govt=kwargs.get("local_govt"),
                gender=kwargs.get("gender"),
                marital_status=kwargs.get("marital_status"),
            )

        user_virtual_account_queryset = CashConnectVirtualAccount.objects.filter(
            customer_reference=customer_instance.customer_reference,
            type_of_account=enums.CashConnectTypeOfVirtualAccount.VIRTUAL,
            account_number__isnull=False,
        )
        if not user_virtual_account_queryset.exists():
            is_returning_customer = False
            previous_account_number = ""
        else:
            virtual_account_instance = user_virtual_account_queryset.last()
            is_returning_customer = True
            previous_account_number = (
                virtual_account_instance.account_number
            )  # get most recent account created

        if kwargs.get("type_of_id") == enums.TypeOfId.BVN:
            virtual_account_payload = {
                "bvn": kwargs.get("id_number"),
                "dateOfBirth": str(kwargs.get("dob")),
                "customerReference": f'{config("CASH_CONNECT_PARTNER_CODE")}-{customer_instance.customer_reference}',
            }

            type_of_request = CashConnectRquestTypes.VIRTUAL_ACCOUNT_BY_BVN

        elif kwargs.get("type_of_id") == enums.TypeOfId.NIN:
            virtual_account_payload = {
                "nin": kwargs.get("id_number"),
                "dateOfBirth": str(kwargs.get("dob")),
                "customerReference": f'{config("CASH_CONNECT_PARTNER_CODE")}-{customer_instance.customer_reference}',
            }
            type_of_request = CashConnectRquestTypes.VIRTUAL_ACCOUNT_BY_NIN

        else:
            return False, "Invalid ID type"

        request_response = CashConnectRequestLogs.make_request(
            type_of_request=type_of_request,
            is_returning_customer=is_returning_customer,
            previous_account_number=previous_account_number,
            **virtual_account_payload,
        )

        if isinstance(request_response, dict):
            if (
                request_response.get("responseCode") == 200
                and request_response.get("processorStatusCode") == "00"
            ):
                CashConnectVirtualAccount.objects.create(
                    company=company_instance,
                    customer_reference=customer_instance.customer_reference,
                    account_name=request_response.get("data", {}).get("accountName"),
                    account_number=request_response.get("data", {}).get(
                        "accountNumber"
                    ),
                    account_id=request_response.get("data", {}).get("accountId"),
                    bank_code=request_response.get("data", {}).get("bankCode"),
                    bank_name=request_response.get("data", {}).get("bankName"),
                    type_of_account=enums.CashConnectTypeOfVirtualAccount.VIRTUAL,
                )

                res_data = request_response.get("data")

                return True, {
                    "account_name": res_data.get("accountName"),
                    "account_number": res_data.get("accountNumber"),
                    "account_id": res_data.get("accountId"),
                    "bank_code": res_data.get("bankCode"),
                    "bank_name": "Cash Connect MFB",
                }
            else:
                return False, request_response
        else:
            return False, request_response

    @staticmethod
    def fetch_cash_connect_account_balance():
        request_response = CashConnectHelperClass().collection_pool_balance()
        return request_response

    @staticmethod
    def disbursement_wallet_funding(**kwargs):

        instance = (
            CashConnectDisbursementWalletFundingFromSettlementWalletLogs.objects.create(
                transaction_reference=kwargs.get("disbursementReference"),
                amount=kwargs.get("amount"),
                payload=json.dumps(kwargs),
            )
        )

        res = CashConnectHelperClass().intra_disbursement(**kwargs)

        instance.response_data = str(res)
        instance.save()

        return res

    @staticmethod
    def book_loan(**kwargs):
        try:
            request_response = CashConnectHelperClass().book_loan_request(**kwargs)
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def fetch_runtime_parameters(**kwargs):
        try:
            request_response = CashConnectHelperClass().fetch_runtime_params(**kwargs)
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def disburse_loan(**kwargs):
        try:
            request_response = CashConnectHelperClass().disburse_loan(**kwargs)
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def check_settlement_account_balance(**kwargs):

        try:
            request_response = (
                CashConnectHelperClass().fetch_settlement_account_balance(**kwargs)
            )
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def fetch_loan_repayment_schedule(**kwargs):

        try:
            request_response = CashConnectHelperClass().fetch_repayment_schedule(
                **kwargs
            )
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def fetch_customer_loans(**kwargs):

        try:
            request_response = CashConnectHelperClass().fetch_customer_loans(**kwargs)
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def fetch_loan_details_by_account_number(**kwargs):

        try:
            request_response = (
                CashConnectHelperClass().fetch_loan_details_by_account_number(**kwargs)
            )
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def fetch_customer_loan_settlement_account(**kwargs):

        try:
            request_response = (
                CashConnectHelperClass().fetch_customer_loan_settlement_account(
                    **kwargs
                )
            )
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def make_account_enquiry(**kwargs):

        try:
            request_response = CashConnectHelperClass().make_account_enquiry(**kwargs)
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def fetch_account_details(**kwargs):

        try:
            request_response = CashConnectHelperClass().fetch_account_details(**kwargs)
        except Exception as e:
            request_response = {"error": str(e)}
        return request_response

    @staticmethod
    def handle_loan_intra_bank_transfer(**kwargs):
        kwargs["service_partner"] = "LOANS"

        if kwargs.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": kwargs,
                "account_balance": 0.0,
            }

            return True, data

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        kwargs[
            "type_of_request"
        ] = enums.CashConnectRquestTypes.INTRA_DISBURSEMENT_DISBURSEMENT

        transfer_request = TransferMoneyRequest.register_cash_connect_transfer_request(
            company=company_instance,
            **kwargs,
        )

        return True, transfer_request

    @staticmethod
    def handle_loan_inter_bank_transfer(**kwargs):
        kwargs["service_partner"] = "LOANS"

        if kwargs.get("mode") == enums.APIMode.TEST:
            data = {
                "status": True,
                "message": "success",
                "transaction": kwargs,
                "account_balance": 0.0,
            }

            return True, data
        # handle account name lookup
        bank_code = kwargs.get("beneficiary_bank_code")
        account_number = kwargs.get("beneficiary_account_number")
        print("ACCOUNT LOOKUP START")
        account_name_lookup_response = (
            CashConnectRequestManagers.account_name_lookup_inter(
                bank_code, account_number
            )
        )
        print("account_name_lookup_response", account_name_lookup_response)
        if not isinstance(account_name_lookup_response, dict):
            return False, account_name_lookup_response

        if account_name_lookup_response.get("responseCode") != 200:
            return False, account_name_lookup_response

        kwargs["beneficiary_account_name"] = account_name_lookup_response.get(
            "data", {}
        ).get("accountName")
        # kwargs["type_of_request"] = enums.CashConnectRquestTypes.INTRA_DISBURSEMENT

        # get customer instance
        company_instance = Company.objects.get(id=kwargs.get("company_id"))

        transfer_request = TransferMoneyRequest.register_cash_connect_transfer_request(
            company=company_instance,
            **kwargs,
        )

        return True, transfer_request

    @staticmethod
    def handle_loan_disbursement_verification(transaction_reference):

        kwargs = {"transaction_reference": transaction_reference}
        service_partner = kwargs.get("service_partner")

        if service_partner == "LOANS":
            request_response = CashConnectRequestLogs.make_loan_request(
                CashConnectRquestTypes.DISBURSEMENT_VERIFICATION, **kwargs
            )
        else:
            request_response = CashConnectRequestLogs.make_request(
                CashConnectRquestTypes.DISBURSEMENT_VERIFICATION, **kwargs
            )
        return request_response

    @staticmethod
    def fetch_bank_list():

        request_response = res = CashConnectHelperClass().fetch_bank_list()
        return request_response
