import base64
import secrets
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.hashes import SHA1
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.keywrap import InvalidUnwrap
from binascii import hexlify, unhexlify
from decouple import config
from cryptography.hazmat.primitives import padding
import redis
from dataclasses import dataclass


class STSLEncryptionHandlerFactory:
    # def __init__(self):
    #     self.key_size = 128  # AES key size in bits
    #     self.key_size_bytes = self.key_size_bits // 8
    #     self.iteration_count = 1000  # PBKDF2 iteration count
    #     self._encryption_salt = None
    #     self._encryption_password = None
    #     self._encryption_iv = None
    #     self._initialize_settings()

    def __init__(self):
        self.key_size_bits = 128  # AES key size in bits
        self.key_size_bytes = self.key_size_bits // 8  # Convert to bytes for PBKDF2 (16 bytes)
        self.iteration_count = 1000
        self._encryption_salt = None
        self._encryption_password = None
        self._encryption_iv = None
        self._initialize_settings()

    def _initialize_settings(self):
        self._encryption_salt = config('CASH_CONNECT_SALT')
        self._encryption_password = config('CASH_CONNECT_PASSWORD2')
        self._encryption_iv = config('CASH_CONNECT_IV')
        # print("salt", self._encryption_salt)
        # print("password", self._encryption_password)
        # print("iv", self._encryption_iv)

    @staticmethod
    def _generate_key(salt, password, iteration_count, key_size_bytes):
        salt_bytes = unhexlify(salt)
        kdf = PBKDF2HMAC(
            algorithm=SHA1(),
            length=key_size_bytes,  # Now passing bytes instead of bits
            salt=salt_bytes,
            iterations=iteration_count,
            backend=default_backend(),
        )
        return kdf.derive(password.encode("utf-8"))

    # def _do_final(self, mode, key, iv, data):
    #     try:
    #         iv_bytes = unhexlify(iv)
    #         cipher = Cipher(algorithms.AES(key),
    #         modes.CBC(iv_bytes), backend=default_backend())
    #         encryptor_or_decryptor = cipher.encryptor() if mode == "encrypt" else cipher.decryptor()
    #         return encryptor_or_decryptor.update(data) + encryptor_or_decryptor.finalize()
    #     except InvalidUnwrap as e:
    #         raise ValueError("Invalid operation during encryption/decryption.") from e

    def _do_final(self, mode, key, iv, data):
        try:
            iv_bytes = unhexlify(iv)
            cipher = Cipher(algorithms.AES(key), modes.CBC(iv_bytes), backend=default_backend())

            if mode == "encrypt":
                padder = padding.PKCS7(algorithms.AES.block_size).padder()
                data = padder.update(data) + padder.finalize()
                encryptor = cipher.encryptor()
                return encryptor.update(data) + encryptor.finalize()
            else:  # decrypt mode
                decryptor = cipher.decryptor()
                decrypted_data = decryptor.update(data) + decryptor.finalize()
                unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
                return unpadder.update(decrypted_data) + unpadder.finalize()
        except InvalidUnwrap as e:
            raise ValueError("Invalid operation during encryption/decryption.") from e

    # def encrypt(self, salt, iv, password, plain_string):
    #     key = self._generate_key(salt, password, self.iteration_count, self.key_size_bytes)
    #     encrypted = self._do_final("encrypt", key, iv, plain_string.encode("utf-8"))
    #     return base64.b64encode(encrypted).decode("utf-8")

    def encrypt(self, salt, iv, password, plain_string):
        key = self._generate_key(salt, password, self.iteration_count, self.key_size_bytes)
        encrypted = self._do_final("encrypt", key, iv, plain_string.encode("utf-8"))
        return base64.b64encode(encrypted).decode("utf-8")

    # def decrypt(self, salt, iv, password, encrypted_string):
    #     key = self._generate_key(salt, password, self.iteration_count, self.key_size_bytes)
    #     encrypted_bytes = base64.b64decode(encrypted_string)
    #     decrypted = self._do_final("decrypt", key, iv, encrypted_bytes)
    #     return decrypted.decode("utf-8")

    def decrypt(self, salt, iv, password, encrypted_string):
        key = self._generate_key(salt, password, self.iteration_count, self.key_size_bytes)
        encrypted_bytes = base64.b64decode(encrypted_string)
        decrypted = self._do_final("decrypt", key, iv, encrypted_bytes)
        return decrypted.decode("utf-8")

    def encrypt_data(self, plain_data):
        return self.encrypt(self._encryption_salt, self._encryption_iv, self._encryption_password, plain_data)

    def decrypt_data(self, encrypted_data):
        return self.decrypt(self._encryption_salt, self._encryption_iv, self._encryption_password, encrypted_data)

    def generate_new_encryption_salt(self):
        return self._random(16)

    def generate_new_encryption_iv(self):
        return self._random(16)

    def generate_encryption_password(self):
        return self._random(12).upper()

    @staticmethod
    def _random(length):
        random_bytes = secrets.token_bytes(length)
        return hexlify(random_bytes).decode("utf-8")


# # Example Usage
# if __name__ == "__main__":
#     handler = STSLEncryptionHandlerFactory()

#     # Encrypt and Decrypt Example
#     plain_text = "Hello, World!"
#     encrypted = handler.encrypt_data(plain_text)
#     print("Encrypted:", encrypted)

#     decrypted = handler.decrypt_data(encrypted)
#     print("Decrypted:", decrypted)


@dataclass
class CashConnectRedisStorage:
    """
    Class for storing data in Redis.
    """

    redis_key: str
    redis_client: object = redis.StrictRedis(host="localhost", port="6379", db=0, decode_responses=True,
                                             encoding="utf-8")

    def set_data(self, data, timeout=60):
        """
        Set data in Redis.
        """
        self.redis_client.set(self.redis_key, data)
        self.redis_client.expire(self.redis_key, timeout)

    def get_data(self):
        """
        Get data from Redis.
        """
        return self.redis_client.get(self.redis_key)

    def delete_data(self):
        """
        Delete data from Redis.
        """
        self.redis_client.delete(self.redis_key)

    def clear_data(self):
        """
        Clear data from Redis.
        """
        self.redis_client.flushdb()





CASH_CONNECT_STATUS_CODES = {
    "9999": {
        "type": "Authentication_Error",
        "message": "Authentication Error"
    },
    "00": {
        "type": "Approved_Or_Completed",
        "message": "Approved or completed successfully"
    },
    "01": {
        "type": "Status_Unknown_Wait_For_Settlement",
        "message": "Status unknown, please wait for settlement report"
    },
    "03": {
        "type": "Invalid_Sender",
        "message": "Invalid Sender"
    },
    "05": {
        "type": "Do_Not_Honor",
        "message": "Do not honor"
    },
    "06": {
        "type": "Dormant_Account",
        "message": "Dormant Account"
    },
    "07": {
        "type": "Invalid_Account",
        "message": "Invalid Account"
    },
    "08": {
        "type": "Account_Name_Mismatch",
        "message": "Account Name Mismatch"
    },
    "09": {
        "type": "Request_Processing_In_Progress",
        "message": "Request processing in progress"
    },
    "12": {
        "type": "Invalid_Transaction",
        "message": "Invalid Transaction"
    },
    "13": {
        "type": "Invalid_Amount",
        "message": "Invalid Amount"
    },
    "14": {
        "type": "Invalid_Batch_Number",
        "message": "Invalid Batch Number"
    },
    "15": {
        "type": "Invalid_Session_OR_Record_ID",
        "message": "Invalid Session or Record ID"
    },
    "16": {
        "type": "Unknown_Bank_Code",
        "message": "Unknown Bank Code"
    },
    "17": {
        "type": "Invalid_Channel",
        "message": "Invalid Channel"
    },
    "18": {
        "type": "Wrong_Method_Call",
        "message": "Wrong Method Call"
    },
    "21": {
        "type": "No_Action_Taken",
        "message": "No action taken"
    },
    "25": {
        "type": "Unable_To_Locate_Record",
        "message": "Unable to locate record"
    },
    "26": {
        "type": "Duplicate_Record",
        "message": "Duplicate record"
    },
    "30": {
        "type": "Format_Error",
        "message": "Format error"
    },
    "34": {
        "type": "Suspected_Fraud",
        "message": "Suspected fraud"
    },
    "35": {
        "type": "Contact_Sending_Bank",
        "message": "Contact sending bank"
    },
    "51": {
        "type": "No_Sufficient_Funds",
        "message": "No sufficient funds"
    },
    "57": {
        "type": "Transaction_not_permitted_to_sender",
        "message": "Transaction not permitted to sender"
    },
    "58": {
        "type": "Transaction_not_permitted_on_channel",
        "message": "Transaction not permitted on channel"
    },
    "61": {
        "type": "Transfer_Limit_Exceeded",
        "message": "Transfer limit Exceeded"
    },
    "63": {
        "type": "Security_Violation",
        "message": "Security violation"
    },
    "65": {
        "type": "Exceeds_Withdrawal_Frequency",
        "message": "Exceeds withdrawal frequency"
    },
    "68": {
        "type": "Response_Received_Too_Late",
        "message": "Response received too late"
    },
    "69": {
        "type": "Unsuccessful_Account_Amount_Block",
        "message": "Unsuccessful Account/Amount block"
    },
    "70": {
        "type": "Unsuccessful_Account_Amount_Unblock",
        "message": "Unsuccessful Account/Amount unblock"
    },
    "71": {
        "type": "Empty_Mandate_Reference_Number",
        "message": "Empty Mandate Reference Number"
    },
    "91": {
        "type": "Beneficiary_Bank_not_available",
        "message": "Beneficiary Bank not available"
    },
    "92": {
        "type": "Routing_Error",
        "message": "Routing error"
    },
    "94": {
        "type": "Duplicate_Transaction",
        "message": "Duplicate transaction"
    },
    "96": {
        "type": "System_Malfunction",
        "message": "System malfunction"
    },
    "97": {
        "type": "Request_Timeout",
        "message": "Timeout waiting for response from destination"
    }
}