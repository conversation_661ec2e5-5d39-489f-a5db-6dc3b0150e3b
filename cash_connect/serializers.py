from rest_framework import serializers
from helpers import enums


class CreateVirtualAccountSerializer(serializers.Serializer):
    title = serializers.ChoiceField(
        enums.UserTitle.choices
    )
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    middle_name = serializers.CharField(
        allow_null=True, allow_blank=True, required=False)
    email_address = serializers.CharField()
    phone_number = serializers.CharField()
    bvn = serializers.CharField(
        allow_null=True, allow_blank=True, required=False)
    nin = serializers.CharField(
        allow_null=True, allow_blank=True, required=False)
    dob = serializers.CharField()
    address = serializers.CharField()
    country = serializers.CharField()
    state = serializers.CharField()
    city = serializers.CharField()
    local_govt = serializers.CharField()
    marital_status = serializers.ChoiceField(
        enums.UserMaritalStatus.choices
    )
    gender = serializers.ChoiceField(
        enums.UserGender.choices
    )


class CreateDynamicInstantVirtualAccountSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()
    expected_collection_amount = serializers.FloatField()
    narration = serializers.CharField()
    transaction_reference = serializers.CharField()


class InterBankTransferSerializer(serializers.Serializer):
    transaction_reference = serializers.CharField()
    amount = serializers.FloatField()
    beneficiary_account_number = serializers.CharField()
    beneficiary_account_name = serializers.CharField()
    beneficiary_bank_code = serializers.CharField()
    narration = serializers.CharField()
    mode = serializers.ChoiceField(
        enums.APIMode.choices
    )


class IntraBankTransferSerializer(serializers.Serializer):
    transaction_reference = serializers.CharField()
    amount = serializers.FloatField()
    beneficiary_account_number = serializers.CharField()
    beneficiary_account_name = serializers.CharField()
    narration = serializers.CharField()
    mode = serializers.ChoiceField(
        enums.APIMode.choices
    )


class VerifyCashConnectBankTransferSerializer(serializers.Serializer):
    transaction_reference = serializers.CharField()


class AccountNameLookupInterSerializer(serializers.Serializer):
    bank_code = serializers.CharField()
    account_number = serializers.CharField()


class CreateVirtualAccountWithIdSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()
    id_number = serializers.CharField()
    type_of_id = serializers.ChoiceField(
        enums.TypeOfId.choices
    )
    dob = serializers.DateField(
        format="%Y-%m-%d", input_formats=["%Y-%m-%d"])  # format: YYYY-MM-DD


class MoveValueFromSettlementWalletToDisubursementWalletSerializer(serializers.Serializer):
    amount = serializers.FloatField()


class BookCashConnectLoanSerializer(serializers.Serializer):
    # Customer Details
    customer_id = serializers.CharField()
    customer_first_name = serializers.CharField()
    customer_middle_name = serializers.CharField()
    customer_last_name = serializers.CharField()
    customer_email_address = serializers.EmailField()
    customer_phone_number = serializers.CharField()
    customer_address = serializers.CharField()
    customer_gender = serializers.CharField() #//Male or Female
    customer_passport_photo = serializers.CharField(default="https://meet.google.com/landing") #//optional
    customer_country = serializers.CharField()
    customer_state = serializers.CharField()
    customer_city = serializers.CharField()
    customer_bvn = serializers.CharField(allow_blank=True) #//optional
    customer_nin = serializers.CharField(allow_blank=True) #//optional
    customer_date_of_birth = serializers.DateField(
        format="%Y-%m-%d", input_formats=["%Y-%m-%d"]
    )

    # Loan Details
    loan_request_amount = serializers.CharField()
    interest_rate = serializers.CharField()
    tenure_frequency_code = serializers.CharField()
    tenure = serializers.IntegerField()
    repayment_type_code = serializers.CharField()
    start_date = serializers.DateField(
        format="%Y-%m-%d", input_formats=["%Y-%m-%d"]
    )
    loan_sector_code = serializers.CharField()
    collateral_type_code = serializers.CharField()
    collateral_value = serializers.CharField()
    collateral_description =  serializers.CharField()
    loan_source_code = serializers.CharField()
    loan_purpose = serializers.CharField()
    calculation_method_code = serializers.CharField()
    lending_model_code = serializers.CharField()
    request_reference_id = serializers.CharField()
    settlement_account = serializers.CharField(allow_blank=True) #// optional if the customer already have a cashconnect account

    def validate(self, attrs):
        customer_bvn = attrs.get("customer_bvn")
        customer_nin = attrs.get("customer_nin")
        if not customer_bvn and not customer_nin:
            raise serializers.ValidationError(
                "BVN or NIN is required"
            )
        return attrs


class DisburseshConnectLoanSerializer(serializers.Serializer):
    customer_id = serializers.CharField()
    loan_account = serializers.CharField()
    loan_booking_request_reference_id = serializers.CharField()