from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
import uuid
from accounts.models import AccountDetail
from accounts.serializers import AccountDetailSerializer
from cash_connect.helpers.manage_request import CashConnectRequestManagers
from cash_connect.serializers import (
    AccountNameLookupInterSerializer,
    CreateDynamicInstantVirtualAccountSerializer,
    CreateVirtualAccountSerializer,
    CreateVirtualAccountWithIdSerializer,
    InterBankTransferSerializer,
    IntraBankTransferSerializer,
    MoveValueFromSettlementWalletToDisubursementWalletSerializer,
    VerifyCashConnectBankTransferSerializer,
    BookCashConnectLoanSerializer,
    DisburseshConnectLoanSerializer,
)
from helpers import enums
from helpers.custom_permissions import (
    APIAccessPermission,
    CompanyCanSend<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    CompanyCanDisburseLoan,
)

from decouple import config


# Create your views here.
@method_decorator(csrf_exempt, name="dispatch")
class CashConnectWebhookNotificationApiView(APIView, CashConnectRequestManagers):
    def post(self, request, *args, **kwargs):
        data = request.body
        if not data:
            return Response(dict(status=False), status=status.HTTP_204_NO_CONTENT)
        # DECODE THE DATA
        data = data.decode("utf-8")
        if not data:
            return Response(dict(status=False), status=status.HTTP_204_NO_CONTENT)
        try:
            self.handle_webhook_notification(data)
        except Exception as e:
            pass
        return Response(dict(status=True), status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class CreateVirtualAccountApiView(APIView, CashConnectRequestManagers):
    permission_classes = [IsAuthenticated]
    serializer_class = CreateVirtualAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        response_status, response_data = self.create_virtual_account(**serialized_data)
        if response_status:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


@method_decorator(csrf_exempt, name="dispatch")
class GetCashConnectAccountDetailApiView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        IsIPWhitelisted,
        CompanyCanSendMoney,
    ]

    def get(self, request):
        account_detail_data = (
            AccountDetail.get_or_create_cash_connect_account_detail_instance(
                company=request.user
            )
        )
        serialized_data = AccountDetailSerializer(account_detail_data).data
        return Response(
            dict(status=True, data=serialized_data), status=status.HTTP_200_OK
        )


@method_decorator(csrf_exempt, name="dispatch")
class CreateDynamicInstantVirtualAccountApiView(APIView, CashConnectRequestManagers):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = CreateDynamicInstantVirtualAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        response_status, response_data = self.create_instant_account(**serialized_data)
        if response_status:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


@method_decorator(csrf_exempt, name="dispatch")
class InterBankTransferApiView(APIView, CashConnectRequestManagers):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        # IsIPWhitelisted,
        CompanyCanSendMoney,
        CompanyCanDisburseLoan,
    ]
    serializer_class = InterBankTransferSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        response_status, response_data = self.handle_inter_bank_transfer(
            **serialized_data
        )
        if response_status:
            data = {
                "status": response_data.get("status"),
                "message": response_data.get("message"),
                "transaction": serializer.data,
                "account_balance": response_data.get("account_balance"),
            }
            return Response(dict(status=True, data=data), status=status.HTTP_200_OK)
        return Response(
            dict(status=False, data=response_data), status=status.HTTP_400_BAD_REQUEST
        )


@method_decorator(csrf_exempt, name="dispatch")
class IntraBankTransferApiView(APIView, CashConnectRequestManagers):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        # IsIPWhitelisted,
        CompanyCanSendMoney,
    ]
    serializer_class = IntraBankTransferSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        serialized_data[
            "type_of_request"
        ] = enums.CashConnectRquestTypes.INTRA_DISBURSEMENT

        response_status, response_data = self.handle_intra_bank_transfer(
            **serialized_data
        )
        if response_status:
            data = {
                "status": response_data.get("status"),
                "message": response_data.get("message"),
                "transaction": serializer.data,
                "account_balance": response_data.get("account_balance"),
            }
            return Response(dict(status=True, data=data), status=status.HTTP_200_OK)
        return Response(
            dict(status=False, data=response_data), status=status.HTTP_400_BAD_REQUEST
        )


@method_decorator(csrf_exempt, name="dispatch")
class VerifyCashConnectBankTransferApiView(APIView, CashConnectRequestManagers):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = VerifyCashConnectBankTransferSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = self.handle_disbursement_verification(
            serializer.validated_data.get("transaction_reference")
        )
        return Response(dict(status=True, data=data), status=status.HTTP_200_OK)


class FetchBankListApiView(APIView, CashConnectRequestManagers):
    permission_classes = []

    def get(self, request):
        data = self.fetch_bank_list()
        return Response(dict(status=True, data=data), status=status.HTTP_200_OK)


class AccountNameLookupInterApiView(APIView, CashConnectRequestManagers):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = AccountNameLookupInterSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = self.account_name_lookup_inter(
            bank_code=serializer.validated_data.get("bank_code"),
            account_number=serializer.validated_data.get("account_number"),
        )
        return Response(dict(status=True, data=data), status=status.HTTP_200_OK)


class FetchCashConnectFloatAccountBalanceApiView(APIView, CashConnectRequestManagers):
    permission_classes = [
        IsAuthenticated,
        IsAdmin,
    ]

    def get(self, request):
        data = self.fetch_cash_connect_account_balance()
        return Response(dict(status=True, data=data), status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class CreateVirtualAccountWithIdApiView(APIView, CashConnectRequestManagers):
    serializer_class = CreateVirtualAccountWithIdSerializer
    permission_classes = [IsAuthenticated | APIAccessPermission]

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        response_status, response_data = self.create_virtual_account_with_id(
            **serialized_data
        )
        if response_status:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class MoveValueFromSettlementWalletToDisubursementWalletApiView(
    APIView, CashConnectRequestManagers
):
    permission_classes = [
        IsAuthenticated,
        IsAdmin,
    ]

    serializer_class = MoveValueFromSettlementWalletToDisubursementWalletSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        serialized_data = serializer.validated_data

        transaction_reference = str(uuid.uuid4())
        beneficiary_account_number = config("CASH_CONNECT_DISBURSEMENT_ACCOUNT_NUMBER")
        beneficiary_account_name = config("CASH_CONNECT_DISBURSEMENT_ACCOUNT_NAME")
        narration = "Transfer from settlement wallet to disbursement wallet"
        mode = enums.APIMode.LIVE

        serialized_data["disbursementReference"] = transaction_reference
        serialized_data["beneficiaryAccount"] = beneficiary_account_number
        serialized_data["beneficiaryAccountName"] = beneficiary_account_name
        serialized_data["narration"] = narration
        # serialized_data["mode"] = mode
        serialized_data["disbursementAmount"] = serializer.validated_data.get("amount")

        data = self.disbursement_wallet_funding(**serialized_data)

        return Response(data={"data": data}, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class BookCashConnectLoanApiView(APIView, CashConnectRequestManagers):
    serializer_class = BookCashConnectLoanSerializer

    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        # IsIPWhitelisted,
        CompanyCanSendMoney,
        CompanyCanDisburseLoan,
    ]

    def post(self, request, *args, **kwargs):
        from datetime import datetime

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        serialized_data = serializer.validated_data
        serialized_data["customer_date_of_birth"] = datetime.strftime(
            serialized_data["start_date"], "%Y-%m-%d"
        )
        serialized_data["start_date"] = datetime.strftime(
            serialized_data["start_date"], "%Y-%m-%d"
        )

        response_data = self.book_loan(**serialized_data)
        if isinstance(response_data, dict) and response_data.get("responseCode") != 400:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class FetchCashConnectLoansRuntimeParamsView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        # IsAdmin,
    ]

    def get(self, request):
        data = self.fetch_runtime_parameters()
        return Response(dict(status=True, data=data), status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class DisburseCashConnectLoanApiView(APIView, CashConnectRequestManagers):
    serializer_class = DisburseshConnectLoanSerializer

    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        # IsIPWhitelisted,
        CompanyCanSendMoney,
        CompanyCanDisburseLoan,
    ]

    def post(self, request, *args, **kwargs):
        from datetime import datetime

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data

        response_data = self.disburse_loan(**serialized_data)
        if isinstance(response_data, dict) and response_data.get("responseCode") != 400:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CheckSettlementAccountBalancesView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        IsAdmin,
    ]

    def get(self, request):
        response_data = self.check_settlement_account_balance()

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CashConnectLoanRepaymentScheduleView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        IsAdmin,
    ]

    def get(self, request):
        arguments = {
            "loan_account_number": request.query_params.get("loan_account_number")
        }
        response_data = self.fetch_loan_repayment_schedule(**arguments)

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CashConnectFetchCustomerLoansView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        IsAdmin,
    ]

    def get(self, request):
        arguments = {"customer_id": request.query_params.get("customer_id")}
        response_data = self.fetch_customer_loans(**arguments)

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CashConnectLoanDetailsView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        IsAdmin,
    ]

    def get(self, request):
        arguments = {
            "loan_account_number": request.query_params.get("loan_account_number")
        }
        response_data = self.fetch_loan_details_by_account_number(**arguments)

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CashConnectFetchCustomerLoanSettlementAccountView(
    APIView, CashConnectRequestManagers
):

    permission_classes = [
        IsAuthenticated,
        # IsAdmin,
    ]

    def get(self, request):
        arguments = {"customer_id": request.query_params.get("customer_id")}
        response_data = self.fetch_customer_loan_settlement_account(**arguments)

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CashConnectAccountEnquiryView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        # IsAdmin,
    ]

    def get(self, request):
        arguments = {
            "loan_account_number": request.query_params.get("loan_account_number")
        }
        response_data = self.make_account_enquiry(**arguments)

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CashConnectFetchAccountDetailsView(APIView, CashConnectRequestManagers):

    permission_classes = [
        IsAuthenticated,
        # IsAdmin,
    ]

    def get(self, request):
        arguments = {
            "loan_account_number": request.query_params.get("loan_account_number")
        }
        response_data = self.fetch_account_details(**arguments)

        if isinstance(response_data, dict) and response_data.get(
            "responseCode"
        ) not in [400, 404]:
            return Response(
                dict(status=True, data=response_data), status=status.HTTP_200_OK
            )
        else:
            return Response(
                dict(status=False, data=response_data),
                status=status.HTTP_400_BAD_REQUEST,
            )


class LoanIntraBankTransferApiView(APIView, CashConnectRequestManagers):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        IsIPWhitelisted,
        CompanyCanSendMoney,
        CompanyCanDisburseLoan,
    ]
    serializer_class = IntraBankTransferSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        serialized_data[
            "type_of_request"
        ] = enums.CashConnectRquestTypes.INTRA_DISBURSEMENT

        response_status, response_data = self.handle_loan_intra_bank_transfer(
            **serialized_data
        )
        if response_status:
            data = {
                "status": response_data.get("status"),
                "message": response_data.get("message"),
                "transaction": serializer.data,
                "account_balance": response_data.get("account_balance"),
            }
            return Response(dict(status=True, data=data), status=status.HTTP_200_OK)
        return Response(
            dict(status=False, data=response_data), status=status.HTTP_400_BAD_REQUEST
        )


@method_decorator(csrf_exempt, name="dispatch")
class LoanInterBankTransferApiView(APIView, CashConnectRequestManagers):
    permission_classes = [
        IsAuthenticated,
        IsCompanyVerified,
        IsIPWhitelisted,
        CompanyCanSendMoney,
        CompanyCanDisburseLoan,
    ]
    serializer_class = InterBankTransferSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serialized_data = serializer.validated_data
        serialized_data["company_id"] = request.user.id
        response_status, response_data = self.handle_loan_inter_bank_transfer(
            **serialized_data
        )
        if response_status:
            data = {
                "status": response_data.get("status"),
                "message": response_data.get("message"),
                "transaction": serializer.data,
                "account_balance": response_data.get("account_balance"),
            }
            return Response(dict(status=True, data=data), status=status.HTTP_200_OK)
        return Response(
            dict(status=False, data=response_data), status=status.HTTP_400_BAD_REQUEST
        )


class LoanVerifyCashConnectBankTransferApiView(APIView, CashConnectRequestManagers):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = VerifyCashConnectBankTransferSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = self.handle_disbursement_verification(
            serializer.validated_data.get("transaction_reference"),
            service_partner="LOANS",
        )
        return Response(dict(status=True, data=data), status=status.HTTP_200_OK)
