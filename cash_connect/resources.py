from import_export import resources
from cash_connect import models

class CashConnectWebHookDataResource(resources.ModelResource):
    class Meta:
        model = models.CashConnectWebHookData

        
class CashConnectCustomersResource(resources.ModelResource):
    class Meta:
        model = models.CashConnectCustomers


class CashConnectVirtualAccountResource(resources.ModelResource):
    class Meta:
        model = models.CashConnectVirtualAccount


class CashConnectRequestLogsResource(resources.ModelResource):
    class Meta:
        model = models.CashConnectRequestLogs


class CashConnectDisbursementWalletFundingFromSettlementWalletLogsResource(resources.ModelResource):
    class Meta:
        model = models.CashConnectDisbursementWalletFundingFromSettlementWalletLogs