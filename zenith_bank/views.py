import json

from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from helpers.custom_responses import Response

from .models import ManagedAccount, TransactionCallback


# Create your view(s) here.
class CallbackAPIView(APIView):
    authentication_classes = [BasicAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        data = request.data
        if data != {}:
            managed_account = data.get("recipient_account_number")
            data["payload"] = json.dumps(data)

            get_managed_account = ManagedAccount.objects.filter(
                account_number=managed_account
            )
            if get_managed_account.exists():
                account_owner = get_managed_account.first().user.email
                data["user"] = account_owner

                TransactionCallback.create_callback_transaction(data=data)
                return Response(
                    data=dict(
                        message="Submission was received successfully."),
                    status=status.HTTP_200_OK)
            else:
                TransactionCallback.create_callback_transaction(data=data)
                return Response(
                    data=dict(
                        message="Submission was received successfully."),
                    status=status.HTTP_200_OK)
        return Response(
            errors=dict(message="payload cannot be empty."),
            status=status.HTTP_400_BAD_REQUEST
        )
