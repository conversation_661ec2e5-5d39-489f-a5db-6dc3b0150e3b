from django.contrib import admin
from import_export.admin import ImportExportModelAdmin

from . import models, resources


# Register your model(s) here.
class ManagedAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ManagedAccountResource
    search_fields = [
        "first_name",
        "last_name",
        "bvn",
        "email",
        "phone",
        "account_number",
        "company__email",

    ]
    list_filter = ("company",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionCallbackResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionCallbackResource
    search_fields = [
        "recipient_account_number",
        "payer_account_name",
        "transaction_reference",
        "company",

    ]
    list_filter = (
        "recipient_account_number",
        "company",

    )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.ManagedAccount, ManagedAccountResourceAdmin)
admin.site.register(models.TransactionCallback,
                    TransactionCallbackResourceAdmin)
