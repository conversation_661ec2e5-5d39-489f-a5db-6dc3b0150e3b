from django.conf import settings
from django.db import models

from core.models import BaseModel


Company = settings.AUTH_USER_MODEL


# Create your model(s) here.
class ManagedAccount(BaseModel):
    first_name = models.CharField(max_length=255)
    middle_name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255)
    bvn = models.CharField(max_length=255, blank=True, null=True)
    email = models.EmailField(verbose_name="user email")
    phone = models.Char<PERSON>ield(max_length=14, null=True, blank=True)
    date_of_birth = models.Char<PERSON>ield(max_length=225, null=True, blank=True)
    account_number = models.Char<PERSON>ield(max_length=10, unique=True)
    bank_name = models.CharField(max_length=255, default="Zenith Bank Plc")
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self) -> str:
        return self.company.__str__()

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'MANAGED ACCOUNT'
        verbose_name_plural = 'MANAGED ACCOUNTS'

    @property
    def fullname(self):
        if self.first_name is not None and self.last_name is not None:
            return f"{self.first_name} {self.last_name}"
        pass


class TransactionCallback(BaseModel):
    amount = models.CharField(max_length=225)
    recipient_account_number = models.CharField(max_length=225)
    payer_account_name = models.CharField(max_length=225)
    payer_account_number = models.CharField(max_length=225)
    payer_account_bvn = models.CharField(max_length=225)
    payer_bank_code = models.CharField(max_length=225)
    channel = models.CharField(max_length=225)
    type = models.CharField(max_length=225)
    paid_at = models.CharField(max_length=225)
    details = models.TextField()
    transaction_reference = models.CharField(max_length=225)
    payload = models.TextField()
    company = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = 'MANAGED ACCOUNT TRANSACTION CALLBACK'
        verbose_name_plural = 'MANAGED ACCOUNT TRANSACTION CALLBACKS'

    @classmethod
    def create_callback_transaction(cls, data):
        transaction = cls.objects.create(
            amount=data.get("amount"),
            payer_account_name=data.get("payer_account_name"),
            channel=data.get("channel"),
            payer_account_bvn=data.get("payer_account_bvn"),
            type=data.get("type"),
            paid_at=data.get("paid_at"),
            payer_bank_code=data.get("payer_bank_code"),
            recipient_account_number=data.get("recipient_account_number"),
            payer_account_number=data.get("payer_account_number"),
            details=data.get("details"),
            transaction_reference=data.get("transaction_reference"),
            payload=data.get("payload"),
            user=data.get("user")
        )
        return transaction
