from accounts.models import TransactionDetail

from .api_specifications import ZenithBank


# Zenith Bank API function(s).
def zenith_direct_transfer(transfer_type: str, data):
    zenith_manager = ZenithBank()

    if transfer_type == "zenith":
        response = zenith_manager.transfer_direct_zenith(**data)
    if transfer_type == "others":
        response = zenith_manager.transfer_direct_other_banks(**data)

    if response.get("status") == True:
        reference = response.get("response").get("transactionReference")
        if (
            response.get("response").get("responseCode") == "00"
            and response.get("response").get("responseDescription") == "SUCCESS"
        ):
            TransactionDetail.objects.filter(
                reference=reference
            ).update(transaction_status="SUCCESSFUL")
        if (
            response.get("response").get("responseCode") == "96"
            and response.get("response").get("responseDescription") == "FAILED"
        ):
            TransactionDetail.objects.filter(
                reference=reference
            ).update(transaction_status="FAILED")


def zenith_transaction_status(reference: str):
    zenith_manager = ZenithBank()
    response = zenith_manager.get_transaction_details(reference=reference)

    if response.get("status") == True:
        reference = response.get("response").get("transactionReference")
        if (
            response.get("response").get("responseCode") == "00"
            and response.get("response").get("responseDescription") == "SUCCESS"
        ):
            TransactionDetail.objects.filter(
                reference=reference
            ).update(transaction_status="SUCCESSFUL")
        if (
            response.get("response").get("responseCode") == "96"
            and response.get("response").get("responseDescription") == "FAILED"
        ):
            TransactionDetail.objects.filter(
                reference=reference
            ).update(transaction_status="FAILED")
