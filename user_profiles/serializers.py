from rest_framework import serializers

from helpers.reusable import (
    add_prefix_to_phone,
    validate_password,
)
from user_profiles import models


# Create your serializer(s) here.
class CompanySerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        style={"input_type": "password"},
        validators=[validate_password],
        write_only=True,
    )
    confirm_password = serializers.CharField(
        required=True,
        style={"input_type": "password"},
        validators=[validate_password],
        write_only=True,
    )

    class Meta:
        model = models.Company
        fields = [
            "name",
            "phone",
            "email",
            "password",
            "confirm_password",
            "bvn",
            "account_name_prefix",
            "nuban",
            "address",
            "callback_url",
            "api_key",
        ]
        read_only_fields = [
            "nuban",
            "api_key",
        ]

    def validate(self, attrs):
        if attrs.get("password") != attrs.get("confirm_password"):
            raise serializers.ValidationError(
                {"detail": "password(s) do not match."}
            )
        attrs.pop("confirm_password")
        return super().validate(attrs)


class CompanyLoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(
        style={"input_type": "password"}, validators=[validate_password]
    )


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(
        style={"input_type": "password"}, validators=[validate_password]
    )
    new_password = serializers.CharField(
        style={"input_type": "password"}, validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        style={"input_type": "password"}, validators=[validate_password]
    )

    def validate(self, attrs):
        if attrs.get("new_password") != attrs.get("confirm_password"):
            raise serializers.ValidationError(
                {"detail": "new password(s) do not match."}
            )
        attrs.pop("confirm_password")
        return super().validate(attrs)


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate(self, attrs):
        if not attrs.get("email") or attrs.get("phone_number"):
            raise serializers.ValidationError(
                {"detail": "input a valid email or phone number."}
            )
        return super().validate(attrs)


class CompanyVerificationSerializer(serializers.Serializer):
    recipient = serializers.EmailField()
    otp = serializers.CharField(max_length=6)


class CompanyPasswordResetSerializer(serializers.Serializer):
    otp = serializers.CharField(max_length=6)
    email = serializers.EmailField()
    new_password = serializers.CharField(
        style={"input_type": "password"}, validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        style={"input_type": "password"}, validators=[validate_password]
    )

    def validate(self, attrs):
        if attrs.get("new_password") != attrs.get("confirm_password"):
            raise serializers.ValidationError(
                {"detail": "new password(s) do not match."}
            )
        attrs.pop("confirm_password")
        return super().validate(attrs)


class CompanyProfileUpdateSerializer(serializers.Serializer):
    address = serializers.CharField(required=False)
    account_name_prefix = serializers.CharField(required=False)
    callback_url = serializers.CharField(required=False)


class CACDocumentSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.CACDocument
        fields = "__all__"
        read_only_fields = [
            "company",
        ]


class SubCompanySerializer(serializers.ModelSerializer):
    company_phone = serializers.CharField(
        max_length=11, validators=[add_prefix_to_phone]
    )

    class Meta:
        model = models.SubCompany
        fields = "__all__"
        read_only_fields = [
            "company",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.name
        return representation


class SubCompanyProfileUpdateSerializer(serializers.Serializer):
    sub_company = serializers.UUIDField()
    address = serializers.CharField(required=False)
    account_name_prefix = serializers.CharField(required=False)
    callback_url = serializers.CharField(required=False)

    def validate(self, attrs):
        sub_company = models.SubCompany.objects.filter(
            id=attrs.get("sub_company")
        ).first()

        if sub_company is None:
            raise serializers.ValidationError(
                {"detail": "invalid sub company ID"})
        attrs["sub_company"] = sub_company
        return super().validate(attrs)
