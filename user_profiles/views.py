from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from helpers.custom_responses import Response
from user_profiles import models, serializers


# Create your view(s) here.
class CompanyRegisterAPIView(APIView):
    serializer_class = serializers.CompanySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = models.Company.register(**serializer.validated_data)
        serializer = self.serializer_class(instance=company)
        return Response(data=serializer.data, status_code=201, status=status.HTTP_201_CREATED)


class CompanyVerificationAPIView(APIView):

    def post(self, request, *args, **kwargs):
        serializer = serializers.CompanyVerificationSerializer(
            data=request.data)
        serializer.is_valid(raise_exception=True)
        verify = models.Company.verify(**serializer.validated_data)

        if verify.get("status") == True:
            return Response(data=verify, status_code=200, status=status.HTTP_200_OK)
        return Response(errors=verify, status_code=400, status=status.HTTP_400_BAD_REQUEST)


class CompanySignInAPIView(APIView):

    def post(self, request, *args, **kwargs):
        serializer = serializers.CompanyLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = models.Company.sign_in(**serializer.validated_data)
        if company is None:
            return Response(
                errors={"message": "invalid credentials (wrong email or password)."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST
            )
        if company.get("status"):
            return Response(data=company, status_code=200, status=status.HTTP_200_OK)
        else:
            return Response(errors=company, status_code=412, status=status.HTTP_412_PRECONDITION_FAILED)
            


class CompanyDetailsAPIView(APIView):
    permission_classes = [IsAuthenticated,]
    serializer_class = serializers.CompanySerializer
    update_serializer_class = serializers.CompanyProfileUpdateSerializer

    def get(self, request, *args, **kwargs):
        company = models.Company.get_details(id=request.user.id)
        serializer = self.serializer_class(instance=company)
        return Response(data=serializer.data, status_code=200, status=status.HTTP_200_OK)
    
    def put(self, request, *args, **kwargs):
        serializer = self.update_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        fields_to_be_updated = serializer.validated_data.items()
        if len(fields_to_be_updated) == 0:
            return Response(
                errors={"message": "cannot update profile with empty fields."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST
            )
        company = models.Company.update_profile(
            company=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=company)
        data = {
            "message": "successfully updated company profile.",
            "company": serializer.data
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class ChangePasswordAPIView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request, *args, **kwargs):
        serializer = serializers.ChangePasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        password = models.Company.change_password(
            company=request.user, **serializer.validated_data
        )
        if password.get("status") == True:
            return Response(data=password, status_code=200, status=status.HTTP_200_OK)
        return Response(data=password, status_code=400, status=status.HTTP_400_BAD_REQUEST)


class ForgotPasswordAPIView(APIView):

    def post(self, request, *args, **kwargs):
        serializer = serializers.ForgotPasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        forgot_password = models.Company.forgot_password(
            **serializer.validated_data)
        if forgot_password.get("status") == True:
            return Response(data=forgot_password, status_code=200, status=status.HTTP_200_OK)
        return Response(errors=forgot_password, status_code=400, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordAPIView(APIView):

    def post(self, request, *args, **kwargs):
        serializer = serializers.CompanyPasswordResetSerializer(
            data=request.data)
        serializer.is_valid(raise_exception=True)
        reset_password = models.Company.reset_password(
            **serializer.validated_data)
        if reset_password.get("status") == True:
            return Response(data=reset_password, status_code=200, status=status.HTTP_200_OK)
        return Response(data=reset_password, status_code=40, status=status.HTTP_400_BAD_REQUEST)


class CACDocumentAPIView(APIView):
    permission_classes = [IsAuthenticated,]
    serializer_class = serializers.CACDocumentSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(company=request.user)
        return Response(
            data={"message": "document upload was successful."},
            status_code=201,
            status=status.HTTP_201_CREATED
        )


class CompanyVerifyTokenAPIView(APIView):
    permission_classes = [IsAuthenticated,]

    def get(self, request, *args, **kwargs):
        return Response(data={"message": True}, status_code=200, status=status.HTTP_200_OK)


class SubCompanyRegisterAPIView(APIView):
    permission_classes = [IsAuthenticated,]
    serializer_class = serializers.SubCompanySerializer
    update_serializer_class = serializers.SubCompanyProfileUpdateSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sub_company = models.SubCompany.register(
            company=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=sub_company)
        data = {
            "message": "sub company created successfully.",
            "sub_company": serializer.data
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        sub_companies = models.SubCompany.objects.filter(company=request.user)
        if sub_companies.exists():
            serializer = self.serializer_class(
                instance=sub_companies, many=True
            )
            data = {
                "message": "successfully fetched sub companies",
                "sub_companies": serializer.data
            }
        else:
            data = {
                "message": "company has no sub companies yet.",
                "sub_companies": []
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def put(self, request, *args, **kwargs):
        serializer = self.update_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        fields_to_be_updated = serializer.validated_data.items()
        if len(fields_to_be_updated) <= 1:
            return Response(
                errors={"message": "cannot update profile with empty fields."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST
            )
        sub_company = models.SubCompany.update_profile(
            **serializer.validated_data
        )
        serializer = self.serializer_class(instance=sub_company)
        data = {
            "message": "successfully updated sub company profile.",
            "company": serializer.data
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)
