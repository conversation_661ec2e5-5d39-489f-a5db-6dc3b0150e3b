from import_export.admin import ImportExportModelAdmin

from django.contrib import admin

from user_profiles import models, resources


# Register your model(s) here.
class CompanyProfileResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CompanyProfileResource
    search_fields = [
        "name",
        "phone",
        "email",
        "bvn",
        "nuban",
    ]

    list_filter = [
        "email_verified",
        "is_active",
        "is_verified",
        "can_send_money",
    ]

    list_display = [
        "id",
        "created_at",
        "updated_at",
        "last_login",
        "name",
        "phone",
        "email",
        "bvn",
        "nuban",
        "account_name_prefix",
        "email_verified",
        "address",
        "callback_url",
        "is_active",
        "is_verified",
        "is_staff",
        "is_admin",
        "can_send_money",
    ]

    date_hierarchy = "created_at"


class CACDocumentResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CACDocumentResource
    search_fields = [
        "company__email"
    ]
    list_filter = []

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SubCompanyResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SubCompanyResource
    search_fields = [
        "company_name",
        "company_email",
        "company__email"
    ]
    list_filter = []

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class APIKeyResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.APIKeyResource
    search_fields = [
        "company__email",
        "sub_company__email"
    ]
    list_filter = [
        "is_active",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.Company, CompanyProfileResourceAdmin)
admin.site.register(models.CACDocument, CACDocumentResourceAdmin)
admin.site.register(models.SubCompany, SubCompanyResourceAdmin)
admin.site.register(models.APIKey, APIKeyResourceAdmin)
