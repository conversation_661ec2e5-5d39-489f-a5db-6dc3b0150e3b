from django.urls import include, path

from user_profiles import views


# Create your url pattern(s) here.
auth_urls = [
    path("auth/register/", views.CompanyRegisterAPIView.as_view()),
    path("auth/verify/", views.CompanyVerificationAPIView.as_view()),
    path("auth/login/", views.CompanySignInAPIView.as_view()),
    path("auth/details", views.CompanyDetailsAPIView.as_view()),
    path("auth/change-password/", views.ChangePasswordAPIView.as_view()),
    path("auth/forgot-password/", views.ForgotPasswordAPIView.as_view()),
    path("auth/verify_token", views.CompanyVerifyTokenAPIView.as_view()),
]

cac_docs_urls = [
    path("cac_docs/", views.CACDocumentAPIView.as_view()),
]

sub_company_urls = [
    path("sub_companies/", views.SubCompanyRegisterAPIView.as_view()),
]


urlpatterns = [
    path("", include(auth_urls)),
    path("", include(cac_docs_urls)),
    path("", include(sub_company_urls)),
]
