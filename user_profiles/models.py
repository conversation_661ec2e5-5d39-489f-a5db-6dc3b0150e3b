from datetime import datetime
from typing import Optional

from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import check_password
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import MinValueValidator
from django.db import models, transaction
from django.utils.translation import gettext as _
import pytz
from rest_framework_simplejwt.tokens import RefreshToken

from core.models import BaseModel, OTP
from helpers.enums import AccountType, OTPType
from helpers.reusable import (
    CryptographyService,
    add_prefix_to_phone,
    email_sender,
    upload_path,
    validate_password,
)
from user_profiles.managers import UserManager


logger = settings.LOGGER


# Create your model(s) here.
class Company(BaseModel, AbstractBaseUser, PermissionsMixin):
    name = models.CharField(max_length=255)
    phone = models.CharField(max_length=25)
    email = models.EmailField(max_length=255, unique=True)
    bvn = models.CharField(max_length=25, blank=True, null=True)
    password = models.CharField(
        max_length=255,
        validators=[validate_password],
        editable=False,
    )
    email_verified = models.BooleanField(default=False)
    account_name_prefix = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        help_text="The account name prefix for account creation ('Liberty'/Joseph Chinedu).",
    )
    nuban = models.CharField(max_length=10, null=True, blank=True)
    service_fee = models.FloatField(default=0.0)
    overdraft_allowed = models.BooleanField(default=False)
    callback_url = models.TextField(null=True, blank=True)
    cash_connect_callback_url = models.TextField(null=True, blank=True)
    fidelity_callback_url = models.TextField(null=True, blank=True)
    address = models.TextField()
    api_key = models.TextField(null=True, blank=True, editable=False)
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(
        default=False,
        help_text="When enabled the user/company can perform operation(s)."
    )
    is_staff = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)
    can_send_money = models.BooleanField(
        default=False,
        help_text="When enabled the user/company can send a transfer money request."
    )
    notify = models.BooleanField(default=False)
    balance_benchmark = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    contact_person = models.TextField(null=True, blank=True)

    objects = UserManager()

    USERNAME_FIELD = "email"

    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY PROFILE"
        verbose_name_plural = "COMPANY PROFILES"

    @classmethod
    @transaction.atomic
    def register(
        cls,
        name: str,
        phone: str,
        email: str,
        password: str,
        address: str,
        bvn: Optional[str] = None,
        account_name_prefix: Optional[str] = None,
        callback_url: Optional[str] = None,
    ) -> object:
        """
        - creates the company
        - creates the company's main wallet
        - generates the company's api key
        - sends email verification mail
        Returns:
            Company: The created company object if successful..
        """
        from accounts.models import AccountDetail

        company = cls.objects.create_user(
            name=name,
            phone=add_prefix_to_phone(phone),
            email=email,
            password=password,
            address=address,
            bvn=bvn,
            account_name_prefix=account_name_prefix,
            callback_url=callback_url,
        )
        account_details = AccountDetail.create_company_account(
            company=company,
            company_name=name,
            account_type=AccountType.MAIN,
        )
        api_key = APIKey.generate_key(company=company)
        company.nuban = account_details.account_number
        company.api_key = api_key.key
        company.save()

        otp = OTP.get_otp(
            type=OTPType.REGISTRATION,
            recipient=email,
            length=6,
            expiry_time=10,
        )
        email_sender(
            recipient=[email],
            subject="LibertyPay Banking Service",
            text=f"""
Welcome onboard {name.title()},\n
Thank you for registering on our platform! Please verify your account with the code provided below:\n
{otp}\n\n
Best regards,\n
The Team at Liberty Tech X.
            """,
        )
        return company

    @classmethod
    def verify(cls, recipient: str, otp: str) -> dict:
        """
        Verifies the company profile based on the provided OTP.
        Returns:
            dict: A dictionary containing the verification status and message.
        """
        verify = OTP.verify_otp(recipient=recipient, otp=otp)
        if not verify.get("status"):
            return {"status": False, "message": "invalid or expired OTP."}
        else:
            company = cls.objects.filter(email=recipient).first()
            if company is not None:
                company.email_verified = True
                company.save()
                return {
                    "status": True,
                    "message": "company profile was verified successfully.",
                }
            else:
                return {
                    "status": False,
                    "message": "company profile not found.",
                }

    @classmethod
    def sign_in(cls, email: str, password: str) -> dict or None:  # type: ignore
        """
        Authenticates a company with the provided email and password, generating a token for successful sign-in.
        """
        company = authenticate(email=email, password=password)
        if company is None:
            return None
        else:
            if not company.email_verified:
                return {
                    "status": False,
                    "message": "company email is not verified.",
                    "company_id": None,
                    "access": None,
                    "refresh": None,
                }
            else:
                token = RefreshToken.for_user(company)
                company.last_login = datetime.now(
                    tz=pytz.timezone(settings.TIME_ZONE)
                )
                company.save()
                return {
                    "status": True,
                    "message": "success",
                    "company_id": company.id,
                    "company_name": company.name,
                    "company_email": company.email,
                    "access": str(token.access_token),
                    "refresh": str(token),
                }

    @classmethod
    def get_details(cls, id: str) -> object:
        """
        Retrieve company details based on the given ID.
        """
        try:
            company = cls.objects.get(id=id)
        except cls.DoesNotExist:
            company = None
        return company

    @classmethod
    def change_password(
        cls, company: object, old_password: str, new_password: str
    ) -> dict:
        """
        Change the password of a company.
        """
        verify_password = check_password(old_password, company.password)
        if not verify_password:
            return {
                "status": False,
                "message": "old password is incorrect, forgot password?",
            }
        else:
            # crosscheck passwords for similarities
            if old_password == new_password:
                return {"status": False, "message": "similar password, try a new one."}
            else:
                company.set_password(new_password)
                company.save()
                return {"status": True, "message": "password changed successfully."}

    @classmethod
    def forgot_password(cls, email: str) -> dict:
        """
        Send a password reset OTP to the company's email.
        """
        company = cls.objects.filter(email=email).first()
        if company is not None:
            otp = OTP.get_otp(
                type="PASSWORD RESET", recipient=email, length=6, expiry_time=10
            )
            email_sender(
                recipient=[email],
                subject="Password Reset",
                text=f"""
Hello {company.name.title()},\n
A password reset was requested on your account, complete the process with the code below:\n
{otp}\n
Kindly disregard this email if you didn't request one.\n\n
Best regards,\n
The Team at Liberty Tech X.
                """,
            )
            return {
                "status": True,
                "message": "OTP has been sent to your registered email.",
            }
        else:
            return {"status": False, "message": "company profile not found."}

    @classmethod
    def reset_password(
        cls,
        otp: str,
        new_password: str,
        email: str,
    ):
        """
        Set a new password for an existing company.
        """
        verify = OTP.verify_otp(recipient=email, otp=otp)
        if not verify.get("status"):
            return {"status": False, "message": "invalid or expired OTP."}
        else:
            company = cls.objects.filter(email=email).first()
            if company is not None:
                company.set_password(new_password)
                company.save()
                return {"status": True, "message": "password reset was successful."}
            else:
                return {"status": False, "message": "company profile does not exist."}

    @classmethod
    def update_profile(
        cls,
        company,
        address: Optional[str] = None,
        account_name_prefix: Optional[str] = None,
        callback_url: Optional[str] = None,
    ) -> object:
        """
        Update the profile of a company.
        Returns:
            Company: The updated Company instance.
        """
        company.address = address if address is not None else company.address
        company.account_name_prefix = (
            account_name_prefix
            if account_name_prefix is not None
            else company.account_name_prefix
        )
        company.callback_url = (
            callback_url if callback_url is not None else company.callback_url
        )
        company.save()
        return company


class CACDocument(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    document = models.FileField(upload_to=upload_path)

    def __str__(self) -> str:
        return self.company.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY CAC DOCUMENT"
        verbose_name_plural = "COMPANY CAC DOCUMENTS"


class SubCompany(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    company_name = models.CharField(max_length=255)
    company_phone = models.CharField(max_length=25)
    company_email = models.EmailField(max_length=255)
    company_address = models.TextField(null=True, blank=True)
    unique_id = models.CharField(max_length=225)
    account_name_prefix = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        help_text="The account name prefix for account creation (eg. 'Liberty'/Augustine Jibunoh).",
    )
    nuban = models.CharField(max_length=10, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    service_fee = models.FloatField(default=0.0)
    callback_url = models.TextField(null=True, blank=True)
    cash_connect_callback_url = models.TextField(null=True, blank=True)
    api_key = models.TextField(null=True, blank=True, editable=False)

    def __str__(self) -> str:
        return self.company_name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SUB COMPANY"
        verbose_name_plural = "SUB COMPANIES"

    @classmethod
    def register(
        cls,
        company: Company,
        company_name: str,
        company_phone: str,
        company_email: str,
        unique_id: str,
        company_address: Optional[str] = None,
        account_name_prefix: Optional[str] = None,
        callback_url: Optional[str] = None,
    ):
        from accounts.models import AccountDetail

        sub_company = cls.objects.filter(unique_id=unique_id).first()
        if sub_company is None:
            sub_company = cls.objects.create(
                company=company,
                company_name=company_name,
                company_phone=add_prefix_to_phone(company_phone),
                company_email=company_email,
                unique_id=unique_id,
                company_address=company_address,
                account_name_prefix=account_name_prefix,
                callback_url=callback_url,
            )
            account_details = AccountDetail.create_company_account(
                company_name=company_name,
                account_type=AccountType.SUB,
                company=company,
                sub_company=sub_company,
            )
            api_key = APIKey.generate_key(sub_company=sub_company)
            sub_company.nuban = account_details.account_number
            sub_company.api_key = api_key.key
            sub_company.save()
        return sub_company

    @classmethod
    def update_profile(
        cls,
        sub_company: str,
        address: Optional[str] = None,
        account_name_prefix: Optional[str] = None,
        callback_url: Optional[str] = None,
    ):
        sub_company.company_address = (
            address if address is not None else sub_company.company_address
        )
        sub_company.account_name_prefix = (
            account_name_prefix
            if account_name_prefix
            else sub_company.account_name_prefix
        )
        sub_company.callback_url = (
            callback_url if callback_url is not None else sub_company.callback_url
        )

        sub_company.save()
        return sub_company

    @classmethod
    def identify_as_user(cls, request):
        auth_key = request.META.get("HTTP_AUTHORIZATION", False)
        if not auth_key:
            return None
        user = APIKey.verify(auth_key)
        print(f"\n\n\nUSER:       {user}\n\n\n")
        if not isinstance(user, str):
            return user
        return None


class APIKey(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    key = models.CharField(max_length=255, editable=False)
    is_active = models.BooleanField(default=False)

    def __str__(self) -> str:
        if self.company is not None:
            return self.company.name
        if self.sub_company is not None:
            return self.sub_company.company_name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "API KEY"
        verbose_name_plural = "API KEYS"

    @classmethod
    def generate_key(
        cls,
        company: Optional[Company] = None,
        sub_company: Optional[SubCompany] = None,
    ):
        encryptor = CryptographyService()
        part1 = str(datetime.now())

        if company is not None:
            old_keys = APIKey.objects.filter(company=company)
            old_keys.update(is_active=False)
            part2 = company.email
            key = encryptor.encrypt(
                f"{part2}{settings.API_KEY_DELIMITER}{part1}"
            )
            key = APIKey.objects.create(
                key=key, company=company, is_active=True
            )
        if sub_company is not None:
            old_keys = APIKey.objects.filter(sub_company=sub_company)
            old_keys.update(is_active=False)
            part2 = sub_company.company_email
            key = encryptor.encrypt(
                f"{part2}{settings.API_KEY_DELIMITER}{part1}"
            )
            key = APIKey.objects.create(
                key=key, sub_company=sub_company, is_active=True
            )
        return key

    @staticmethod
    def decrypt(key):
        encryptor = CryptographyService()
        key = encryptor.decrypt(key)
        data = key.split(settings.API_KEY_DELIMITER)
        if len(data) < 2:
            return False
        else:
            return dict(email=data[0], date=data[1])

    @staticmethod
    def verify(key):
        cleaned_key = key.replace(settings.API_KEY_PREFIX, "")
        user_key = APIKey.objects.filter(
            key=cleaned_key,
            is_active=True,
        ).last()
        if user_key is None:
            return "Error: key does not exist."
        key_data = user_key.decrypt(cleaned_key)
        user_email = key_data.get("email")
        user = Company.objects.filter(
            email=user_email,
            is_active=True,
        ).last()
        if user is None:
            user = SubCompany.objects.filter(
                company_email=user_email,
                is_active=True,
            ).last()
        if user is None:
            return "Error: user not found."
        return user
