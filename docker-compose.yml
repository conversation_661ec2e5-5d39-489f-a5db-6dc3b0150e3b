version: "3.9"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    command: >
      sh -c "
            python3 manage.py makemigrations accounts cash_connect core fidelity user_profiles vfd_bank wema_bank zenith_bank one_pipe  &&
            python3 manage.py migrate &&
            python3 manage.py runserver 0.0.0.0:8000"
    env_file:
      - .env
    networks:
      - corebanking_network
    depends_on:
      - db

  db:
    image: postgres:13-alpine
    container_name: corebanking_db
    networks:
      - corebanking_network
    volumes:
      - dev-db-data:/var/lib/postgres/data
    environment:
      - POSTGRES_DB=devdb
      - POSTGRES_USER=devuser
      - POSTGRES_PASSWORD=changeme
    ports:
      - "5434:5432"

  celery:
    image: corebanking-app
    container_name: celery-service
    networks:
      - corebanking_network
    depends_on:
      - banking_redis
      - app
    env_file:
      - .env
    command: celery -A config.celery worker --loglevel=INFO --concurrency 1 -P solo

  celery-beat:
    image: corebanking-app
    container_name: celerybeat-service
    networks:
      - corebanking_network
    depends_on:
      - banking_redis
      - app
    env_file:
      - .env
    command: celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler

  banking_redis:
    image: redis:alpine
    container_name: redis_corebanking
    restart: always
    ports:
      - "6359:6379"
    command: [ "redis-server", "--appendonly", "yes" ]
    networks:
      - corebanking_network
    volumes:
      - redis_data:/data

volumes:
  dev-db-data:
  redis_data:


networks:
  corebanking_network:
    driver: bridge
