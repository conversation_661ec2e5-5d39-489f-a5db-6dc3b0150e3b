from datetime import datetime

import pandas as pd
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response as DRFResponse
from rest_framework.views import APIView

from accounts.models import TransactionDetail
from helpers.custom_permissions import (
    APIAccessPermission,
    IsSharedBearer,
    USSDIsAuthenticated,
    WemaIsAuthenticated,
)
from helpers.custom_responses import Response
from helpers.reusable import (
    Paginator,
    get_ip_address,
    is_valid_string,
)
from user_profiles.models import SubCompany
from wema_bank import models, serializers
from wema_bank.helper.api_specifications import WemaBank
from wema_bank.tasks import register_offline_accounts


# Create your view(s) here.
class WemaLookUpAPIView(APIView):
    """
    Wema Bank Plc.
    NOTE: this is a name enquiry API service;
    - account is prefixed with "842"
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [WemaIsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.AccountLookUpSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        get_account = models.VirtualAccount.retrieve_account(
            account_number=serializer.validated_data.get("accountnumber")
        )
        if get_account.get("status"):
            data = {
                "accountname": get_account.get("account_holder"),
                "status": "00",
                "status_desc": "account number found",
            }
            return DRFResponse(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                "accountname": "-",
                "status": "07",
                "status_desc": "account number does not exist",
            }
            return DRFResponse(data=data, status=status.HTTP_404_NOT_FOUND)


class WemaCallbackAPIView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [WemaIsAuthenticated]

    def post(self, request, *args, **kwargs):
        request_ip = get_ip_address(request=request)
        if request.data == {}:
            return DRFResponse(
                data={"message": "invalid request, cannot handle empty data."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        models.WemaInflowDump.dump_data(request_ip=request_ip, data=request.data)
        event = models.TransactionCallback.create_event_transaction(
            request_ip=request_ip, data=request.data
        )
        if event == False:
            data = {
                "transactionreference": None,
                "status": "00",
                "status_desc": "duplicate transaction",
            }
            return DRFResponse(data=data, status=status.HTTP_200_OK)
        if event == None:
            data = {
                "transactionreference": None,
                "status": "xx",
                "status_desc": "unexpected server error",
            }
            return DRFResponse(data=data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            data = {
                "transactionreference": event.id,
                "status": "00",
                "status_desc": "event received successfully",
            }
            return DRFResponse(data=data, status=status.HTTP_200_OK)


class VirtualAccountAPIView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = serializers.VirtualAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        account = models.VirtualAccount.create_account(
            company=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=account)
        data = {
            "message": "account created successfully.",
            "account_details": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        account_number = request.GET.get("account_number", False)
        company_virtual_accounts = models.VirtualAccount.objects.filter(
            company=request.user, is_active=True
        )
        if account_number:
            if len(account_number) < 10 or len(account_number) > 10:
                return Response(
                    errors={"message": "invalid account number."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            account_details = company_virtual_accounts.filter(
                account_number=account_number
            )
            serializer = self.serializer_class(instance=account_details, many=True)
            data = {
                "message": "successfully fetched company account details.",
                "account_details": serializer.data,
            }
        else:
            paginated_data = Paginator.paginate(
                request=request, queryset=company_virtual_accounts
            )
            serializer = self.serializer_class(instance=paginated_data, many=True)
            data = {
                "message": "successfully fetched company account details.",
                "account_details": serializer.data,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class VirtualAccountV2APIView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]
    serializer_class = serializers.VirtualAccountSerializer

    def post(self, request, *args, **kwargs):
        company = request.user
        sub_company = models.SubCompany.identify_as_user(request=request)

        if not company.is_authenticated:
            company = sub_company.company

        serializer = self.serializer_class(data=request.data, context={"version": 2})
        serializer.is_valid(raise_exception=True)
        account = models.VirtualAccount.create_account(
            company=company, sub_company=sub_company, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=account)
        data = {"message": "success.", "account_details": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class VerifyEventAPIView(APIView):
    permission_classes = [IsAuthenticated | APIAccessPermission]

    def get(self, request, *args, **kwargs):
        sub_company = models.SubCompany.identify_as_user(request=request)
        query_params = request.GET
        reference = query_params.get("reference")
        session_id = query_params.get("session_id")

        if not reference and not session_id:
            return Response(
                errors={"errors": "invalid request."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        event = TransactionDetail.verify_event(
            reference=reference, session_id=session_id, sub_company=sub_company
        )
        if event is None:
            return Response(
                data={"message": "transaction not found."},
                status_code=404,
                status=status.HTTP_404_NOT_FOUND,
            )
        return Response(data=event, status_code=200, status=status.HTTP_200_OK)


class MiniStatementAPIView(APIView):
    """
    Wema Bank Plc.
    NOTE: this is an account statement API service;
    - generates a statement of account for a specified account number
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [WemaIsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.AccountLookUpSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        account_number = serializer.validated_data.get("accountnumber")
        get_account = models.VirtualAccount.retrieve_account(
            account_number=account_number
        )
        if get_account.get("status"):
            transactions = models.TransactionCallback.get_mini_statement(
                account_number=account_number
            )
            serializer = serializers.MiniStatementTransactionSerializer(
                instance=transactions, many=True
            )
            data = {
                "transactions": serializer.data,
                "status_desc": "account number found",
            }
            return DRFResponse(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                "transactions": [],
                "status_desc": "account number not found or active",
            }
            return DRFResponse(data=data, status=status.HTTP_404_NOT_FOUND)


class KYCDetailsAPIView(APIView):
    """
    Wema Bank Plc.
    NOTE: this is a KYC API service;
    - provides customer details for a specified account number
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [WemaIsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.AccountLookUpSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        get_account = models.VirtualAccount.get_account_details(
            account_number=serializer.validated_data.get("accountnumber")
        )
        if get_account is not None:
            serializer = serializers.KYCDetailsSerializer(instance=get_account)
            return DRFResponse(data=serializer.data, status=status.HTTP_200_OK)
        else:
            data = {
                "BVN": "-",
                "mobilenumber": "-",
                "accountname": "-",
                "walletbalance": 0.0,
                "status_desc": "account number not found or active",
            }
            return DRFResponse(data=data, status=status.HTTP_404_NOT_FOUND)


class BlockAccountAPIView(APIView):
    """
    Wema Bank Plc.
    NOTE: this is an account deactivation API service;
    - deactivates customer account for a specified account number
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [WemaIsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.BlockAccountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        account = models.VirtualAccount.block_account(**serializer.validated_data)
        if account:
            data = {"message": "Account Restricted Successfully"}
        else:
            data = {"message": "Account not found"}
        return DRFResponse(data=data, status=status.HTTP_200_OK)


class VirtualAccountV3APIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.VirtualAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data, context={"version": 3})
        serializer.is_valid(raise_exception=True)
        account = models.VirtualAccount.create_account(
            company=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=account)
        data = {"message": "success.", "account_details": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class InstantAccountAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.InstantAccountSummarySerializer

    def post(self, request, *args, **kwargs):
        serializer = serializers.GetInstantAccountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        account = models.VirtualAccount.get_instant_account(
            company=request.user, **serializer.validated_data
        )
        if isinstance(account, bool):
            return Response(
                errors={"message": "duplicate request, try again."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(instance=account)
        data = {"message": "success.", "account_details": serializer.data}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class ManualWemaInflowAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        request_ip = get_ip_address(request=request)
        if request.data == {}:
            return Response(
                errors={"message": "invalid request, cannot handle empty data."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        models.WemaInflowDump.dump_data(
            request_ip=request_ip, company=request.user, data=request.data
        )
        event = models.TransactionCallback.create_event_transaction(
            request_ip=request_ip, company=request.user, data=request.data
        )
        if event == False:
            data = {
                "transactionreference": None,
                "status": "00",
                "status_desc": "duplicate transaction",
            }
            return Response(
                errors=data,
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if event == None:
            data = {
                "transactionreference": None,
                "status": "xx",
                "status_desc": "unexpected server error",
            }
            return Response(
                data=data,
                status_code=500,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        else:
            data = {
                "transactionreference": event.id,
                "status": "00",
                "status_desc": "event received successfully",
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class OfflineAccountsAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.RegisterOfflineVirtualAccountSerializer(
            data=request.data
        )
        serializer.is_valid(raise_exception=True)
        register_offline_accounts.delay(serializer.validated_data["sub_company"].id)
        return Response(
            data={"message": "account creation in progress."},
            status_code=200,
            status=status.HTTP_200_OK,
        )

    def get(self, request, *args, **kwargs):
        sub_company_unique_id = request.query_params.get("unique_id")
        if sub_company_unique_id is None or not is_valid_string(sub_company_unique_id):
            return Response(
                errors={"message": "provide a valid sub company unique ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        sub_company = SubCompany.objects.filter(unique_id=sub_company_unique_id).first()
        if sub_company is None:
            return Response(
                errors={"message": "provide a valid sub company unique ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        offline_accounts = models.VirtualAccount.objects.filter(
            sub_company=sub_company,
            offline=True,
            is_active=True,
        )
        serializer = serializers.VirtualAccountSerializer(
            instance=offline_accounts, many=True
        )
        data = {
            "offline_accounts": serializer.data,
            "count": len(serializer.data),
            "total": offline_accounts.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class USSDVerifyAPIView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [USSDIsAuthenticated]

    def get(self, request, *args, **kwargs):
        unique_code = request.query_params.get("unique_code")
        if unique_code is None:
            return Response(
                errors={"message": "provide a valid unique code."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        virtual_account = models.VirtualAccount.objects.filter(
            unique_code=unique_code,
            offline=True,
            is_active=True,
        ).first()
        if virtual_account is None:
            return Response(
                errors={"message": "provide a valid unique code."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        inflow_checker = models.TransactionCallback.objects.filter(
            recipient_account_number=virtual_account.account_number
        ).first()
        if inflow_checker is not None:
            data = {
                "status": True,
                "message": "payment received",
                "payer_name": inflow_checker.payer_account_name,
                "amount": float(inflow_checker.amount),
                "confirmation_code": virtual_account.confirmation_code,
            }
        else:
            data = {
                "status": False,
                "message": "awaiting payment",
                "payer_name": "",
                "amount": 0.0,
                "confirmation_code": "",
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class NIPStatusAPIView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsSharedBearer]

    def get(self, request, *args, **kwargs):
        session_id = request.query_params.get("session_id")
        if not session_id or not is_valid_string(session_id):
            return Response(
                errors={"message": "session ID is required"},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        # Local check
        transaction = models.TransactionCallback.objects.filter(
            session_id=session_id
        ).last()
        if transaction:
            message = "transaction verified successfully."
            response_data = {
                "originatoraccountnumber": transaction.payer_account_number,
                "amount": transaction.amount,
                "originatorname": transaction.payer_account_name,
                "narration": transaction.narration,
                "craccountname": transaction.recipient_account_name,
                "paymentreference": transaction.transaction_reference,
                "bankname": transaction.payer_bank_name,
                "sessionid": transaction.session_id,
                "craccount": transaction.recipient_account_number,
                "bankcode": transaction.payer_bank_code,
                "requestdate": transaction.paid_at,
                "nibssresponse": "00",
                "sendresponse": "",
            }
            data = {
                "message": message,
                "response_data": response_data,
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)
        if not transaction:
            handler = WemaBank()
            handler_response = handler.inflow_event_tsq(session_id)
            if not handler_response.get("status"):
                message = "transaction not found."
                response_data = {}
                data = {
                    "message": message,
                    "response_data": response_data,
                }
                return DRFResponse(data=data, status=status.HTTP_400_BAD_REQUEST)
            else:
                if handler_response.get("data").get("status") == "02":
                    message = handler_response.get("data").get("status_desc")
                    response_data = {}
                    data = {
                        "message": message,
                        "response_data": response_data,
                    }
                    return DRFResponse(data=data, status=status.HTTP_400_BAD_REQUEST)
                if handler_response.get("data").get("status") == "00":
                    transaction = handler_response.get("data").get("transactions")[0]
                    message = "transaction verified successfully."
                    response_data = {
                        "originatoraccountnumber": transaction.get(
                            "originatoraccountnumber"
                        ),
                        "amount": transaction.get("amount"),
                        "originatorname": transaction.get("originatorname"),
                        "narration": transaction.get("narration"),
                        "craccountname": transaction.get("craccountname"),
                        "paymentreference": transaction.get("paymentreference"),
                        "bankname": transaction.get("bankname"),
                        "sessionid": transaction.get("sessionid"),
                        "craccount": transaction.get("craccount"),
                        "bankcode": transaction.get("bankcode"),
                        "requestdate": transaction.get("requestdate"),
                        "nibssresponse": transaction.get("nibssresponse"),
                        "sendresponse": transaction.get("sendresponse"),
                    }
                    models.TransactionCallback.create_event_transaction(
                        request_ip="localhost",
                        data=response_data,
                    )
                    data = {
                        "message": message,
                        "response_data": response_data,
                    }
                    return Response(
                        data=data, status_code=200, status=status.HTTP_200_OK
                    )


class ManualConfirmationAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        pass
