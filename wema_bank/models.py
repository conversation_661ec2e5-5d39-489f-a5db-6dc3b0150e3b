from datetime import datetime, timedelta
import json
import random
from typing import Optional

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext as _
import pytz
from rest_framework import serializers

from core.models import BaseModel
from helpers.enums import Currency, InflowStatus
from helpers.redis_db import connect_wema_database
from helpers.reusable import add_prefix_to_phone, is_valid_string
from user_profiles.models import SubCompany
from wema_bank.helper.functions import generate_incremental_account_number


Company = settings.AUTH_USER_MODEL
logger = settings.LOGGER


# Create your model(s) here.
class VirtualAccount(BaseModel):
    """
    NOTE [ATTRIBUTES]:
    - current inflow balance; represents the aggregate of all time `CREDIT` transaction(s).
    - previous inflow balance; represents the state before the last `CREDIT` transaction.
    """

    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    sub_company = models.ForeignKey(
        SubCompany,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    first_name = models.Char<PERSON>ield(max_length=255)
    middle_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=10, unique=True)
    bvn = models.CharField(
        max_length=25,
        default=settings.MOTHER_BVN,
        null=True,
        blank=True,
    )
    current_inflow_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    previous_inflow_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    email = models.EmailField(
        null=True,
        blank=True,
        verbose_name="account holder email",
    )
    phone = models.CharField(max_length=25, null=True, blank=True)
    date_of_birth = models.CharField(max_length=25, null=True, blank=True)
    bank_name = models.CharField(max_length=255, default="Wema Bank Plc")
    bank_code = models.CharField(max_length=25, default="000017")
    account_name_prefix = models.CharField(max_length=10, default="Liberty")
    instant_settlement = models.BooleanField(
        default=False,
        help_text="represents accounts used instant settlements.",
    )
    settlement_bank_code = models.CharField(
        max_length=25,
        null=True,
        blank=True,
    )
    settlement_account_name = models.CharField(
        max_length=255,
        unique=True,
        null=True,
        blank=True,
    )
    settlement_account_number = models.CharField(
        max_length=10,
        unique=True,
        null=True,
        blank=True,
    )
    one_time = models.BooleanField(
        default=False,
        help_text="represents accounts used for one-time transactions.",
    )
    request_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="one-time account request tracker.",
    )
    request_active = models.BooleanField(
        default=False,
        help_text="indicator `True` if the one-time account is currently in use.",
    )
    offline = models.BooleanField(
        default=False,
        help_text="represents accounts used for offline transactions.",
    )
    unique_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="offline ussd unique code.",
    )
    confirmation_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="offline ussd confirmation code.",
    )
    reassignment_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    deactivate_reason = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return self.fullname

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "VIRTUAL ACCOUNT"
        verbose_name_plural = "VIRTUAL ACCOUNTS"

    @property
    def fullname(self):
        middle_name = self.middle_name if self.middle_name is not None else ""
        last_name = self.last_name if self.last_name is not None else ""
        if (
            self.sub_company is not None
            and self.sub_company.account_name_prefix is not None
        ):
            return f"{self.account_name_prefix}/{self.sub_company.account_name_prefix}/{self.first_name} {middle_name} {last_name}"
        if self.company.account_name_prefix is not None:
            return f"{self.account_name_prefix}/{self.company.account_name_prefix}/{self.first_name} {middle_name} {last_name}"
        else:
            return f"{self.account_name_prefix}/{self.first_name} {middle_name} {last_name}"

    @classmethod
    def create_account(
        cls,
        company: Company,
        first_name: str,
        last_name: Optional[str] = None,
        middle_name: Optional[str] = None,
        bvn: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        date_of_birth: Optional[str] = None,
        sub_company: Optional[SubCompany] = None,
    ) -> object:
        """
        Creates a collection account for a given company.
        Returns:
            VirtualAccount: The created VirtualAccount object.
        """
        phone_number = (
            add_prefix_to_phone(phone)
            if phone is not None else phone
        )
        company_accounts = cls.objects.filter(company=company)
        if sub_company is not None:
            company_accounts = company_accounts.filter(sub_company=sub_company)
        if email and is_valid_string(email):
            account_details = company_accounts.filter(email=email).last()
        if phone and is_valid_string(phone):
            account_details = company_accounts.filter(
                phone=phone_number
            ).last()
        if bvn and is_valid_string(bvn):
            account_details = company_accounts.filter(bvn=bvn).last()
        if account_details is None:
            account_details = cls.objects.create(
                company=company,
                first_name=first_name,
                middle_name=middle_name,
                last_name=last_name,
                account_number=generate_incremental_account_number(),
                bvn=bvn,
                email=email,
                phone=phone_number,
                date_of_birth=date_of_birth,
                sub_company=sub_company,
            )
            account_holder = account_details.fullname
            account_number = account_details.account_number
            connect_wema_database.set(account_number, account_holder)
            return account_details
        else:
            return account_details

    @ classmethod
    def get_account_details(cls, account_number: str):
        """
        Retrieve account details based on the provided account number.
        Returns:
            VirtualAccount: The VirtualAccount object if found else None.
        """
        try:
            account = cls.objects.get(
                account_number=account_number, is_active=True
            )
        except cls.DoesNotExist:
            account = None
        return account

    @ classmethod
    def retrieve_account(cls, account_number: str):
        """
        Wema Bank Account look up handler:
        NOTE [LOGIC]:
        - it first tries to fetch the account details from the Cache.
        - else, it falls back to querying the local Database.
        Returns:
            - "status" (bool): Indicates whether the account retrieval was successful (True) or not (False).
            - "account_holder" (str or None): The name of the account holder if found, or None if not found.
        """
        account = connect_wema_database.get(str(account_number))
        if account is not None:
            return {"status": True, "account_holder": account}
        account = cls.get_account_details(account_number=account_number)
        if account is not None:
            account_holder = account.fullname
            connect_wema_database.set(account_number, account_holder)
            return {"status": True, "account_holder": account_holder}
        return {"status": False, "account_holder": account}

    @ classmethod
    def block_account(cls, accountnumber: str, blockreason: str):
        """
        This method is used to deactivate a user's account.
        Args:
            accountnumber (str): The account number to be deactivated.
            blockreason (str): The reason for the account deactivation.
        Returns:
            bool: True if the operation was successful and False otherwise.
        """
        account_details = cls.get_account_details(account_number=accountnumber)
        if account_details is not None:
            # deactivate from the local Database.
            account_details.is_active = False
            account_details.deactivate_reason = blockreason
            account_details.save()
            # delete from Cache name lookup.
            connect_wema_database.delete(str(accountnumber))
            return True
        return False

    @ classmethod
    def fund(cls, nuban: str, amount: float):
        """
        It aids `CREDIT` transaction(s).

        This method now uses database row locking to prevent race conditions when multiple transactions
        attempt to update the same virtual account balance simultaneously.
        """
        from django.db import transaction

        # Use atomic transaction with row locking to prevent race conditions
        with transaction.atomic():
            # Use select_for_update() to lock the row and prevent concurrent modifications
            account_details = cls.objects.select_for_update().filter(
                account_number=nuban
            ).first()

            if account_details is None:
                return False

            current_state = float(account_details.current_inflow_balance)
            account_details.previous_inflow_balance = current_state
            account_details.current_inflow_balance = (
                models.F("current_inflow_balance") + float(amount)
            )
            account_details.save(update_fields=['current_inflow_balance', 'previous_inflow_balance'])
            return True

    @ classmethod
    def charge(cls, nuban: str, amount: float):
        """
        It aids `DEBIT` transaction(s).
        """
        charge_amount = float(amount)
        account_details = cls.get_account_details(account_number=nuban)
        if account_details is None:
            return False
        current_state = float(account_details.current_inflow_balance)
        if current_state >= charge_amount:
            account_details.previous_inflow_balance = current_state
            account_details.current_inflow_balance = (
                models.F("current_inflow_balance") - charge_amount
            )
            account_details.save()
            return True
        return False

    @classmethod
    def get_instant_account(
        cls,
        company: Company,
        request_reference: str,
        sub_company: Optional[SubCompany] = None,
    ) -> object:
        """
        Assigns a one time collection account for a given company.
        Returns:
            VirtualAccount: The assigned VirtualAccount instance.
        """
        instant_virtual_account = cls.objects.filter(
            request_reference=request_reference
        ).first()
        if instant_virtual_account is not None:
            return False
        company_instant_accounts = cls.objects.filter(
            company=company,
            one_time=True,
            request_active=False,
        )
        if not company_instant_accounts.exists():
            return "Company is not yet provisioned for this service."
        instant_virtual_account = random.choice(company_instant_accounts)
        instant_virtual_account.sub_company = sub_company
        instant_virtual_account.request_reference = request_reference
        instant_virtual_account.request_active = True
        instant_virtual_account.reassignment_date = datetime.now(
            tz=pytz.timezone(settings.TIME_ZONE)
        )
        instant_virtual_account.save()
        return instant_virtual_account


class TransactionCallback(BaseModel):
    """
    Event received and created successfully will be verified before processing.
    NOTE:
    - under normal circumstances all the three (3) steps and the 'is_resolved'
    flag should be True.

    - if TSQ is bypassed, 'step_two' will be False and the 'is_resolved' flag
    will be True.
    """

    request_ip = models.CharField(max_length=25, null=True, blank=True)
    step_one = models.BooleanField(
        default=False,
        editable=False,
        help_text="Event received flag.",
    )
    step_two = models.BooleanField(
        default=False,
        editable=False,
        help_text="Event verified flag.",
    )
    step_three = models.BooleanField(
        default=False,
        editable=False,
        help_text="Event sent flag (notified inflow owner).",
    )
    is_resolved = models.BooleanField(
        default=False,
        editable=False,
        help_text="Inflow has been settled locally.",
    )
    tsq_count = models.PositiveIntegerField(
        default=0,
        editable=False,
        help_text="transaction query event count.",
    )
    status = models.CharField(
        max_length=25,
        choices=InflowStatus.choices,
        default=InflowStatus.UNCONFIRMED,
    )
    session_id = models.CharField(max_length=255, unique=True)
    recipient_account_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    recipient_account_number = models.CharField(
        max_length=10,
        null=True,
        blank=True,
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    payer_account_name = models.CharField(
        max_length=225, null=True, blank=True
    )
    payer_account_number = models.CharField(
        max_length=225, null=True, blank=True
    )
    payer_bank_code = models.CharField(max_length=25, null=True, blank=True)
    payer_bank_name = models.CharField(max_length=225, null=True, blank=True)
    paid_at = models.CharField(max_length=225, null=True, blank=True)
    narration = models.TextField(null=True, blank=True)
    transaction_reference = models.CharField(
        max_length=225,
        null=True,
        blank=True,
    )
    currency = models.CharField(
        max_length=3,
        choices=Currency.choices,
        default=Currency.NGN,
    )
    request_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="onetime instant account request tracker.",
    )
    unique_code = models.CharField(
        max_length=8,
        null=True,
        blank=True,
        help_text="onetime offline ussd unique code.",
    )
    confirmation_code = models.CharField(
        max_length=6,
        null=True,
        blank=True,
        help_text="onetime offline ussd confirmation code.",
    )
    payload = models.TextField()
    confirmed_at = models.DateTimeField(null=True, blank=True)
    confirmation_payload = models.TextField(null=True, blank=True)
    failed_tsq_payload = models.TextField(null=True, blank=True)
    re_verified = models.BooleanField(default=False)
    last_confirmed_at = models.DateTimeField(null=True, blank=True)
    last_confirmation_payload = models.TextField(null=True, blank=True)
    dumped_by = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "VIRTUAL ACCOUNT INFLOW"
        verbose_name_plural = "VIRTUAL ACCOUNT INFLOWS"

    @classmethod
    def get_mini_statement(cls, account_number: str):
        """
        Statement of account generator.
        """
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        start_date = TODAY - timedelta(days=10)
        end_date = TODAY + timedelta(days=1)
        transactions = cls.objects.filter(
            recipient_account_number=account_number,
            created_at__range=[start_date, end_date],
        )
        return transactions

    @classmethod
    def create_event_transaction(
        cls,
        request_ip: str,
        data: json,
        company: Optional[Company] = None,
    ):
        """
        NOTE:
        - session_id (str): this value must be unique for all transaction(s).
        - transaction (instance): if created successfully will be processed accordingly.
        """
        from accounts.tasks import handle_wema_company_collection

        session_id = data.get("sessionid")
        if not is_valid_string(session_id):
            raise serializers.ValidationError(
                {"message": "provide a valid session ID."}
            )
        if cls.objects.filter(session_id=session_id).exists():
            return False
        try:
            transaction = cls.objects.create(
                request_ip=request_ip,
                step_one=True,
                recipient_account_number=data.get("craccount"),
                recipient_account_name=data.get("craccountname"),
                amount=float(data.get("amount")),
                payer_account_name=data.get("originatorname"),
                payer_account_number=data.get("originatoraccountnumber"),
                payer_bank_code=data.get("bankcode"),
                payer_bank_name=data.get("bankname"),
                paid_at=data.get("created_at"),
                narration=data.get("narration"),
                transaction_reference=data.get("paymentreference"),
                session_id=session_id,
                currency=data.get("currency", "NGN"),
                payload=json.dumps(data),
                dumped_by=company,
            )
            handle_wema_company_collection.delay(inflow_id=transaction.id)
        except Exception as error_occurred:
            logger.error(str(error_occurred))
            transaction = None
        return transaction


class ConstantVariable(BaseModel):
    nuban = models.CharField(max_length=7, default="0038519")
    use_bvn = models.BooleanField(default=False)
    process_inflow = models.BooleanField(default=True)
    bypass_tsq = models.BooleanField(default=False)
    notify_email = models.TextField(null=True, blank=True)
    cashconnect_disbursement_account = models.CharField(max_length=200, default="**********")

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CONSTANT VARIABLE"
        verbose_name_plural = "CONSTANT VARIABLES"

    @classmethod
    def get_account_number(cls):
        account_prefix = "842"
        account_number = cls.objects.first()
        if account_number is None:
            account_number = cls.objects.create()
            account_number.nuban = str(int(account_number.nuban) + 1)
        else:
            account_number.nuban = str(int(account_number.nuban) + 1)
        account_number.save()
        if settings.ENVIRONMENT == "production":
            account_number = str(account_number.nuban).zfill(7)
        else:
            account_number = f"""{"".join("x" for _ in range(7 - len(account_number.nuban)))}{account_number.nuban}"""

        return f"{account_prefix}{account_number}"


class SourceAccountBalance(BaseModel):
    opening_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )
    closing_balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.0,
        validators=[MinValueValidator(0.0)],
    )

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SOURCE ACCOUNT BALANCE"
        verbose_name_plural = "SOURCE ACCOUNT BALANCES"


class WemaInflowDump(BaseModel):
    is_duplicate = models.BooleanField(default=False)
    is_resolved = models.BooleanField(default=False)
    request_ip = models.CharField(max_length=25, null=True, blank=True)
    session_id = models.CharField(max_length=255, null=True, blank=True)
    data = models.TextField(null=True, blank=True)
    dumped_by = models.ForeignKey(
        Company,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "WEMA INFLOW DUMP"
        verbose_name_plural = "WEMA INFLOW DUMPS"

    @classmethod
    def dump_data(
        cls,
        request_ip: str,
        data: json,
        company: Optional[Company] = None,
    ):
        session_id = data.get("sessionid")
        dump = cls.objects.create(
            request_ip=request_ip,
            session_id=session_id,
            data=json.dumps(data),
            dumped_by=company,
        )
        return dump
