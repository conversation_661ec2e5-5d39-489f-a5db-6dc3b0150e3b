from import_export import resources

from wema_bank import models


# Register your resources(s) here.
class VirtualAccountResource(resources.ModelResource):
    class Meta:
        model = models.VirtualAccount


class TransactionCallbackResource(resources.ModelResource):
    class Meta:
        model = models.TransactionCallback


class ConstantVariableResource(resources.ModelResource):
    class Meta:
        model = models.ConstantVariable


class SourceAccountBalanceResource(resources.ModelResource):
    class Meta:
        model = models.SourceAccountBalance


class WemaInflowDumpResource(resources.ModelResource):
    class Meta:
        model = models.WemaInflowDump
