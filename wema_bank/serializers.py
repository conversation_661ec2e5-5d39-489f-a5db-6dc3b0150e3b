from rest_framework import serializers

from helpers.reusable import is_valid_string
from user_profiles.models import SubCompany
from wema_bank import models


# Create your serializer(s) here.
class VirtualAccountSerializer(serializers.ModelSerializer):
    phone = serializers.CharField(
        max_length=25,
        required=False,
        allow_null=True,
        allow_blank=True,
    )

    class Meta:
        model = models.VirtualAccount
        exclude = [
            "current_inflow_balance",
            "previous_inflow_balance",
            "is_active",
            "deactivate_reason",
            "instant_settlement",
            "settlement_bank_code",
            "settlement_account_name",
            "settlement_account_number",
            "one_time",
            "request_reference",
            "request_active",
            "offline",
            "unique_code",
            "confirmation_code",
            "reassignment_date",
        ]
        read_only_fields = [
            "company",
            "account_number",
        ]

    def validate(self, attrs):
        if not attrs.get("email") and not attrs.get("phone"):
            raise serializers.ValidationError(
                {
                    "errors": "account creation must include either account holder 'email' or 'phone'."
                }
            )
        if self.context.get("version") == 2:
            check_constraint = models.ConstantVariable.objects.first()
            if check_constraint is not None:
                if check_constraint.use_bvn:
                    if not attrs.get("bvn"):
                        raise serializers.ValidationError(
                            {"errors": "account creation must include the field 'bvn'."}
                        )
                    return super().validate(attrs)
                return super().validate(attrs)
            return super().validate(attrs)
        if self.context.get("version") == 3:
            if not attrs.get("sub_company"):
                raise serializers.ValidationError(
                    {"errors": "account creation must include the Sub-Company ID."}
                )
            return super().validate(attrs)
        return super().validate(attrs)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company_name"] = instance.company.name
        representation["sub_company_name"] = (
            instance.sub_company.company_name
            if instance.sub_company is not None
            else None
        )
        return representation


class AccountLookUpSerializer(serializers.Serializer):
    accountnumber = serializers.CharField(
        max_length=10, min_length=10
    )


class TransactionCallbackSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.TransactionCallback
        fields = "__all__"


class KYCDetailsSerializer(serializers.Serializer):
    BVN = serializers.CharField(source="bvn")
    mobilenumber = serializers.CharField(source="phone")

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["accountname"] = instance.fullname
        representation["walletbalance"] = 0.0
        representation["status_desc"] = "Active"
        return representation


class BlockAccountSerializer(AccountLookUpSerializer):
    blockreason = serializers.CharField()


class MiniStatementTransactionSerializer(serializers.Serializer):
    transactionDate = serializers.DateTimeField(source="created_at")
    accountNo = serializers.CharField(source="recipient_account_number")
    amount = serializers.DecimalField(max_digits=12, decimal_places=2)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["bankName"] = "WEMA BANK"
        representation["direction"] = "Credit"
        return representation


class GetInstantAccountSerializer(serializers.Serializer):
    request_reference = serializers.CharField(max_length=255)
    sub_company_unique_id = serializers.CharField(
        max_length=255, required=False)

    def validate(self, attrs):
        unique_id = attrs.get("sub_company_unique_id")
        if unique_id and is_valid_string(unique_id):
            sub_company = SubCompany.objects.filter(
                unique_id=unique_id).first()
            if sub_company is None:
                raise serializers.ValidationError(
                    {"errors": "invalid sub-company unique ID."}
                )
            attrs["sub_company"] = sub_company
            attrs.pop("sub_company_unique_id")
        return super().validate(attrs)


class InstantAccountSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.VirtualAccount
        fields = [
            "bank_name",
            "bank_code",
            "account_number",
            "one_time",
            "request_reference",
            "request_active",
            "company",
            "sub_company",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["account_name"] = instance.fullname
        representation["company"] = instance.company.name
        representation["sub_company"] = (
            instance.sub_company.company_name
            if instance.sub_company is not None
            else None
        )
        return representation


class RegisterOfflineVirtualAccountSerializer(serializers.Serializer):
    sub_company_unique_id = serializers.CharField(max_length=255)

    def validate(self, attrs):
        unique_id = attrs.get("sub_company_unique_id")
        sub_company = SubCompany.objects.filter(unique_id=unique_id).first()
        if sub_company is None:
            raise serializers.ValidationError(
                {"errors": "invalid sub-company unique ID."}
            )
        attrs["sub_company"] = sub_company
        attrs.pop("sub_company_unique_id")
        return super().validate(attrs)
