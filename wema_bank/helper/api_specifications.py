from base64 import b64encode, b64decode
from datetime import <PERSON><PERSON><PERSON>
import json

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from django.conf import settings

from helpers.redis_db import connect_wema_database
from helpers.reusable import make_request
from wema_bank.helper.functions import vpn_tunnel_up


WEMA_INFLOW_TSQ = "https://apps3.wemabank.com/FintechTransQuery/api/v1/Trans/TransQuery"
WEMA_INFLOW_TSQ_ETRANZACT = (
    "https://apps3.wemabank.com/eTzTransQuery/api/v1/Trans/EtzTransQuery"
)


# Possible value(s) of response from the provider.
response_code_status = {
    "00": "Approved or completed successfully",
    "01": "Status unknown, please wait for settlement report",
    "03": "Invalid Sender",
    "05": "Do not honor",
    "06": "Dormant Account",
    "07": "Invalid Account",
    "08": "Account Name Mismatch",
    "09": "Request processing in progress",
    "12": "Invalid transaction",
    "13": "Invalid Amount",
    "14": "Invalid Batch Number",
    "15": "Invalid Session or Record ID",
    "16": "Unknown Bank Code",
    "17": "Invalid Channel",
    "18": "Wrong Method Call",
    "21": "No action taken",
    "19": "Payment Reference cannot be found",
    "20": "Transaction in progress. Awaits Confirmation from NIBSS",
    "22": "Error Occurred Getting Transaction Status",
    "23": "Payment Reference Exist. Transaction under Investigation",
    "25": "Unable to locate record",
    "26": "Duplicate record",
    "30": "Format error",
    "31": "Unable to do TSQ",
    "32": "Transaction Failed. Resend with new Payment Reference No",
    "34": "Suspected fraud",
    "35": "Contact sending bank",
    "33": "Exceptions raised during Name Enquiry",
    "36": "Unable to call main Fund Transfer Service",
    "37": "Unable to call main Transaction Status Service",
    "38": "Unable to call main Name Enquiry Service",
    "39": "Unable to call main Bank List Service",
    "51": "No sufficient funds",
    "57": "Transaction not permitted to sender",
    "58": "Transaction not permitted on channel",
    "61": "Transfer limit Exceeded",
    "63": "Security violation",
    "65": "Exceeds withdrawal frequency",
    "68": "Response received too late",
    "69": "Unsuccessful Account/Amount block",
    "70": "Unsuccessful Account/Amount unblock",
    "71": "Empty Mandate Reference Number",
    "91": "Beneficiary Bank not available",
    "92": "Routing error",
    "94": "Duplicate transaction",
    "96": "System malfunction",
    "97": "Timeout waiting for response from destination",
}


# Wema Bank Restful API technical specification(s).
class WemaBank:
    """
    Wema Bank Restful API Technical Specification.
    The requests and responses are encrypted with AES(AES/CBC/PKCS5Padding) algorithm.
    NOTE:
    - This web service supports only REST requests.
    - The headers must contain the VendorID value.
    - All requests are expected to be encrypted and responses decrypted.
    """

    # Shared secret.
    IV = settings.WEMA_BANK_AES_IV
    KEY = settings.WEMA_BANK_AES_KEY

    base_url = settings.WEMA_BASE_URL
    password = settings.WEMA_PASSWORD
    source_account = settings.WEMA_SOURCE_ACCOUNT
    user_id = settings.WEMA_USER_ID
    username = settings.WEMA_USERNAME
    vendor_id = settings.WEMA_VENDOR_ID

    headers = {
        "Content-Type": "application/json",
        "VendorID": vendor_id,
    }

    @classmethod
    def encrypt(cls, data: dict):
        """
        Encrypts the input data using AES encryption with the provided KEY and IV.
        Args:
            data (dict): The data to be encrypted.
        Returns:
            str: The base64-encoded cipher text after encryption.
        NOTE:
        - This method uses AES encryption in Cipher Block Chaining (CBC) mode with a specified KEY and IV.
        - The input data is first padded to match the block size of AES before encryption.
        - The result is base64-encoded to ensure it is represented as a string.
        Raises:
            ValueError: If the input data cannot be encoded as UTF-8 or if encryption fails.
        """
        cipher = AES.new(cls.KEY.encode("utf-8"), AES.MODE_CBC, cls.IV.encode("utf-8"))
        cipher_bytes = cipher.encrypt(pad(str(data).encode("utf-8"), AES.block_size))
        cipher_text = b64encode(cipher_bytes).decode("utf-8")
        return cipher_text

    @classmethod
    def decrypt(cls, data: str):
        """
        Decrypts the input cipher text using AES decryption with the provided KEY and IV.
        Args:
            data (str): The base64-encoded cipher text to be decrypted.
        Returns:
            str: The decrypted plaintext as a UTF-8 encoded string.
        NOTE:
        - This method uses AES decryption in Cipher Block Chaining (CBC) mode with a specified KEY and IV.
        - The input cipher text is base64-decoded before decryption.
        - The result is UTF-8 decoded to obtain the original plaintext.
        Raises:
            ValueError: If decryption fails, or if the input data is not a valid base64-encoded string.
        """
        cipher = AES.new(cls.KEY.encode("utf-8"), AES.MODE_CBC, cls.IV.encode("utf-8"))
        plain_text = unpad(cipher.decrypt(b64decode(data)), AES.block_size).decode(
            "utf-8"
        )
        return plain_text

    @classmethod
    def authenticate(cls):
        """
        Sends a POST request to the authentication endpoint.
        Returns:
            dict: A JSON response containing the authentication result.
        """
        absolute_url = f"{cls.base_url}/Authentication/authenticate/"
        payload = json.dumps({"username": cls.username, "password": cls.password})
        tunnel_up = vpn_tunnel_up()
        if tunnel_up:
            response = make_request(
                "POST",
                dict(
                    url=absolute_url,
                    headers=cls.headers,
                    data=payload,
                    verify=False,
                ),
            )
        else:
            response = None
        return response

    @classmethod
    def login(cls):
        """
        Store Token in RedisStorage for ease of access.
        NOTE:
        - The stored Token is valid for twenty-four (24) hours.
        """
        token = connect_wema_database.get("WEMA_TOKEN")
        if token is None:
            wema_authenticator = cls.authenticate()
            if wema_authenticator is None:
                return None
            if wema_authenticator.get("status") and not isinstance(
                wema_authenticator.get("data"), str
            ):
                token = wema_authenticator.get("data").get("token")
                connect_wema_database.set(
                    "WEMA_TOKEN", token, ex=timedelta(seconds=86400)
                )
                return token
            else:
                return None
        return token

    @classmethod
    def nip_fund_transfer(
        cls,
        account_name: str,
        account_number: str,
        bank_code: str,
        narration: str,
        reference_id: str,
        amount: int,
    ):
        """
        This method initiates a fund transfer request for processing.
        NOTE:
        - The payment reference should be unique per transaction.
        - Sample decrypted response for fund request below:
            "00|000017231117132007224212207238"
        """
        absolute_url = f"{cls.base_url}/WMServices/NIPFundTransfer"
        data = {
            "myAccountName": account_name,
            "myDestinationAccountNumber": account_number,
            "myDestinationBankCode": bank_code,
            "myNarration": narration,
            "myPaymentReference": reference_id,
            "myAmount": int(amount),
            "myOriginatorName": cls.vendor_id,
            "sourceAccountNo": cls.source_account,
        }
        encrypted_request = cls.encrypt(data=data)
        bearer_token = cls.login()
        if bearer_token is None:
            return {
                "status": False,
                "message": "Wema Bearer Token not found.",
                "wema_response": None,
                "request_response": None,
            }
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps({"fundTransferRequest": encrypted_request})
        tunnel_up = vpn_tunnel_up()
        if tunnel_up:
            response = make_request(
                "POST",
                dict(
                    url=absolute_url,
                    headers=cls.headers,
                    data=payload,
                    verify=False,
                ),
            )
            if response.get("status") and isinstance(response.get("data"), str):
                try:
                    decrypted_response = cls.decrypt(response.get("data"))
                except ValueError:
                    decrypted_response = {
                        "message": "response could not be decrypted",
                        "response": response.get("data"),
                    }
                return {
                    "status": True,
                    "message": "Request completed successfully.",
                    "wema_response": decrypted_response,
                    "request_response": response,
                }
            return {
                "status": False,
                "message": "Request could not be completed.",
                "wema_response": None,
                "request_response": response,
            }
        else:
            return {
                "status": False,
                "message": "Request cannot be processed at this time.",
                "wema_response": None,
                "request_response": None,
            }

    @classmethod
    def get_transaction_status(cls, reference: str):
        """
        This method is used to query the status of fund transfer request.
        Args:
            reference (str): The transaction reference associated with the fund transfer request.
        NOTE:
        - Sample decrypted response for transaction status below:
            {
                "TransactionReference": "7116fccd-03e6-4350-b2ec-b51fd7ac4616,
                "Response": "00|000017231117132007224212207238",
                "TransactionType": "NIP-InterBank",
                "DatePosted": "11/17/2023 1:20:07 PM"
            }
        """
        absolute_url = f"{cls.base_url}/WMServices/GetTransactionStatus"
        encrypted_request = cls.encrypt(data=reference)
        bearer_token = cls.login()
        if bearer_token is None:
            return {
                "status": False,
                "message": "Wema Bearer Token not found.",
                "wema_response": None,
                "request_response": None,
            }
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps({})
        tunnel_up = vpn_tunnel_up()
        if tunnel_up:
            response = make_request(
                "GET",
                dict(
                    url=f"{absolute_url}?TransactionReference={encrypted_request}",
                    headers=cls.headers,
                    data=payload,
                    verify=False,
                ),
            )
            if response.get("status") and isinstance(response.get("data"), str):
                if response.get("data") == "Transaction Reference not found":
                    return {
                        "status": True,
                        "message": "Request completed successfully.",
                        "wema_response": str(response.get("data")),
                        "request_response": response,
                    }
                decrypted_response = cls.decrypt(response.get("data"))
                return {
                    "status": True,
                    "message": "Request completed successfully.",
                    "wema_response": decrypted_response,
                    "request_response": response,
                }
            return {
                "status": False,
                "message": "Request could not be completed.",
                "wema_response": None,
                "request_response": response,
            }
        else:
            return {
                "status": False,
                "message": "Request cannot be processed at this time.",
                "wema_response": None,
                "request_response": None,
            }

    @classmethod
    def nip_outward_tsq(cls, session_id: str):
        """
        This method is used to query the status of fund transfer request.
        Args:
            session_id (str): The session ID associated with the fund transfer request.
        """
        absolute_url = f"{cls.base_url}/WMServices/NIPOutwardTSQ"
        encrypted_request = cls.encrypt(data=session_id)
        bearer_token = cls.login()
        if bearer_token is None:
            return {
                "status": False,
                "message": "Wema Bearer Token not found.",
                "wema_response": None,
                "request_response": None,
            }
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps({})
        tunnel_up = vpn_tunnel_up()
        if tunnel_up:
            response = make_request(
                "GET",
                dict(
                    url=f"{absolute_url}?SessionID={encrypted_request}",
                    headers=cls.headers,
                    data=payload,
                    verify=False,
                ),
            )
            if response.get("status") and isinstance(response.get("data"), str):
                decrypted_response = cls.decrypt(response.get("data"))
                return {
                    "status": True,
                    "message": "Request completed successfully.",
                    "wema_response": decrypted_response,
                    "request_response": response,
                }
            return {
                "status": False,
                "message": "Request could not be completed.",
                "wema_response": None,
                "request_response": response,
            }
        else:
            return {
                "status": False,
                "message": "Request cannot be processed at this time.",
                "wema_response": None,
                "request_response": None,
            }

    @classmethod
    def get_balance(cls):
        """
        This method gets the balance on the account profiled for the vendor.
        NOTE:
        - The decrypted response should show account balance in figures.
        """
        absolute_url = f"{cls.base_url}/WMServices/GetBalance"
        encrypted_request = cls.encrypt(data=cls.source_account)
        bearer_token = cls.login()
        if bearer_token is None:
            return {
                "status": False,
                "message": "Wema Bearer Token not found.",
                "wema_response": None,
                "request_response": None,
            }
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps({})
        tunnel_up = vpn_tunnel_up()
        if tunnel_up:
            response = make_request(
                "GET",
                dict(
                    url=f"{absolute_url}?AccountNumber={encrypted_request}",
                    headers=cls.headers,
                    data=payload,
                    verify=False,
                ),
            )
            if response.get("status") and isinstance(response.get("data"), str):
                decrypted_response = cls.decrypt(response.get("data"))
                return {
                    "status": True,
                    "message": "Request completed successfully.",
                    "wema_response": decrypted_response,
                    "request_response": response,
                }
            return {
                "status": False,
                "message": "Request could not be completed.",
                "wema_response": None,
                "request_response": response,
            }
        else:
            return {
                "status": False,
                "message": "Request cannot be processed at this time.",
                "wema_response": None,
                "request_response": None,
            }

    @classmethod
    def inflow_event_tsq(cls, session_id: str):
        """
        This method is used to query the status of an inflow event.
        """
        if session_id.startswith("09FG"):
            url = WEMA_INFLOW_TSQ_ETRANZACT
        else:
            url = WEMA_INFLOW_TSQ
        response = make_request(
            "POST",
            dict(
                url=url,
                headers=cls.headers,
                data=json.dumps({"sessionid": session_id}),
            ),
        )
        return response
