import random
import re
import subprocess


# Wema Bank account number generator.
def generate_incremental_account_number():
    """
    Generates an incremental account number by retrieving the base value from a database,
    incrementing it, and updating the database with the new base value.
    Returns:
        int: The incremented account number base value.
    """
    from wema_bank.models import ConstantVariable

    account_number = ConstantVariable.get_account_number()
    return account_number


def vpn_tunnel_up():
    """
    Check the status of the VPN tunnel before making a request via the gateway.
    Returns:
        bool: True if tunnel up, False if otherwise.
    """
    # Pattern to match the gateway available time.
    # pattern = r"ESTABLISHED (\d+) seconds ago" (old pattern before permanent fix).
    # new pattern defined 2024-06-01
    pattern = r"ESTABLISHED"
    try:
        status = subprocess.check_output(
            ["ipsec", "status", "wema-bank-ltd"],
            stderr=subprocess.STDOUT,
        ).decode()
    except subprocess.CalledProcessError as error:
        status = None
    if status == None:
        return False
    if status == "no match":
        return False
    match = re.search(pattern, status)
    if match:
        return True
        # time = int(match.group(1))
        # if time <= 35:
        #     return True
        # return False
    return False


def generate_unique_code(code_length: int = 8):
    """
    Generate a random unique code of desired length.
    NOTE: defaults to 8 digits
    :param code_length: Length of the unique code
    :return: A string representing the unique code
    """
    digits = "**********"
    return "".join(random.choices(digits, k=code_length))
