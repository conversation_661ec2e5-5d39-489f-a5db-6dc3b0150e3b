from django.contrib import admin
from django.db.models.query import QuerySet
from import_export.admin import ImportExportModelAdmin

from wema_bank import models, resources


# Register your model(s) here.
class VirtualAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.VirtualAccountResource
    autocomplete_fields = [
        "company",
        "sub_company",
    ]
    search_fields = [
        "company__email",
        "sub_company__company_email",
        "first_name",
        "middle_name",
        "last_name",
        "bvn",
        "phone",
        "email",
        "account_number",
        "request_reference",
        "unique_code",
        "confirmation_code",
    ]
    list_filter = [
        "created_at",
        "is_active",
        "instant_settlement",
        "one_time",
        "offline",
        "request_active",
        "company",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionCallbackResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionCallbackResource
    search_fields = [
        "id",
        "recipient_account_number",
        "payer_account_number",
        "transaction_reference",
        "session_id",
    ]
    list_filter = [
        "created_at",
        "step_one",
        "step_two",
        "step_three",
        "status",
        "currency",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    @admin.action(description="WEMA CREDITS: process inflow transaction")
    def process_inflow(
        self,
        request,
        queryset: QuerySet[models.TransactionCallback]
    ):
        from accounts.tasks import handle_wema_company_collection

        for obj in queryset:
            handle_wema_company_collection.delay(obj.id)

    actions = [
        process_inflow
    ]


class ConstantVariableResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ConstantVariableResource
    search_fields = []
    list_filter = ()
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SourceAccountBalanceAdmin(ImportExportModelAdmin):
    resource_class = resources.SourceAccountBalanceResource
    search_fields = []
    list_filter = [
        "created_at",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WemaInflowDumpAdmin(ImportExportModelAdmin):
    resource_class = resources.WemaInflowDumpResource
    search_fields = [
        "session_id",
    ]
    list_filter = [
        "created_at",
        "is_resolved",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.VirtualAccount, VirtualAccountResourceAdmin)
admin.site.register(
    models.TransactionCallback, TransactionCallbackResourceAdmin
)
admin.site.register(models.ConstantVariable, ConstantVariableResourceAdmin)
admin.site.register(models.SourceAccountBalance, SourceAccountBalanceAdmin)
admin.site.register(models.WemaInflowDump, WemaInflowDumpAdmin)
