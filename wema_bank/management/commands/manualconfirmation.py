# from datetime import datetime
# import json

# from django.conf import settings
# from django.core.management.base import BaseCommand
# import pytz

# from accounts.models import AccountDetail, TransactionDetail
# from helpers import enums
# from wema_bank.models import VirtualAccount, TransactionCallback
# from wema_bank.tasks import send_company_transaction_callbacks


# class Command(BaseCommand):
#     help = "MANUAL CONFIRMATION OF INFLOW."

#     def handle(self, *args, **kwargs):
#         available_keys = manual_tsq_data.keys()

#         # Fetch all unconfirmed inflows.
#         unconfirmed_inflows = TransactionCallback.objects.filter(
#              status=enums.InflowStatus.UNCONFIRMED
#         )
#         if unconfirmed_inflows.exists():
#             for key in available_keys:
#                 available_data = manual_tsq_data.get(key)
#                 transaction = available_data.get("transactions")[0]
#                 available_amount = float(transaction.get("amount"))
#                 # get associated callbacks
#                 try:
#                     inflow_transaction = unconfirmed_inflows.get(
#                         session_id=key
#                     )
#                 except TransactionCallback.DoesNotExist:
#                     inflow_transaction = None

#                 if inflow_transaction is None:
#                     pass
#                 else:
#                     amount_received = float(inflow_transaction.amount)
#                     if amount_received != available_amount:
#                         pass
#                     else:
#                         data = {
#                             "payload" : available_data,
#                             "additional_info": "MANUAL CONFIRMATION",
#                         }
#                         inflow_transaction.status = enums.InflowStatus.CONFIRMED
#                         inflow_transaction.confirmation_payload = json.dumps(data)
#                         inflow_transaction.confirmed_at = datetime.now(
#                             tz=pytz.timezone(settings.TIME_ZONE)
#                         )
#                         inflow_transaction.save()
#                         # Fetch virtual account details.
#                         account_details = VirtualAccount.get_account_details(
#                             account_number=inflow_transaction.recipient_account_number
#                         )
#                         if account_details is None:
#                             pass
#                         else:
#                             # Update the virtual account current_inflow balance.
#                             VirtualAccount.fund(
#                                 nuban=inflow_transaction.recipient_account_number,
#                                 amount=amount_received,
#                             )
#                             # Identify the company's/sub-company's transaction details.
#                             company = account_details.company
#                             if account_details.sub_company is not None:
#                                 sub_company = account_details.sub_company
#                                 charges = float(sub_company.service_fee)
#                                 account_type = enums.AccountType.SUB
#                             else:
#                                 sub_company = None
#                                 charges = float(company.service_fee)
#                                 account_type = enums.AccountType.MAIN
#                             # Instantiate the transaction record.
#                             credit_transaction = TransactionDetail.register_transaction(
#                                 company=company,
#                                 beneficiary_account_number=inflow_transaction.recipient_account_number,
#                                 beneficiary_account_name=inflow_transaction.recipient_account_name,
#                                 amount=amount_received,
#                                 fee=charges,
#                                 amount_payable=amount_received - charges,
#                                 transaction_type=enums.TransactionType.CREDIT,
#                                 transaction_status=enums.TransactionStatus.SUCCESSFUL,
#                                 narration=inflow_transaction.narration,
#                                 service_provider=enums.ServiceProvider.WEMA_BANK,
#                                 mode=enums.APIMode.LIVE,
#                                 session_id=key,
#                                 bank_code=inflow_transaction.payer_bank_code,
#                                 bank_name=inflow_transaction.payer_bank_name,
#                                 source_account=inflow_transaction.payer_account_number,
#                                 is_verified=True,
#                                 sub_company=sub_company,
#                             )
#                             # Update the associated wallet.
#                             AccountDetail.fund_account(
#                                 company=company,
#                                 account_type=account_type,
#                                 amount=amount_received,
#                                 charges=charges,
#                                 sub_company=sub_company,
#                             )
#                             send_company_transaction_callbacks.delay(
#                                 transaction_id=credit_transaction.id
#                             )
#             print("THE JOB IS DONE  !!!!!!!")
