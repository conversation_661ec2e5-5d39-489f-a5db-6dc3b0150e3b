import time

from django.conf import settings
from django.core.management.base import (
    <PERSON>Command,
    CommandError,
    CommandParser,
)

from user_profiles.models import Company
from wema_bank.models import VirtualAccount


class Command(BaseCommand):
    help = "DEACTIVATE DUPLICATE ACCOUNTS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("company_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        MOTHER_BVN = settings.MOTHER_BVN
        company = Company.objects.filter(id=kwargs["company_id"]).first()
        print(f"\n\nCURRENT COMPANY:    {company}\n\n")
        if company is None:
            raise CommandError("NO RECORD FOUND FOR THE COMPANY ID SUPPLIED.")
        company_virtual_accounts = VirtualAccount.objects.filter(company=company)
        print(f"\n\nCOMPANY VIRTUAL ACCOUNTS:   {company_virtual_accounts.count()}\n\n")
        company_bvns = company_virtual_accounts.values_list("bvn", flat=True)
        # Distinct only please.
        users_bvn = []
        [users_bvn.append(bvn) for bvn in company_bvns if bvn not in users_bvn]
        print(f"\n\nUSERS' BVN:   {len(users_bvn)}\n\n")
        for user_bvn in users_bvn:
            virtual_accounts = company_virtual_accounts.filter(bvn=user_bvn)
            if virtual_accounts.exists():
                for virtual_account in virtual_accounts:
                    print(f"\n\nUSER VIRTUAL ACCOUNT: {virtual_account}")
                    time.sleep(2)
                    if virtual_account.current_inflow_balance > 0.0:
                        pass
                    else:
                        virtual_account.is_active = False
                        virtual_account.deactivate_reason = "duplicate record"
                        virtual_account.save()
