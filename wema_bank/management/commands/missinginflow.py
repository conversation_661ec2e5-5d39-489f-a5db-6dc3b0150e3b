from django.conf import settings
from django.core.management.base import BaseCommand
import openpyxl
import os
import pandas as pd

from wema_bank.models import TransactionCallback


FILE = os.path.join(settings.BASE_DIR, "liberty_assured.xlsx")


class Command(BaseCommand):
    help = "IDENTIFY MISSING INFLOW FROM EXCEL SHEET."

    def handle(self, *args, **kwargs):
        # Read Excel sheet for confirmation process.
        # Use read_only=False to avoid ReadOnlyWorksheet issues with defined_names
        excel_Workbook = openpyxl.load_workbook(FILE, data_only=True, read_only=False)
        excel_sheet = excel_Workbook.active
        column_names = [cell.value for cell in excel_sheet[1]]
        data_rows = [row for row in excel_sheet.iter_rows(min_row=2, values_only=True)]
        df = pd.DataFrame(data_rows, columns=column_names)
        data = df.to_dict(orient="records")

        missing_inflows = []
        for row in data:
            amount = row.get("Column5")
            session_id = row.get("Column7")
            if amount.isdigit():
                amount_payable = float(amount)
            else:
                amount_payable = amount
            if pd.isna(session_id):
                pass
            else:
                print(f"\nAMOUNT:   {amount_payable}\n")
                print(f"\nSESSION ID:   {session_id}\n")
                if amount_payable == 0.0:
                    pass
                else:
                    search_inflow = TransactionCallback.objects.filter(
                        session_id=session_id
                    ).first()
                    if search_inflow is not None:
                        pass
                    else:
                        data = {
                            "amount": amount_payable,
                            "session_id": session_id,
                        }
                        missing_inflows.append(data)
        print(f"\nMISSING INFLOWS: {missing_inflows}\n")
        print(f"\nCOUNT:    {len(missing_inflows)}\n")
        print("THE JOB IS DONE  !!!!!!!")
