from django.core.management.base import BaseCommand

from accounts.models import AccountDetail, TransactionDetail
from accounts.tasks import handle_inflow_commission
from helpers import enums
from wema_bank.models import VirtualAccount, TransactionCallback
from wema_bank.tasks import send_company_transaction_callbacks


class Command(BaseCommand):
    help = "MANUAL INFLOW UPDATE WITHOUT CONFIRMATION."

    def handle(self, *args, **kwargs):
        unconfirmed_transactions = TransactionCallback.objects.filter(
            status=enums.InflowStatus.UNCONFIRMED
        )
        for inflow_transaction in unconfirmed_transactions:
            session_id = inflow_transaction.session_id
            credit_account = inflow_transaction.recipient_account_number
            amount = float(inflow_transaction.amount)

            # Fetch virtual account details.
            account_details = VirtualAccount.get_account_details(
                account_number=credit_account
            )
            if account_details is None:
                pass
            # Identify the company's/sub-company's transaction details.
            company = account_details.company
            if account_details.sub_company is not None:
                sub_company = account_details.sub_company
                charges = float(sub_company.service_fee)
                account_type = enums.AccountType.SUB
            else:
                sub_company = None
                charges = float(company.service_fee)
                account_type = enums.AccountType.MAIN
            # Instantiate the transaction record.
            credit_transaction = TransactionDetail.register_transaction(
                one_time=account_details.one_time,
                request_reference=account_details.request_reference,
                company=company,
                beneficiary_account_number=credit_account,
                beneficiary_account_name=inflow_transaction.recipient_account_name,
                amount=amount,
                fee=charges,
                amount_payable=amount - charges,
                transaction_type=enums.TransactionType.CREDIT,
                transaction_status=enums.TransactionStatus.SUCCESSFUL,
                narration=inflow_transaction.narration,
                service_provider=enums.ServiceProvider.WEMA_BANK,
                mode=enums.APIMode.LIVE,
                session_id=session_id,
                bank_code=inflow_transaction.payer_bank_code,
                bank_name=inflow_transaction.payer_bank_name,
                source_account=inflow_transaction.payer_account_number,
                is_verified=True,
                sub_company=sub_company,
            )
            if not isinstance(credit_transaction, bool):
                inflow_transaction.is_resolved = True
                inflow_transaction.save()
                # Update the virtual account all time inflow balance.
                VirtualAccount.fund(
                    nuban=credit_account,
                    amount=amount,
                )
                # Update the associated wallet.
                wallet_transaction = AccountDetail.fund_account(
                    company=company,
                    account_type=account_type,
                    amount=amount,
                    charges=charges,
                    sub_company=sub_company,
                )
                if wallet_transaction.get("status"):
                    credit_transaction.balance_before = wallet_transaction.get(
                        "previous_balance"
                    )
                    credit_transaction.balance_after = wallet_transaction.get(
                        "account_balance"
                    )
                    credit_transaction.save()

                if charges > 0.0:
                    handle_inflow_commission.delay(transaction_id=credit_transaction.id)

                send_company_transaction_callbacks.delay(
                    transaction_id=credit_transaction.id,
                )
                if account_details.one_time:
                    account_details.request_active = False
                    account_details.save()
            else:
                inflow_transaction.is_resolved = True
                inflow_transaction.save()
        print(
            "THE JOB IS DONE      !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
        )
