from django.core.management.base import (
    <PERSON><PERSON>om<PERSON>,
    Command<PERSON>rror,
    CommandParser,
)

from user_profiles.models import Company
from wema_bank.helper.functions import generate_incremental_account_number
from wema_bank.models import VirtualAccount


class Command(BaseCommand):
    help = "CREATE MULTIPLE INSTANT/ONE_TIME VIRTUAL ACCOUNTS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("company_id")
        parser.add_argument("total_accounts")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        company_id = kwargs["company_id"]
        total_accounts = int(kwargs["total_accounts"])

        company = Company.objects.filter(id=company_id).first()
        if company is None:
            raise CommandError("INVALID COMPANY ID PROVIDED!")
        for _ in range(total_accounts):
            VirtualAccount.objects.create(
                company=company,
                first_name=company.name,
                last_name="PAYMENTS",
                account_number=generate_incremental_account_number(),
                one_time=True,
            )
        self.stdout.write(
            self.style.SUCCESS("COMPANY VIRTUAL ACCOUNTS CREATED SUCCESSFULLY.")
        )
