from django.core.management.base import (
    <PERSON><PERSON><PERSON><PERSON>,
    Command<PERSON><PERSON>r,
    Command<PERSON>arser,
)

from user_profiles.models import SubCompany
from wema_bank.helper.functions import (
    generate_incremental_account_number,
    generate_unique_code,
)
from wema_bank.models import VirtualAccount


class Command(BaseCommand):
    help = "UPDATE OFFLINE VIRTUAL ACCOUNTS."

    def handle(self, *args, **kwargs):
        accounts = VirtualAccount.objects.filter(
            offline=True,
            unique_code__isnull=True,
            confirmation_code__isnull=True,
        )
        for account in accounts:
            unique_code = generate_unique_code()
            unique_code_checker = VirtualAccount.objects.filter(
                unique_code=unique_code
            )
            confirmation_code = generate_unique_code()
            confirmation_code_checker = VirtualAccount.objects.filter(
                confirmation_code=confirmation_code
            )
            if unique_code_checker.exists() and confirmation_code_checker.exists():
                pass
            account.unique_code = unique_code
            account.confirmation_code = confirmation_code
            account.save()
        self.stdout.write(
            self.style.SUCCESS(
                "OFFLINE VIRTUAL ACCOUNTS SUCCESSFULLY UPDATED.")
        )
