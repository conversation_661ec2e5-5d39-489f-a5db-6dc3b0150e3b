from django.core.management.base import BaseCommand

from user_profiles.models import SubCompany
from wema_bank.models import VirtualAccount


class Command(BaseCommand):
    help = "UPDATE VIRTUAL ACCOUNTS WITH SUB COMPANY."

    def handle(self, *args, **kwargs):
        virtual_accounts = VirtualAccount.objects.filter(
            company="38be8635-d636-4948-af3a-811c160d5ffe"
        )
        for virtual_account in virtual_accounts:
            sub_company = SubCompany.objects.filter(
                nuban=virtual_account.account_number
            ).first()
            if sub_company is not None:
                virtual_account.sub_company = sub_company
                virtual_account.save()
