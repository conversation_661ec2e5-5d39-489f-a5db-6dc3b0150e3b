# EMQX MQTT Handler

This document provides information about the requirements and usage of the EMQXHandler class for MQTT communication.

## Overview

The EMQXHandler is a singleton class that provides a wrapper around the Paho MQTT client for connecting to an EMQX MQTT broker. It handles connection management, automatic reconnection, message publishing, and subscription to topics.

## Requirements

### Dependencies

- Python 3.x
- paho-mqtt library (`pip install paho-mqtt`)
- python-decouple (`pip install python-decouple`)

### Configuration

The following environment variables need to be set in your `.env` file:

```env
MQTT_BROKER=your_broker_address
MQTT_PORT=1883  # Default MQTT port, use 8883 for TLS
MQTT_USER=your_username
MQTT_PASS=your_password
```

## Step-by-Step Usage Guide

### 1. Basic Initialization

```python
from helpers.emqx_mqtt import EMQXHandler

# Create an instance (singleton pattern ensures only one instance exists)
mqtt_handler = EMQXHandler()
```

### 2. Custom Initialization (Optional)

```python
# Override default configuration
mqtt_handler = EMQXHandler(
    broker="custom-broker.example.com",
    port=8883,
    username="custom_user",
    password="custom_password",
    keepalive=120,  # Connection keepalive in seconds
    use_tls=True    # Enable TLS/SSL
)
```

### 3. Connect to the MQTT Broker

```python
# Connect to the broker
mqtt_handler.connect()
```

### 4. Start the Client Loop

```python
# Start the client loop in a background thread
mqtt_handler.start()
```

### 5. Subscribe to Topics

```python
# Subscribe to a topic with default QoS 0
mqtt_handler.subscribe("my/topic")

# Subscribe with specific QoS (0, 1, or 2)
mqtt_handler.subscribe("important/topic", qos=1)
```

### 6. Publish Messages

```python
# Publish a string message
mqtt_handler.publish("my/topic", "Hello, MQTT!")

# Publish a JSON message (automatically converted to JSON string)
mqtt_handler.publish(
    topic="account/events/12345",
    message={"subject": "Account Update", "body": "Your account has been updated"},
    qos=1,
    retain=False
)
```

### 7. Stopping the Client

```python
# Stop the client loop and disconnect
mqtt_handler.stop()
```

## Example: Sending Account Events

```python
import json
from helpers.emqx_mqtt import EMQXHandler

def send_account_notification(account_number, title, message):
    handler = EMQXHandler()

    with handler.session():
        payload = {
            "subject": title,
            "body": message,
        }
        success = handler.publish(
            topic=f"account/events/{account_number}",
            message=json.dumps(payload),
            qos=1
        )
        return success
```

## Using EMQXHandler in Celery Tasks

EMQXHandler can be easily integrated with Celery for asynchronous MQTT messaging. Here's an example of how it's used in the `mqtt_inflow_event` Celery task:

```python
from celery import shared_task
import json
from django.conf import settings
from helpers.emqx_mqtt import EMQXHandler

@shared_task
def mqtt_inflow_event(
    account_number: str,
    title: str,
    message: str,
):
    logger = settings.LOGGER

    try:
        handler = EMQXHandler()

        with handler.session():
            payload = {
                "subject": title,
                "body": message,
            }
            success = handler.publish(
                topic=f"account/events/{account_number}",
                message=json.dumps(payload),
                qos=1
            )
            if success:
                return f"Successfully published message to account/events/{account_number}"
            else:
                return f"Failed to publish message to account/events/{account_number}"
    except Exception as e:
        logger.error(f"Error in mqtt_inflow_event: {e}")
        return f"Error in mqtt_inflow_event: {e}"
```

### Calling the Celery Task

To trigger this task from anywhere in your application:

```python
from accounts.tasks import mqtt_inflow_event

# Call the task asynchronously
mqtt_inflow_event.delay(
    account_number="**********",
    title="Transaction Alert",
    message="Your account has been credited with $100"
)

# Or with more control over execution
mqtt_inflow_event.apply_async(
    args=["**********", "Transaction Alert", "Your account has been credited with $100"],
    queue="default",  # Specify a queue if needed
    countdown=10      # Delay execution by 10 seconds
)
```

## Advanced Features

- **Automatic Reconnection**: The handler automatically attempts to reconnect with exponential backoff if the connection is lost.
- **Singleton Pattern**: Only one instance of the handler is created, ensuring efficient resource usage.
- **TLS Support**: Enable secure connections with the `use_tls=True` parameter.
- **Context Manager**: Use the `session()` context manager for clean resource management.

## Best Practices

1. Use the context manager (`with handler.session()`) for short-lived operations.
2. For long-running applications, call `start()` once and `stop()` when shutting down.
3. Use appropriate QoS levels:
   - QoS 0: For non-critical messages where loss is acceptable
   - QoS 1: For messages that should be delivered at least once
   - QoS 2: For messages that must be delivered exactly once
4. Handle exceptions when publishing or subscribing to topics.
