from rest_framework import serializers

from vfd_bank.models import FinancialInstitution, AccountVerification


class FinancialInstitutionSerializer(serializers.ModelSerializer):

    class Meta:
        model = FinancialInstitution
        exclude = [
            "id",
            "created_at",
            "updated_at",
        ]


class AccountVerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountVerification
        exclude = [
            "id",
            "created_at",
            "updated_at",
        ]
