from django.db import models
from django.utils.translation import gettext as _
from django.core.cache import cache
from django.db.models import QuerySet

from vfd_bank.apis import VfdBankMgr


class FinancialInstitution(models.Model):
    code = models.CharField(max_length=20, unique=True, editable=False)
    name = models.CharField(max_length=255, editable=False)
    logo = models.TextField(blank=True, null=True)
    bank_code = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    def __str__(self):
        return self.name

    @classmethod
    def update_bank_list(cls):
        """
        Fetches the latest list of banks from VfdBankMgr and updates the database.

        This method retrieves the bank list from an external service, checks the response status,
        and updates or creates bank records in the database accordingly.

        Raises:
            Exception: If the API call fails or returns an error status.
        """

        # Fetch the bank list from the external API
        bank_list = VfdBankMgr.get_bank_list()

        # Check if the API response indicates success
        if bank_list.get("status") == "success":
            # Extract the list of banks from the response
            banks = bank_list["response"]["data"]["bank"]

            # Prepare a list for bulk creation
            bank_objects = []
            existing_codes = set(cls.objects.values_list("code", flat=True))

            # Iterate through each bank entry and prepare records for bulk creation
            for bank_data in banks:
                if bank_data["code"] not in existing_codes:
                    bank_objects.append(
                        cls(
                            code=bank_data[
                                "code"
                            ],  # Use 'code' as the unique identifier
                            name=bank_data["name"],  # Set bank name
                            logo=bank_data.get(
                                "logo", ""
                            ),  # Store logo (may be Base64, default empty string)
                            bank_code=bank_data.get(
                                "bank_code", ""
                            ),  # Store bank code (default empty string)
                        )
                    )

            # Bulk create the bank records if there are any new entries
            if bank_objects:
                # Try to retrieve the cached bank list
                # bank_list_cache = cache.delete("ngn_bank_list_cache")
                cls.objects.bulk_create(bank_objects)

            # Return all bank records after the update
            return cls.objects.all()

        else:
            # Raise an exception if the API request fails, providing the error message if available
            raise Exception(
                "Failed to fetch bank list: "
                + bank_list.get("message", "Unknown error")
            )

    @classmethod
    def fetch_and_cache_ngn_bank_list(cls) -> QuerySet:
        """
        This function retrieves the latest bank list, caches it for 6 hours,
        and returns the bank list. If the cached data is not available,
        it calls the update_bank_list method to fetch and create the data.
        """
        # Try to retrieve the cached bank list
        bank_list_cache = cache.get("ngn_bank_list_cache")
        if not bank_list_cache:
            bank_list = cls.update_bank_list()

            cache.set(key="ngn_bank_list_cache", value=bank_list, timeout=60 * 60 * 6)
            return bank_list
        else:
            return bank_list_cache


class AccountVerification(models.Model):
    account_number = models.CharField(max_length=30)
    nip_code = models.CharField(max_length=20)  # NIP code (bank code)
    cbn_code = models.CharField(
        max_length=20, blank=True, null=True
    )  # CBN code if available
    account_name = models.CharField(max_length=255)
    bank_name = models.CharField(max_length=255)
    is_valid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("account_number", "nip_code")

    def __str__(self):
        return f"{self.account_number} - {self.account_name} ({self.bank_name})"

    @classmethod
    def verify_account(cls, account_number, nip_code, cbn_code=None):
        """
        Verifies an account using VfdBankMgr.beneficiary_enquiry.
        Caches and saves the result if status is '00' (valid) or '104' (invalid).
        Returns a dictionary with status_code and message.
        """
        cache_key = f"account_verification_{account_number}_{nip_code}"
        cached = cache.get(cache_key)
        if cached:
            return {
                "status_code": 200,
                "message": "Account verification found in cache.",
                "data": {
                    "account_number": cached.account_number,
                    "nip_code": cached.nip_code,
                    "cbn_code": cached.cbn_code,
                    "account_name": cached.account_name,
                    "bank_name": cached.bank_name,
                    "is_valid": cached.is_valid,
                },
            }

        try:
            instance = cls.objects.get(account_number=account_number, nip_code=nip_code)
            cache.set(cache_key, instance, timeout=60 * 60 * 6)
            return {
                "status_code": 200,
                "message": "Account verification found in database.",
                "data": {
                    "account_number": instance.account_number,
                    "nip_code": instance.nip_code,
                    "cbn_code": instance.cbn_code,
                    "account_name": instance.account_name,
                    "bank_name": instance.bank_name,
                    "is_valid": instance.is_valid,
                },
            }
        except cls.DoesNotExist:
            pass

        response = VfdBankMgr.beneficiary_enquiry(account_number, nip_code)
       
        resp_data = response.get("response", {})
        status = resp_data.get("status")
        if status == "00":
            data = resp_data.get("data", {})
            instance = cls.objects.create(
                account_number=account_number,
                nip_code=nip_code,
                cbn_code=cbn_code,
                account_name=data.get("name", ""),
                bank_name=data.get("bank", ""),
                is_valid=True,
            )
            cache.set(cache_key, instance, timeout=60 * 60 * 6)
            return {
                "status_code": 200,
                "message": "Account verified successfully.",
                "data": {
                    "account_number": instance.account_number,
                    "nip_code": instance.nip_code,
                    "cbn_code": instance.cbn_code,
                    "account_name": instance.account_name,
                    "bank_name": instance.bank_name,
                    "is_valid": instance.is_valid,
                },
            }
        elif status == "104":
            # Invalid account, save as invalid
            instance = cls.objects.create(
                account_number=account_number,
                nip_code=nip_code,
                cbn_code=cbn_code,
                account_name="",
                bank_name="",
                is_valid=False,
            )
            cache.set(cache_key, instance, timeout=60 * 60 * 6)
            return {
                "status_code": 404,
                "message": "Account not found or invalid account details.",
                "data": {
                    "account_number": instance.account_number,
                    "nip_code": instance.nip_code,
                    "cbn_code": instance.cbn_code,
                    "account_name": instance.account_name,
                    "bank_name": instance.bank_name,
                    "is_valid": instance.is_valid,
                },
            }
        # For any other status, do not create or cache
        return {
            "status_code": 400,
            "message": "Could not fetch account name or unknown error occurred.",
            "data": None,
        }
