import hashlib
from dataclasses import dataclass
from datetime import datetime
import json

import requests
from django.conf import settings


@dataclass
class VfdBankMgr:
    """
    VfdBankMgr class provides an interface to interact with the VFD Bank API.

    This class is designed to handle various banking operations including:
    - Wallet creation
    - Beneficiary enquiry
    - Fund transfers (intra-bank and inter-bank)
    - Account enquiry
    - Transaction verification
    - BVN/NIN update

    It automatically handles the API environment (development or production)
    based on the settings and includes necessary authentication headers.
    """

    environment = settings.ENVIRONMENT
    if environment == "dev":
        base_url = "https://api-devapps.vfdbank.systems/vtech-wallet/api/v1.1/wallet2"
    else:
        base_url = "https://api-apps.vfdbank.systems/vtech-wallet/api/v1/wallet2"

    access_token = f"{settings.VFD_ACCESS_TOKEN}"
    headers = {"AccessToken": f"{access_token}", "accept": "application/json"}
    test_account_number = ""
    test_bank_code = "999999"

    @classmethod
    def format_api_response(cls, payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "success",
                "response": response.json(),
                "method": response.request.method,
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "status_code": response.status_code,
                "status": "failed",
                "method": response.request.method,
                "response": response.text,
                "payload": payload,
            }
        return response

    @classmethod
    def create_wallet(cls, dob, bvn, previousAccountNo=None):
        """
        Creates a new wallet for a client in the VFD Bank system.

        Args:
            dob (str): Date of birth in 'YYYY-MM-DD' format.
            bvn (str): Bank Verification Number (BVN) of the client.
            previousAccountNo (str, optional): Previous account number, if applicable.

        Returns:
            dict: The response from the VFD Bank API, processed by format_api_response.
        """
        filter_url = "/client/create"
        dateOfBirth = datetime.strptime(dob, "%Y-%m-%d").strftime("%d-%b-%Y")

        if not previousAccountNo:
            url = f"{cls.base_url}/client/create?dateOfBirth={dateOfBirth}&bvn={bvn}"
        else:
            url = f"{cls.base_url}/client/create?dateOfBirth={dateOfBirth}&bvn={bvn}&previousAccountNo={previousAccountNo}"

        response = requests.request("POST", url, headers=cls.headers)
        return cls.format_api_response(url=url, payload=None, response=response)

    @classmethod
    def get_transfer_type(cls, bank_code):
        """
        Determines the transfer type based on the bank code.

        Args:
            bank_code (str): The bank code to check.

        Returns:
            str: 'intra' if the transfer is within VFD Bank, 'inter' if it's to another bank.
        """
        if bank_code in ["090110", "999999"]:
            transfer_type = "intra"
        else:
            transfer_type = "inter"
        return transfer_type

    @classmethod
    def beneficiary_enquiry(cls, account_number, bank_code):
        """
        Enquires about a beneficiary's account details.

        Args:
            account_number (str): The beneficiary's account number.
            bank_code (str): The bank code for the beneficiary's bank.

        Returns:
            dict: The response from the VFD Bank API, processed by format_api_response.
        """
        url = f"{cls.base_url}/transfer/recipient?accountNo={account_number}&transfer_type={cls.get_transfer_type(bank_code)}&bank={bank_code}"
        response = requests.request("GET", url=url, headers=cls.headers)
        return cls.format_api_response(url=url, payload=None, response=response)

    @classmethod
    def vfd_account_enquiry(cls, account_number=None):
        """
        Enquires about an account within VFD Bank.

        Args:
            account_number (str, optional): The account number to enquire about.

        Returns:
            dict: The response from the VFD Bank API, processed by format_api_response.
        """
        url = f"{cls.base_url}/account/enquiry"
        response = requests.request("GET", url=url, headers=cls.headers)
        return cls.format_api_response(url=url, payload=None, response=response)

    @classmethod
    def get_float_balance(cls):
        """
        Gets the current float balance from VFD Bank account.

        Returns:
            float or None: The account balance if successful, None if the request fails
        """
        account_enquiry = cls.vfd_account_enquiry()
        if account_enquiry.get("status") == "success":
            response_data = account_enquiry.get("response", {}).get("data", {})
            return response_data.get("accountBalance")
        return None

    @classmethod
    def get_fromClient_toClient_data(cls, account_number, bank_code):
        """
        Retrieves data required for client-to-client fund transfers.

        Args:
            account_number (str): The beneficiary's account number.
            bank_code (str): The bank code for the beneficiary's bank.

        Returns:
            dict: A dictionary containing the necessary data for the transfer.
        """
        payout_data = None
        recipient_verification_response = cls.beneficiary_enquiry(
            account_number=(
                account_number
                if cls.environment == "production"
                else cls.test_account_number
            ),
            bank_code=(
                bank_code if cls.environment == "production" else cls.test_bank_code
            ),
        )
        verification_response = recipient_verification_response.get("response")
        if (
            recipient_verification_response.get("status") == "success"
            and verification_response.get("status") == "00"
        ):
            response_data = verification_response.get("data")
            payout_data = {
                "toClient": response_data["name"],
                "toAccount": account_number,
                "toBank": bank_code,
                "toBvn": response_data["bvn"],
                "toClientId": response_data["clientId"],
                "toSavingsId": response_data["account"]["id"],
            }
            # get admin account details
            admin_account_details = cls.vfd_account_enquiry()
            account_detail_response = admin_account_details.get("response")
            account_data = account_detail_response.get("data")
            payout_data.update(
                {
                    "fromSavingsId": account_data["accountId"],
                    "fromClient": account_data["client"],
                    "fromClientId": account_data["clientId"],
                    "fromAccount": account_data["accountNo"],
                }
            )
        final_result = {
            "payout_data": payout_data,
            "enquiry_result": recipient_verification_response,
        }
        return final_result

    @classmethod
    def initiate_payout(
        cls, beneficiary_nuban, beneficiary_bank_code, narration, amount, reference
    ):
        """
        Initiates a fund transfer (payout) to a beneficiary.

        Args:
            beneficiary_nuban (str): The beneficiary's account number (NUBAN).
            beneficiary_bank_code (str): The beneficiary's bank code.
            narration (str): The transfer narration.
            amount (float): The amount to transfer.
            reference (str): A unique reference for the transaction.

        Returns:
            dict: The response from the VFD Bank API, processed by format_api_response,
                  or an error message if the account details are invalid.
        """
        url = f"{cls.base_url}/transfer"
        payout_data = cls.get_fromClient_toClient_data(
            account_number=beneficiary_nuban, bank_code=beneficiary_bank_code
        )

        if payout_data.get("payout_data") is not None:
            client_credentials = payout_data.get("payout_data")
            fromAccount = client_credentials.get("fromAccount")
            toSavingsId = client_credentials.get("toSavingsId")

            get_signature = f"{fromAccount}{beneficiary_nuban}"
            signature_code = hashlib.sha512(
                str(get_signature).encode("utf-8")
            ).hexdigest()

            transferType = cls.get_transfer_type(bank_code=beneficiary_bank_code)

            payload = {
                "amount": float(amount),
                "signature": signature_code,
                "remark": narration,
                "toKyc": "99",
                "reference": reference,
                "toSession": toSavingsId,
                "transferType": transferType,
            }
            payload.update(client_credentials)

            response = requests.request(
                "POST", url=url, headers=cls.headers, json=payload
            )
            return cls.format_api_response(url=url, payload=payload, response=response)
        else:
            account_fetch_error = {
                "error": "Invalid account details",
                "payout_data": payout_data,
            }
            return account_fetch_error

    @classmethod
    def vfd_transaction_verification_handler(cls, reference):
        """
        Verifies a transaction using its unique reference.

        Args:
            reference (str): The unique reference for the transaction.

        Returns:
            dict: The response from the VFD Bank API, processed by format_api_response.
        """
        url = f"{cls.base_url}/transactions?reference={reference}"
        response = requests.request("GET", url=url, headers=cls.headers)
        return cls.format_api_response(url=url, payload=None, response=response)

    @classmethod
    def update_bvn_nin(cls, account_number, bvn, nin: str = None, dob=None):
        """
        Updates a client's BVN or NIN in the VFD Bank system.

        Args:
            account_number (str): The client's account number.
            bvn (str): The Bank Verification Number (BVN) of the client.
            nin (str, optional): The National Identification Number (NIN) of the client.
            dob (str, optional): The client's date of birth, if updating with BVN.

        Returns:
            dict: The response from the VFD Bank API, processed by format_api_response.
        """
        url = f"{cls.base_url}/client/upgrade"

        if not dob:
            payload = {"accountNo": account_number, "action": "Update-BVN"}
        else:
            payload = {
                "accountNo": account_number,
                "dob": dob,
                "action": "Recomply-With-BVN",
            }

        if nin:
            payload["nin"] = nin
        else:
            payload["bvn"] = bvn

        response = requests.request("POST", url=url, headers=cls.headers, json=payload)
        return cls.format_api_response(url=url, payload=payload, response=response)

    @classmethod
    def get_bank_list(cls):
        """
        Fetches the list of banks from the specified endpoint.

        This class method sends a GET request to the /bank endpoint of the base URL to
        retrieve a list of banks. It uses the predefined base URL and headers for the
        request and handles the response with a custom request result handler.

        Returns:
            dict: A dictionary containing the response data from the request result handler.

        Raises:
            requests.exceptions.RequestException: If the request fails due to network issues
            or invalid URL.

        Example:
            >>> bank_list = YourClassName.get_bank_list()
            >>> print(bank_list)
        """

        url = f"{cls.base_url}/bank"
        response = requests.request("GET", url=url, headers=cls.headers)
        result = cls.format_api_response(url=url, payload=None, response=response)
        return result
