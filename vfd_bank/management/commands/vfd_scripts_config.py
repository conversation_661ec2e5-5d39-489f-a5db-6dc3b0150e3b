from django.core.management import BaseCommand

from vfd_bank.apis import VfdBankMgr
from vfd_bank.models import FinancialInstitution
from django.core.cache import cache


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        i = FinancialInstitution.fetch_and_cache_ngn_bank_list()

        cache.set(
            key="ngn_bank_list_cache", value={"name": "bank name"}, timeout=60 * 30
        )
        # print(i)
        pass
