from django.shortcuts import render

from helpers.custom_responses import Response
from rest_framework.views import APIView

from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from rest_framework import permissions

from vfd_bank.models import FinancialInstitution, AccountVerification


from helpers.reusable import Paginator  # Ensure Paginator is imported


from vfd_bank.serializers import (
    FinancialInstitutionSerializer,
    AccountVerificationSerializer,
)

from helpers.custom_permissions import AccountVerificationKeyPermission


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"


class FetchBanksView(APIView):
    # serializer_class = FinancialInstitutionSerializer

    def get(self, request):
        name = request.GET.get("name")

        if not name:
            bank_query_set = (
                FinancialInstitution.fetch_and_cache_ngn_bank_list()
                .order_by("name")
                .values("code", "name", "logo", "bank_code")
            )
        else:
            bank_query_set = (
                FinancialInstitution.fetch_and_cache_ngn_bank_list()
                .filter(name__icontains=name)
                .order_by("name")
                .values("code", "name", "logo", "bank_code")
            )

        # paginated_data = Paginator.paginate(
        #         request=request, queryset=bank_query_set
        #     )
        # serializer = self.serializer_class(
        #     instance=paginated_data, many=True)
        # data = {
        #     "message": "successfully fetched company account details.",
        #     "account_details": serializer.data,
        # }
        # print(data)

        paginator = PageNumberPagination()
        paginator.page_size = 30
        paginated_queryset = paginator.paginate_queryset(bank_query_set, request)

        return paginator.get_paginated_response(
            {"message": "Successfully fetched banks", "data": paginated_queryset}
        )


class AccountVerificationView(APIView):
    permission_classes = [AccountVerificationKeyPermission]

    def post(self, request):
        account_number = request.data.get("account_number")
        nip_code = request.data.get("nip_code")
        cbn_code = request.data.get("cbn_code")
        if not account_number or not nip_code:
            return Response(
                data=None,
                errors={"detail": "account_number and nip_code are required."},
                status_code=400,
            )
        result = AccountVerification.verify_account(account_number, nip_code, cbn_code)
        return Response(
            data=result.get("data"),
            errors=(
                None if result["status_code"] == 200 else {"detail": result["message"]}
            ),
            status_code=result["status_code"],
        )
